"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityManager = void 0;
const crypto_1 = __importDefault(require("crypto"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class SecurityManager {
    constructor() {
        this.securityEvents = new Map();
        this.playerProfiles = new Map();
        this.antiCheatDetections = new Map();
        this.rateLimits = new Map(); // endpoint -> ip -> timestamps
        this.blockedIPs = new Set();
        this.suspiciousIPs = new Map();
        this.jwtSecret = process.env.JWT_SECRET || 'default-secret-change-in-production';
        this.initializeSecurityRules();
        this.startSecurityMonitoring();
    }
    /**
     * Проверяет безопасность запроса
     */
    validateRequest(playerId, ipAddress, userAgent, endpoint, data) {
        // Проверяем заблокированные IP
        if (this.blockedIPs.has(ipAddress)) {
            return { allowed: false, reason: 'IP address is blocked' };
        }
        // Проверяем rate limiting
        const rateLimitResult = this.checkRateLimit(endpoint, ipAddress);
        if (!rateLimitResult.allowed) {
            return rateLimitResult;
        }
        // Проверяем профиль игрока
        const playerProfile = this.getPlayerSecurityProfile(playerId);
        if (playerProfile.trustLevel === 'banned') {
            return { allowed: false, reason: 'Player is banned' };
        }
        // Проверяем подозрительную активность
        const suspiciousActivity = this.detectSuspiciousActivity(playerId, ipAddress, userAgent, endpoint, data);
        if (suspiciousActivity.detected) {
            return {
                allowed: suspiciousActivity.severity !== 'critical',
                reason: suspiciousActivity.reason,
                actions: suspiciousActivity.actions
            };
        }
        return { allowed: true };
    }
    /**
     * Создает JWT токен
     */
    createJWTToken(playerId, playerName, expiresIn = '24h') {
        const payload = {
            playerId,
            playerName,
            iat: Math.floor(Date.now() / 1000)
        };
        return jsonwebtoken_1.default.sign(payload, this.jwtSecret, { expiresIn });
    }
    /**
     * Проверяет JWT токен
     */
    verifyJWTToken(token) {
        try {
            const payload = jsonwebtoken_1.default.verify(token, this.jwtSecret);
            return { valid: true, payload };
        }
        catch (error) {
            return { valid: false, error: error.message };
        }
    }
    /**
     * Детектирует читерство в игре
     */
    detectCheating(playerId, gameId, gameData) {
        const detections = [];
        // Проверка скорости действий
        const speedCheck = this.checkActionSpeed(playerId, gameData);
        if (speedCheck)
            detections.push(speedCheck);
        // Проверка невозможных ходов
        const moveCheck = this.checkImpossibleMoves(playerId, gameId, gameData);
        if (moveCheck)
            detections.push(moveCheck);
        // Статистический анализ
        const statCheck = this.checkStatisticalAnomalies(playerId, gameData);
        if (statCheck)
            detections.push(statCheck);
        // Сохраняем детекции
        if (detections.length > 0) {
            if (!this.antiCheatDetections.has(playerId)) {
                this.antiCheatDetections.set(playerId, []);
            }
            this.antiCheatDetections.get(playerId).push(...detections);
            // Возвращаем самую серьезную детекцию
            return detections.reduce((max, current) => current.confidence > max.confidence ? current : max);
        }
        return null;
    }
    /**
     * Блокирует IP адрес
     */
    blockIP(ipAddress, reason, duration) {
        this.blockedIPs.add(ipAddress);
        const securityEvent = {
            id: this.generateSecurityEventId(),
            type: 'unauthorized_access',
            severity: 'high',
            ipAddress,
            description: `IP blocked: ${reason}`,
            evidence: { reason, duration },
            timestamp: new Date(),
            status: 'detected',
            actions: [{
                    type: 'ip_block',
                    duration,
                    reason,
                    timestamp: new Date(),
                    executedBy: 'system'
                }]
        };
        this.securityEvents.set(securityEvent.id, securityEvent);
        // Автоматическая разблокировка
        if (duration) {
            setTimeout(() => {
                this.unblockIP(ipAddress);
            }, duration * 60 * 1000);
        }
    }
    /**
     * Разблокирует IP адрес
     */
    unblockIP(ipAddress) {
        this.blockedIPs.delete(ipAddress);
    }
    /**
     * Банит игрока
     */
    banPlayer(playerId, reason, duration, bannedBy = 'system') {
        const profile = this.getPlayerSecurityProfile(playerId);
        profile.trustLevel = 'banned';
        const action = {
            type: 'ban',
            duration,
            reason,
            timestamp: new Date(),
            executedBy: bannedBy
        };
        const securityEvent = {
            id: this.generateSecurityEventId(),
            type: 'cheating_attempt',
            severity: 'critical',
            playerId,
            ipAddress: '',
            description: `Player banned: ${reason}`,
            evidence: { reason, duration },
            timestamp: new Date(),
            status: 'resolved',
            actions: [action]
        };
        this.securityEvents.set(securityEvent.id, securityEvent);
        // Автоматическая разблокировка
        if (duration) {
            setTimeout(() => {
                this.unbanPlayer(playerId);
            }, duration * 60 * 1000);
        }
    }
    /**
     * Разбанивает игрока
     */
    unbanPlayer(playerId) {
        const profile = this.getPlayerSecurityProfile(playerId);
        profile.trustLevel = 'suspicious'; // Не сразу доверенный
    }
    /**
     * Получает профиль безопасности игрока
     */
    getPlayerSecurityProfile(playerId) {
        let profile = this.playerProfiles.get(playerId);
        if (!profile) {
            profile = {
                playerId,
                riskScore: 0.1,
                trustLevel: 'new',
                securityEvents: [],
                lastSecurityCheck: new Date(),
                behaviorPatterns: {
                    averageActionsPerMinute: 0,
                    typicalPlayHours: [],
                    deviceFingerprints: [],
                    ipAddresses: [],
                    suspiciousPatterns: []
                },
                restrictions: {
                    chatBanned: false,
                    tradingBanned: false,
                    tournamentBanned: false,
                    rateLimited: false
                }
            };
            this.playerProfiles.set(playerId, profile);
        }
        return profile;
    }
    /**
     * Обновляет поведенческие паттерны игрока
     */
    updatePlayerBehavior(playerId, ipAddress, userAgent, actionType) {
        const profile = this.getPlayerSecurityProfile(playerId);
        // Обновляем IP адреса
        if (!profile.behaviorPatterns.ipAddresses.includes(ipAddress)) {
            profile.behaviorPatterns.ipAddresses.push(ipAddress);
        }
        // Создаем отпечаток устройства
        const deviceFingerprint = this.createDeviceFingerprint(userAgent, ipAddress);
        if (!profile.behaviorPatterns.deviceFingerprints.includes(deviceFingerprint)) {
            profile.behaviorPatterns.deviceFingerprints.push(deviceFingerprint);
        }
        // Обновляем время активности
        const currentHour = new Date().getHours();
        if (!profile.behaviorPatterns.typicalPlayHours.includes(currentHour)) {
            profile.behaviorPatterns.typicalPlayHours.push(currentHour);
        }
        profile.lastSecurityCheck = new Date();
    }
    /**
     * Получает статистику безопасности
     */
    getSecurityStats() {
        const totalEvents = this.securityEvents.size;
        const eventsBySeverity = Array.from(this.securityEvents.values()).reduce((acc, event) => {
            acc[event.severity] = (acc[event.severity] || 0) + 1;
            return acc;
        }, {});
        const playersByTrustLevel = Array.from(this.playerProfiles.values()).reduce((acc, profile) => {
            acc[profile.trustLevel] = (acc[profile.trustLevel] || 0) + 1;
            return acc;
        }, {});
        return {
            totalSecurityEvents: totalEvents,
            eventsBySeverity,
            playersByTrustLevel,
            blockedIPs: this.blockedIPs.size,
            suspiciousIPs: this.suspiciousIPs.size,
            antiCheatDetections: Array.from(this.antiCheatDetections.values()).flat().length
        };
    }
    /**
     * Экспортирует отчет по безопасности
     */
    generateSecurityReport(timeRange) {
        const events = Array.from(this.securityEvents.values())
            .filter(event => event.timestamp > timeRange);
        return {
            summary: {
                totalEvents: events.length,
                criticalEvents: events.filter(e => e.severity === 'critical').length,
                resolvedEvents: events.filter(e => e.status === 'resolved').length
            },
            topThreats: this.getTopThreats(events),
            affectedPlayers: this.getAffectedPlayers(events),
            recommendations: this.generateSecurityRecommendations(events)
        };
    }
    // Приватные методы
    initializeSecurityRules() {
        // Инициализация правил безопасности
        this.setupRateLimits();
        this.loadThreatIntelligence();
    }
    setupRateLimits() {
        const rateLimitConfigs = [
            { endpoint: '/api/auth/login', maxRequests: 5, windowMs: 15 * 60 * 1000 }, // 5 попыток за 15 минут
            { endpoint: '/api/game/create', maxRequests: 10, windowMs: 60 * 1000 }, // 10 игр в минуту
            { endpoint: '/api/chat/send', maxRequests: 30, windowMs: 60 * 1000 }, // 30 сообщений в минуту
            { endpoint: '/api/purchase', maxRequests: 5, windowMs: 60 * 1000 } // 5 покупок в минуту
        ];
        // Сохраняем конфигурации rate limiting
    }
    loadThreatIntelligence() {
        // Загрузка данных о известных угрозах
        // В реальном приложении это может быть загрузка из внешних источников
    }
    startSecurityMonitoring() {
        // Запуск фонового мониторинга безопасности
        setInterval(() => {
            this.performSecurityScan();
        }, 5 * 60 * 1000); // Каждые 5 минут
    }
    performSecurityScan() {
        // Сканирование на предмет подозрительной активности
        this.scanForAnomalies();
        this.updateRiskScores();
        this.cleanupOldData();
    }
    checkRateLimit(endpoint, ipAddress) {
        if (!this.rateLimits.has(endpoint)) {
            this.rateLimits.set(endpoint, new Map());
        }
        const endpointLimits = this.rateLimits.get(endpoint);
        const now = Date.now();
        const windowMs = 60 * 1000; // 1 минута
        const maxRequests = this.getMaxRequestsForEndpoint(endpoint);
        if (!endpointLimits.has(ipAddress)) {
            endpointLimits.set(ipAddress, []);
        }
        const requests = endpointLimits.get(ipAddress);
        // Удаляем старые запросы
        const validRequests = requests.filter(timestamp => now - timestamp < windowMs);
        endpointLimits.set(ipAddress, validRequests);
        if (validRequests.length >= maxRequests) {
            return { allowed: false, reason: 'Rate limit exceeded' };
        }
        // Добавляем текущий запрос
        validRequests.push(now);
        return { allowed: true };
    }
    detectSuspiciousActivity(playerId, ipAddress, userAgent, endpoint, data) {
        const profile = this.getPlayerSecurityProfile(playerId);
        // Проверяем новое устройство/IP
        if (!profile.behaviorPatterns.ipAddresses.includes(ipAddress)) {
            const ipInfo = this.analyzeIP(ipAddress);
            if (ipInfo.riskScore > 0.7) {
                return {
                    detected: true,
                    severity: 'high',
                    reason: 'High-risk IP address detected',
                    actions: [{
                            type: 'manual_review',
                            reason: 'New high-risk IP',
                            timestamp: new Date(),
                            executedBy: 'system'
                        }]
                };
            }
        }
        // Проверяем частоту действий
        const actionFrequency = this.calculateActionFrequency(playerId);
        if (actionFrequency > 100) { // Более 100 действий в минуту
            return {
                detected: true,
                severity: 'medium',
                reason: 'Unusually high action frequency',
                actions: [{
                        type: 'rate_limit',
                        duration: 10,
                        reason: 'High action frequency',
                        timestamp: new Date(),
                        executedBy: 'system'
                    }]
            };
        }
        return { detected: false };
    }
    checkActionSpeed(playerId, gameData) {
        const expectedMinTime = 1000; // Минимум 1 секунда на действие
        const actualTime = gameData.actionTime || 0;
        if (actualTime < expectedMinTime) {
            return {
                playerId,
                gameId: gameData.gameId,
                detectionType: 'speed_hack',
                confidence: 0.8,
                evidence: {
                    expectedTime: expectedMinTime,
                    actualTime
                },
                timestamp: new Date()
            };
        }
        return null;
    }
    checkImpossibleMoves(playerId, gameId, gameData) {
        // Логика проверки невозможных ходов
        // Зависит от конкретной игры
        return null;
    }
    checkStatisticalAnomalies(playerId, gameData) {
        // Статистический анализ игровых данных
        const winRate = gameData.winRate || 0;
        if (winRate > 0.95 && gameData.gamesPlayed > 100) {
            return {
                playerId,
                gameId: gameData.gameId,
                detectionType: 'statistical_anomaly',
                confidence: 0.9,
                evidence: {
                    expectedTime: 0,
                    actualTime: 0,
                    statisticalData: { winRate, gamesPlayed: gameData.gamesPlayed }
                },
                timestamp: new Date()
            };
        }
        return null;
    }
    createDeviceFingerprint(userAgent, ipAddress) {
        return crypto_1.default.createHash('sha256')
            .update(userAgent + ipAddress)
            .digest('hex')
            .substring(0, 16);
    }
    analyzeIP(ipAddress) {
        // В реальном приложении здесь была бы интеграция с сервисами проверки IP
        return {
            ipAddress,
            country: 'Unknown',
            isVPN: false,
            isTor: false,
            isProxy: false,
            riskScore: Math.random() * 0.3 // Низкий риск по умолчанию
        };
    }
    calculateActionFrequency(playerId) {
        // Расчет частоты действий игрока
        return Math.random() * 50; // Заглушка
    }
    getMaxRequestsForEndpoint(endpoint) {
        const limits = {
            '/api/auth/login': 5,
            '/api/game/create': 10,
            '/api/chat/send': 30,
            '/api/purchase': 5
        };
        return limits[endpoint] || 60; // По умолчанию 60 запросов в минуту
    }
    scanForAnomalies() {
        // Сканирование аномалий в поведении игроков
    }
    updateRiskScores() {
        // Обновление оценок риска для игроков
        for (const profile of this.playerProfiles.values()) {
            profile.riskScore = this.calculateRiskScore(profile);
        }
    }
    calculateRiskScore(profile) {
        let score = 0.1; // Базовый риск
        // Увеличиваем риск за события безопасности
        score += profile.securityEvents.length * 0.1;
        // Увеличиваем риск за множественные IP
        if (profile.behaviorPatterns.ipAddresses.length > 5) {
            score += 0.2;
        }
        // Уменьшаем риск для доверенных игроков
        if (profile.trustLevel === 'trusted') {
            score *= 0.5;
        }
        return Math.min(1, score);
    }
    cleanupOldData() {
        const cutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 дней
        // Очищаем старые события безопасности
        for (const [id, event] of this.securityEvents.entries()) {
            if (event.timestamp < cutoff) {
                this.securityEvents.delete(id);
            }
        }
    }
    getTopThreats(events) {
        const threatCounts = events.reduce((acc, event) => {
            acc[event.type] = (acc[event.type] || 0) + 1;
            return acc;
        }, {});
        return Object.entries(threatCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([threat, count]) => ({ threat, count }));
    }
    getAffectedPlayers(events) {
        return [...new Set(events.filter(e => e.playerId).map(e => e.playerId))];
    }
    generateSecurityRecommendations(events) {
        const recommendations = [];
        if (events.filter(e => e.type === 'ddos_attack').length > 5) {
            recommendations.push('Consider implementing DDoS protection');
        }
        if (events.filter(e => e.type === 'cheating_attempt').length > 10) {
            recommendations.push('Strengthen anti-cheat measures');
        }
        return recommendations;
    }
    generateSecurityEventId() {
        return 'sec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
exports.SecurityManager = SecurityManager;
//# sourceMappingURL=SecurityManager.js.map