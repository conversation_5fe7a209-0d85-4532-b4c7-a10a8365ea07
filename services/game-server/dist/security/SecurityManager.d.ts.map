{"version": 3, "file": "SecurityManager.d.ts", "sourceRoot": "", "sources": ["../../src/security/SecurityManager.ts"], "names": [], "mappings": "AAGA,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,qBAAqB,GAAG,kBAAkB,GAAG,aAAa,GAAG,qBAAqB,GAAG,aAAa,CAAC;IACzG,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE,UAAU,GAAG,eAAe,GAAG,UAAU,GAAG,gBAAgB,CAAC;IACrE,OAAO,EAAE,cAAc,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,KAAK,GAAG,SAAS,GAAG,YAAY,GAAG,UAAU,GAAG,iBAAiB,GAAG,eAAe,CAAC;IAC1F,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,UAAU,EAAE,QAAQ,GAAG,OAAO,CAAC;CAChC;AAED,MAAM,WAAW,qBAAqB;IACpC,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,KAAK,GAAG,SAAS,GAAG,UAAU,GAAG,YAAY,GAAG,QAAQ,CAAC;IACrE,cAAc,EAAE,MAAM,EAAE,CAAC;IACzB,iBAAiB,EAAE,IAAI,CAAC;IACxB,gBAAgB,EAAE;QAChB,uBAAuB,EAAE,MAAM,CAAC;QAChC,gBAAgB,EAAE,MAAM,EAAE,CAAC;QAC3B,kBAAkB,EAAE,MAAM,EAAE,CAAC;QAC7B,WAAW,EAAE,MAAM,EAAE,CAAC;QACtB,kBAAkB,EAAE,MAAM,EAAE,CAAC;KAC9B,CAAC;IACF,YAAY,EAAE;QACZ,UAAU,EAAE,OAAO,CAAC;QACpB,aAAa,EAAE,OAAO,CAAC;QACvB,gBAAgB,EAAE,OAAO,CAAC;QAC1B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;CACH;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE,YAAY,GAAG,kBAAkB,GAAG,qBAAqB,GAAG,qBAAqB,CAAC;IACjG,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE;QACR,YAAY,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;QACxB,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1C,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,eAAe;IAC9B,QAAQ,EAAE,MAAM,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,qBAAa,eAAe;IAC1B,OAAO,CAAC,cAAc,CAAyC;IAC/D,OAAO,CAAC,cAAc,CAAiD;IACvE,OAAO,CAAC,mBAAmB,CAAgD;IAC3E,OAAO,CAAC,UAAU,CAAiD;IACnE,OAAO,CAAC,UAAU,CAA0B;IAC5C,OAAO,CAAC,aAAa,CAA0C;IAC/D,OAAO,CAAC,SAAS,CAA2E;;IAO5F;;OAEG;IACH,eAAe,CACb,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,EAChB,IAAI,CAAC,EAAE,GAAG,GACT;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,cAAc,EAAE,CAAA;KAAE;IA+BpE;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,GAAE,MAAc,GAAG,MAAM;IAUvF;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,GAAG;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,OAAO,CAAC,EAAE,GAAG,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE;IAShF;;OAEG;IACH,cAAc,CACZ,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,GAAG,GACZ,kBAAkB,GAAG,IAAI;IA+B5B;;OAEG;IACH,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IA+BnE;;OAEG;IACH,SAAS,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAIlC;;OAEG;IACH,SAAS,CACP,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,QAAQ,CAAC,EAAE,MAAM,EACjB,QAAQ,GAAE,QAAQ,GAAG,OAAkB,GACtC,IAAI;IAmCP;;OAEG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAKnC;;OAEG;IACH,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,qBAAqB;IA+BjE;;OAEG;IACH,oBAAoB,CAClB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,UAAU,EAAE,MAAM,GACjB,IAAI;IAuBP;;OAEG;IACH,gBAAgB,IAAI,GAAG;IAsBvB;;OAEG;IACH,sBAAsB,CAAC,SAAS,EAAE,IAAI,GAAG,GAAG;IAiB5C,OAAO,CAAC,uBAAuB;IAM/B,OAAO,CAAC,eAAe;IAWvB,OAAO,CAAC,sBAAsB;IAK9B,OAAO,CAAC,uBAAuB;IAO/B,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,cAAc;IA8BtB,OAAO,CAAC,wBAAwB;IA+ChC,OAAO,CAAC,gBAAgB;IAqBxB,OAAO,CAAC,oBAAoB;IAM5B,OAAO,CAAC,yBAAyB;IAsBjC,OAAO,CAAC,uBAAuB;IAO/B,OAAO,CAAC,SAAS;IAYjB,OAAO,CAAC,wBAAwB;IAKhC,OAAO,CAAC,yBAAyB;IAWjC,OAAO,CAAC,gBAAgB;IAIxB,OAAO,CAAC,gBAAgB;IAOxB,OAAO,CAAC,kBAAkB;IAmB1B,OAAO,CAAC,cAAc;IAWtB,OAAO,CAAC,aAAa;IAYrB,OAAO,CAAC,kBAAkB;IAI1B,OAAO,CAAC,+BAA+B;IAcvC,OAAO,CAAC,uBAAuB;CAGhC"}