export interface SecurityEvent {
    id: string;
    type: 'suspicious_activity' | 'cheating_attempt' | 'ddos_attack' | 'unauthorized_access' | 'data_breach';
    severity: 'low' | 'medium' | 'high' | 'critical';
    playerId?: string;
    ipAddress: string;
    userAgent?: string;
    description: string;
    evidence: Record<string, any>;
    timestamp: Date;
    status: 'detected' | 'investigating' | 'resolved' | 'false_positive';
    actions: SecurityAction[];
}
export interface SecurityAction {
    type: 'ban' | 'warning' | 'rate_limit' | 'ip_block' | 'account_suspend' | 'manual_review';
    duration?: number;
    reason: string;
    timestamp: Date;
    executedBy: 'system' | 'admin';
}
export interface PlayerSecurityProfile {
    playerId: string;
    riskScore: number;
    trustLevel: 'new' | 'trusted' | 'verified' | 'suspicious' | 'banned';
    securityEvents: string[];
    lastSecurityCheck: Date;
    behaviorPatterns: {
        averageActionsPerMinute: number;
        typicalPlayHours: number[];
        deviceFingerprints: string[];
        ipAddresses: string[];
        suspiciousPatterns: string[];
    };
    restrictions: {
        chatBanned: boolean;
        tradingBanned: boolean;
        tournamentBanned: boolean;
        rateLimited: boolean;
    };
}
export interface AntiCheatDetection {
    playerId: string;
    gameId: string;
    detectionType: 'speed_hack' | 'impossible_moves' | 'pattern_recognition' | 'statistical_anomaly';
    confidence: number;
    evidence: {
        expectedTime: number;
        actualTime: number;
        moveSequence?: string[];
        statisticalData?: Record<string, number>;
    };
    timestamp: Date;
}
export interface RateLimitConfig {
    endpoint: string;
    maxRequests: number;
    windowMs: number;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
}
export interface IPSecurityInfo {
    ipAddress: string;
    country: string;
    isVPN: boolean;
    isTor: boolean;
    isProxy: boolean;
    riskScore: number;
    blockedAt?: Date;
    blockReason?: string;
}
export declare class SecurityManager {
    private securityEvents;
    private playerProfiles;
    private antiCheatDetections;
    private rateLimits;
    private blockedIPs;
    private suspiciousIPs;
    private jwtSecret;
    constructor();
    /**
     * Проверяет безопасность запроса
     */
    validateRequest(playerId: string, ipAddress: string, userAgent: string, endpoint: string, data?: any): {
        allowed: boolean;
        reason?: string;
        actions?: SecurityAction[];
    };
    /**
     * Создает JWT токен
     */
    createJWTToken(playerId: string, playerName: string, expiresIn?: string): string;
    /**
     * Проверяет JWT токен
     */
    verifyJWTToken(token: string): {
        valid: boolean;
        payload?: any;
        error?: string;
    };
    /**
     * Детектирует читерство в игре
     */
    detectCheating(playerId: string, gameId: string, gameData: any): AntiCheatDetection | null;
    /**
     * Блокирует IP адрес
     */
    blockIP(ipAddress: string, reason: string, duration?: number): void;
    /**
     * Разблокирует IP адрес
     */
    unblockIP(ipAddress: string): void;
    /**
     * Банит игрока
     */
    banPlayer(playerId: string, reason: string, duration?: number, bannedBy?: 'system' | 'admin'): void;
    /**
     * Разбанивает игрока
     */
    unbanPlayer(playerId: string): void;
    /**
     * Получает профиль безопасности игрока
     */
    getPlayerSecurityProfile(playerId: string): PlayerSecurityProfile;
    /**
     * Обновляет поведенческие паттерны игрока
     */
    updatePlayerBehavior(playerId: string, ipAddress: string, userAgent: string, actionType: string): void;
    /**
     * Получает статистику безопасности
     */
    getSecurityStats(): any;
    /**
     * Экспортирует отчет по безопасности
     */
    generateSecurityReport(timeRange: Date): any;
    private initializeSecurityRules;
    private setupRateLimits;
    private loadThreatIntelligence;
    private startSecurityMonitoring;
    private performSecurityScan;
    private checkRateLimit;
    private detectSuspiciousActivity;
    private checkActionSpeed;
    private checkImpossibleMoves;
    private checkStatisticalAnomalies;
    private createDeviceFingerprint;
    private analyzeIP;
    private calculateActionFrequency;
    private getMaxRequestsForEndpoint;
    private scanForAnomalies;
    private updateRiskScores;
    private calculateRiskScore;
    private cleanupOldData;
    private getTopThreats;
    private getAffectedPlayers;
    private generateSecurityRecommendations;
    private generateSecurityEventId;
}
//# sourceMappingURL=SecurityManager.d.ts.map