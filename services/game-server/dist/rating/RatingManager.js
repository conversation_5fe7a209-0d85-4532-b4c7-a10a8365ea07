"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingManager = void 0;
class RatingManager {
    constructor() {
        this.ratings = new Map(); // playerId -> PlayerRating
        this.gameResults = [];
        // ELO константы
        this.K_FACTOR = 32; // Коэффициент изменения рейтинга
        this.INITIAL_RATING = 1200; // Начальный рейтинг
    }
    /**
     * Получает рейтинг игрока или создает новый
     */
    getPlayerRating(playerId, playerName) {
        let rating = this.ratings.get(playerId);
        if (!rating) {
            rating = this.createInitialRating(playerId, playerName);
            this.ratings.set(playerId, rating);
        }
        return rating;
    }
    /**
     * Создает начальный рейтинг для нового игрока
     */
    createInitialRating(playerId, playerName) {
        return {
            playerId,
            playerName,
            rating: this.INITIAL_RATING,
            gamesPlayed: 0,
            wins: 0,
            losses: 0,
            winRate: 0,
            highestRating: this.INITIAL_RATING,
            currentStreak: 0,
            longestWinStreak: 0,
            averageGameDuration: 0,
            lastGameAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
            // Покерная статистика
            pokerGamesPlayed: 0,
            pokerWins: 0,
            pokerLosses: 0,
            pokerWinRate: 0,
            pokerTotalWinnings: 0,
            pokerBiggestPot: 0,
            pokerHandsPlayed: 0,
            pokerAllInWins: 0,
            pokerAllInLosses: 0,
            pokerTournamentWins: 0,
            pokerTournamentPlayed: 0,
            // Покерные комбинации
            pokerRoyalFlushes: 0,
            pokerStraightFlushes: 0,
            pokerFourOfKinds: 0,
            pokerFullHouses: 0,
            pokerFlushes: 0,
            pokerStraights: 0,
            pokerThreeOfKinds: 0,
            pokerTwoPairs: 0,
            pokerPairs: 0,
            pokerHighCardWins: 0
        };
    }
    /**
     * Обновляет рейтинги после игры
     */
    updateRatingsAfterGame(winnerId, winnerName, loserId, loserName, gameId, gameDuration, moveCount) {
        const winnerRating = this.getPlayerRating(winnerId, winnerName);
        const loserRating = this.getPlayerRating(loserId, loserName);
        // Вычисляем ожидаемые результаты по формуле ELO
        const expectedWinner = this.getExpectedScore(winnerRating.rating, loserRating.rating);
        const expectedLoser = this.getExpectedScore(loserRating.rating, winnerRating.rating);
        // Вычисляем новые рейтинги
        const newWinnerRating = Math.round(winnerRating.rating + this.K_FACTOR * (1 - expectedWinner));
        const newLoserRating = Math.round(loserRating.rating + this.K_FACTOR * (0 - expectedLoser));
        // Обновляем статистику победителя
        this.updatePlayerStats(winnerRating, newWinnerRating, true, gameDuration);
        // Обновляем статистику проигравшего
        this.updatePlayerStats(loserRating, newLoserRating, false, gameDuration);
        // Сохраняем результат игры
        const gameResult = {
            gameId,
            winnerId,
            loserId,
            gameDuration,
            moveCount,
            timestamp: new Date()
        };
        this.gameResults.push(gameResult);
        // Ограничиваем историю игр
        if (this.gameResults.length > 10000) {
            this.gameResults = this.gameResults.slice(-5000);
        }
        return { winnerRating, loserRating };
    }
    /**
     * Вычисляет ожидаемый результат по формуле ELO
     */
    getExpectedScore(ratingA, ratingB) {
        return 1 / (1 + Math.pow(10, (ratingB - ratingA) / 400));
    }
    /**
     * Обновляет статистику игрока
     */
    updatePlayerStats(playerRating, newRating, isWin, gameDuration) {
        // Обновляем основные показатели
        playerRating.rating = newRating;
        playerRating.gamesPlayed++;
        playerRating.lastGameAt = new Date();
        playerRating.updatedAt = new Date();
        // Обновляем рекорды
        if (newRating > playerRating.highestRating) {
            playerRating.highestRating = newRating;
        }
        // Обновляем статистику побед/поражений
        if (isWin) {
            playerRating.wins++;
            playerRating.currentStreak = playerRating.currentStreak > 0 ?
                playerRating.currentStreak + 1 : 1;
            if (playerRating.currentStreak > playerRating.longestWinStreak) {
                playerRating.longestWinStreak = playerRating.currentStreak;
            }
        }
        else {
            playerRating.losses++;
            playerRating.currentStreak = playerRating.currentStreak < 0 ?
                playerRating.currentStreak - 1 : -1;
        }
        // Обновляем процент побед
        playerRating.winRate = (playerRating.wins / playerRating.gamesPlayed) * 100;
        // Обновляем среднюю продолжительность игры
        const totalDuration = playerRating.averageGameDuration * (playerRating.gamesPlayed - 1) + gameDuration;
        playerRating.averageGameDuration = Math.round(totalDuration / playerRating.gamesPlayed);
    }
    /**
     * Получает таблицу лидеров
     */
    getLeaderboard(limit = 50) {
        return Array.from(this.ratings.values())
            .filter(rating => rating.gamesPlayed >= 5) // Минимум 5 игр для попадания в рейтинг
            .sort((a, b) => b.rating - a.rating)
            .slice(0, limit);
    }
    /**
     * Получает топ игроков по различным критериям
     */
    getTopPlayers() {
        const players = Array.from(this.ratings.values())
            .filter(rating => rating.gamesPlayed >= 5);
        return {
            byRating: players.sort((a, b) => b.rating - a.rating).slice(0, 10),
            byWins: players.sort((a, b) => b.wins - a.wins).slice(0, 10),
            byWinRate: players.sort((a, b) => b.winRate - a.winRate).slice(0, 10),
            byStreak: players.sort((a, b) => b.longestWinStreak - a.longestWinStreak).slice(0, 10)
        };
    }
    /**
     * Получает статистику игрока
     */
    getPlayerStats(playerId) {
        return this.ratings.get(playerId);
    }
    /**
     * Получает историю игр игрока
     */
    getPlayerGameHistory(playerId, limit = 20) {
        return this.gameResults
            .filter(result => result.winnerId === playerId || result.loserId === playerId)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }
    /**
     * Получает общую статистику системы рейтингов
     */
    getSystemStats() {
        const players = Array.from(this.ratings.values());
        const totalGames = this.gameResults.length;
        if (players.length === 0) {
            return {
                totalPlayers: 0,
                totalGames: 0,
                averageRating: 0,
                highestRating: 0,
                mostActivePlayer: null,
                recentGames: 0
            };
        }
        const averageRating = players.reduce((sum, p) => sum + p.rating, 0) / players.length;
        const highestRating = Math.max(...players.map(p => p.rating));
        const mostActivePlayer = players.reduce((prev, current) => prev.gamesPlayed > current.gamesPlayed ? prev : current);
        // Игры за последние 24 часа
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentGames = this.gameResults.filter(game => game.timestamp > oneDayAgo).length;
        return {
            totalPlayers: players.length,
            totalGames,
            averageRating: Math.round(averageRating),
            highestRating,
            mostActivePlayer: {
                name: mostActivePlayer.playerName,
                gamesPlayed: mostActivePlayer.gamesPlayed
            },
            recentGames
        };
    }
    /**
     * Обновляет покерную статистику игрока
     */
    updatePokerStats(playerId, playerName, isWin, potAmount, handRank, wasAllIn = false) {
        const rating = this.getPlayerRating(playerId, playerName);
        // Обновляем основную покерную статистику
        rating.pokerGamesPlayed++;
        rating.pokerHandsPlayed++;
        if (isWin) {
            rating.pokerWins++;
            rating.pokerTotalWinnings += potAmount;
            if (potAmount > rating.pokerBiggestPot) {
                rating.pokerBiggestPot = potAmount;
            }
            if (wasAllIn) {
                rating.pokerAllInWins++;
            }
            // Обновляем статистику комбинаций
            this.updatePokerHandStats(rating, handRank);
        }
        else {
            rating.pokerLosses++;
            if (wasAllIn) {
                rating.pokerAllInLosses++;
            }
        }
        // Обновляем процент побед в покере
        rating.pokerWinRate = (rating.pokerWins / rating.pokerGamesPlayed) * 100;
        rating.updatedAt = new Date();
        return rating;
    }
    /**
     * Обновляет статистику покерных комбинаций
     */
    updatePokerHandStats(rating, handRank) {
        switch (handRank) {
            case 10: // Royal Flush
                rating.pokerRoyalFlushes++;
                break;
            case 9: // Straight Flush
                rating.pokerStraightFlushes++;
                break;
            case 8: // Four of a Kind
                rating.pokerFourOfKinds++;
                break;
            case 7: // Full House
                rating.pokerFullHouses++;
                break;
            case 6: // Flush
                rating.pokerFlushes++;
                break;
            case 5: // Straight
                rating.pokerStraights++;
                break;
            case 4: // Three of a Kind
                rating.pokerThreeOfKinds++;
                break;
            case 3: // Two Pair
                rating.pokerTwoPairs++;
                break;
            case 2: // Pair
                rating.pokerPairs++;
                break;
            case 1: // High Card
                rating.pokerHighCardWins++;
                break;
        }
    }
    /**
     * Обновляет турнирную статистику
     */
    updatePokerTournamentStats(playerId, playerName, isWin) {
        const rating = this.getPlayerRating(playerId, playerName);
        rating.pokerTournamentPlayed++;
        if (isWin) {
            rating.pokerTournamentWins++;
        }
        rating.updatedAt = new Date();
        return rating;
    }
    /**
     * Получает покерную статистику игрока
     */
    getPokerStats(playerId) {
        const rating = this.ratings.get(playerId);
        if (!rating)
            return null;
        return {
            gamesPlayed: rating.pokerGamesPlayed,
            wins: rating.pokerWins,
            losses: rating.pokerLosses,
            winRate: rating.pokerWinRate,
            totalWinnings: rating.pokerTotalWinnings,
            biggestPot: rating.pokerBiggestPot,
            handsPlayed: rating.pokerHandsPlayed,
            allInWins: rating.pokerAllInWins,
            allInLosses: rating.pokerAllInLosses,
            tournamentWins: rating.pokerTournamentWins,
            tournamentPlayed: rating.pokerTournamentPlayed,
            combinations: {
                royalFlushes: rating.pokerRoyalFlushes,
                straightFlushes: rating.pokerStraightFlushes,
                fourOfKinds: rating.pokerFourOfKinds,
                fullHouses: rating.pokerFullHouses,
                flushes: rating.pokerFlushes,
                straights: rating.pokerStraights,
                threeOfKinds: rating.pokerThreeOfKinds,
                twoPairs: rating.pokerTwoPairs,
                pairs: rating.pokerPairs,
                highCardWins: rating.pokerHighCardWins
            }
        };
    }
    /**
     * Получает топ покерных игроков
     */
    getPokerLeaderboard(limit = 50) {
        return Array.from(this.ratings.values())
            .filter(rating => rating.pokerGamesPlayed >= 10) // Минимум 10 покерных игр
            .sort((a, b) => {
            // Сортируем по проценту побед, затем по количеству игр
            if (Math.abs(a.pokerWinRate - b.pokerWinRate) < 0.1) {
                return b.pokerGamesPlayed - a.pokerGamesPlayed;
            }
            return b.pokerWinRate - a.pokerWinRate;
        })
            .slice(0, limit)
            .map(rating => ({
            playerId: rating.playerId,
            playerName: rating.playerName,
            gamesPlayed: rating.pokerGamesPlayed,
            wins: rating.pokerWins,
            winRate: rating.pokerWinRate,
            totalWinnings: rating.pokerTotalWinnings,
            biggestPot: rating.pokerBiggestPot
        }));
    }
    /**
     * Получает рейтинговые категории
     */
    getRatingCategory(rating) {
        if (rating >= 2000)
            return { name: 'Гроссмейстер', color: '#FFD700', minRating: 2000 };
        if (rating >= 1800)
            return { name: 'Мастер', color: '#C0C0C0', minRating: 1800 };
        if (rating >= 1600)
            return { name: 'Эксперт', color: '#CD7F32', minRating: 1600 };
        if (rating >= 1400)
            return { name: 'Продвинутый', color: '#4CAF50', minRating: 1400 };
        if (rating >= 1200)
            return { name: 'Средний', color: '#2196F3', minRating: 1200 };
        if (rating >= 1000)
            return { name: 'Начинающий', color: '#FF9800', minRating: 1000 };
        return { name: 'Новичок', color: '#9E9E9E', minRating: 0 };
    }
    /**
     * Очищает старые данные
     */
    cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const cutoffDate = new Date(Date.now() - maxAge);
        let cleaned = 0;
        // Удаляем неактивных игроков
        for (const [playerId, rating] of this.ratings.entries()) {
            if (rating.lastGameAt < cutoffDate && rating.gamesPlayed < 5) {
                this.ratings.delete(playerId);
                cleaned++;
            }
        }
        // Очищаем старые результаты игр
        const initialLength = this.gameResults.length;
        this.gameResults = this.gameResults.filter(result => result.timestamp > cutoffDate);
        cleaned += initialLength - this.gameResults.length;
        return cleaned;
    }
}
exports.RatingManager = RatingManager;
//# sourceMappingURL=RatingManager.js.map