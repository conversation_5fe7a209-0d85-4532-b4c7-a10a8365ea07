{"version": 3, "file": "AchievementManager.d.ts", "sourceRoot": "", "sources": ["../../src/achievements/AchievementManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,CAAC;IACxE,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;IACjD,SAAS,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,OAAO,CAAC;IACnE,MAAM,CAAC,EAAE;QACP,IAAI,EAAE,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC;QACnC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC;KACxB,CAAC;CACH;AAED,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;IACtB,UAAU,EAAE,IAAI,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,YAAY,CAAuC;IAC3D,OAAO,CAAC,kBAAkB,CAA+C;;IAMzE;;OAEG;IACH,OAAO,CAAC,sBAAsB;IA8R9B;;OAEG;IACH,iBAAiB,CACf,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,YAAY,EACzB,WAAW,EAAE,UAAU,EAAE,GACxB,iBAAiB,EAAE;IAgCtB;;OAEG;IACH,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG;QACvC,QAAQ,EAAE,KAAK,CAAC,WAAW,GAAG;YAAE,UAAU,EAAE,IAAI,CAAA;SAAE,CAAC,CAAC;QACpD,MAAM,EAAE,WAAW,EAAE,CAAC;QACtB,QAAQ,EAAE;YAAE,KAAK,EAAE,MAAM,CAAC;YAAC,QAAQ,EAAE,MAAM,CAAC;YAAC,UAAU,EAAE,MAAM,CAAA;SAAE,CAAC;KACnE;IA6BD;;OAEG;IACH,yBAAyB,IAAI,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC;IAoB1D;;OAEG;IACH,mBAAmB,IAAI,WAAW,EAAE;IAMpC;;OAEG;IACH,cAAc,CAAC,aAAa,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAI9D;;OAEG;IACH,mBAAmB,IAAI;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnC,YAAY,EAAE;YAAE,WAAW,EAAE,WAAW,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAE,GAAG,IAAI,CAAC;QACjE,MAAM,EAAE;YAAE,WAAW,EAAE,WAAW,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAE,GAAG,IAAI,CAAC;KAC5D;CA8CF"}