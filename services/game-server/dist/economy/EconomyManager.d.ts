export interface Currency {
    coins: number;
    gems: number;
    tokens: number;
    seasonPoints: number;
}
export interface PlayerWallet {
    playerId: string;
    playerName: string;
    currency: Currency;
    totalEarned: Currency;
    totalSpent: Currency;
    transactions: Transaction[];
    lastUpdated: Date;
}
export interface Transaction {
    id: string;
    type: 'earn' | 'spend' | 'transfer' | 'purchase' | 'reward';
    currency: keyof Currency;
    amount: number;
    reason: string;
    source?: string;
    targetPlayer?: string;
    metadata?: any;
    timestamp: Date;
}
export interface ShopItem {
    id: string;
    name: string;
    description: string;
    category: 'cosmetic' | 'gameplay' | 'premium' | 'bundle' | 'seasonal';
    type: 'card_back' | 'avatar' | 'title' | 'emote' | 'boost' | 'chest' | 'subscription';
    rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
    price: Partial<Currency>;
    icon: string;
    preview?: string;
    isLimited: boolean;
    limitedQuantity?: number;
    soldQuantity: number;
    availableFrom?: Date;
    availableUntil?: Date;
    requirements?: {
        minLevel?: number;
        minRating?: number;
        achievements?: string[];
        clanMember?: boolean;
    };
    effects?: {
        experienceBoost?: number;
        coinBoost?: number;
        duration?: number;
    };
    bundle?: {
        items: string[];
        discount: number;
    };
    isActive: boolean;
    createdAt: Date;
}
export interface PlayerInventory {
    playerId: string;
    items: InventoryItem[];
    activeItems: {
        cardBack?: string;
        avatar?: string;
        title?: string;
    };
    lastUpdated: Date;
}
export interface InventoryItem {
    itemId: string;
    quantity: number;
    acquiredAt: Date;
    expiresAt?: Date;
    isActive: boolean;
    metadata?: any;
}
export interface DailyReward {
    day: number;
    reward: Partial<Currency>;
    item?: string;
    claimed: boolean;
    claimedAt?: Date;
}
export interface PlayerDailyRewards {
    playerId: string;
    currentStreak: number;
    longestStreak: number;
    lastClaimDate?: Date;
    rewards: DailyReward[];
    nextResetDate: Date;
}
export declare class EconomyManager {
    private wallets;
    private inventories;
    private shopItems;
    private dailyRewards;
    private transactionCounter;
    constructor();
    /**
     * Получает кошелек игрока
     */
    getPlayerWallet(playerId: string, playerName: string): PlayerWallet;
    /**
     * Добавляет валюту игроку
     */
    addCurrency(playerId: string, playerName: string, currency: keyof Currency, amount: number, reason: string, source?: string): boolean;
    /**
     * Тратит валюту игрока
     */
    spendCurrency(playerId: string, currency: keyof Currency, amount: number, reason: string, targetPlayer?: string): boolean;
    /**
     * Переводит валюту между игроками
     */
    transferCurrency(fromPlayerId: string, toPlayerId: string, toPlayerName: string, currency: keyof Currency, amount: number): boolean;
    /**
     * Покупает предмет в магазине
     */
    purchaseItem(playerId: string, itemId: string): {
        success: boolean;
        error?: string;
    };
    /**
     * Получает инвентарь игрока
     */
    getPlayerInventory(playerId: string): PlayerInventory;
    /**
     * Добавляет предмет в инвентарь
     */
    private addItemToInventory;
    /**
     * Активирует предмет
     */
    activateItem(playerId: string, itemId: string): boolean;
    /**
     * Получает ежедневные награды игрока
     */
    getDailyRewards(playerId: string): PlayerDailyRewards;
    /**
     * Забирает ежедневную награду
     */
    claimDailyReward(playerId: string, playerName: string, day: number): boolean;
    /**
     * Награждает игрока за игровые действия
     */
    rewardGameAction(playerId: string, playerName: string, action: string, metadata?: any): void;
    /**
     * Получает награды за игровые действия
     */
    private getGameActionRewards;
    /**
     * Инициализирует магазин
     */
    private initializeShop;
    /**
     * Проверяет требования для покупки предмета
     */
    private checkItemRequirements;
    /**
     * Генерирует ежедневные награды
     */
    private generateDailyRewards;
    /**
     * Получает дату следующего сброса
     */
    private getNextResetDate;
    /**
     * Генерирует ID транзакции
     */
    private generateTransactionId;
    /**
     * Получает статистику экономики
     */
    getEconomyStats(): {
        totalPlayers: number;
        totalCurrency: {
            coins: number;
            gems: number;
            tokens: number;
            seasonPoints: number;
        };
        totalTransactions: number;
        shopStats: {
            totalItems: number;
            totalSold: number;
        };
    };
    /**
     * Получает список предметов магазина
     */
    getShopItems(category?: string): ShopItem[];
}
//# sourceMappingURL=EconomyManager.d.ts.map