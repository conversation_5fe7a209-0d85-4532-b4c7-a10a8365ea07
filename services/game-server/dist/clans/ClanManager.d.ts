export interface Clan {
    id: string;
    name: string;
    tag: string;
    description: string;
    leaderId: string;
    leaderName: string;
    members: ClanMember[];
    level: number;
    experience: number;
    experienceToNextLevel: number;
    totalRating: number;
    averageRating: number;
    totalWins: number;
    totalGames: number;
    winRate: number;
    treasury: number;
    isPublic: boolean;
    requirements: {
        minRating: number;
        minLevel: number;
        applicationRequired: boolean;
    };
    perks: {
        experienceBonus: number;
        dailyTaskBonus: number;
        tournamentBonus: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
export interface ClanMember {
    playerId: string;
    playerName: string;
    role: 'leader' | 'officer' | 'member';
    joinedAt: Date;
    contributedExperience: number;
    contributedTreasury: number;
    lastActiveAt: Date;
    permissions: {
        canInvite: boolean;
        canKick: boolean;
        canManageEvents: boolean;
        canManageTreasury: boolean;
    };
}
export interface ClanApplication {
    id: string;
    clanId: string;
    playerId: string;
    playerName: string;
    playerRating: number;
    playerLevel: number;
    message: string;
    status: 'pending' | 'approved' | 'rejected';
    createdAt: Date;
    reviewedAt?: Date;
    reviewedBy?: string;
}
export interface ClanEvent {
    id: string;
    clanId: string;
    type: 'tournament' | 'challenge' | 'meeting' | 'training';
    title: string;
    description: string;
    startTime: Date;
    endTime: Date;
    maxParticipants: number;
    participants: string[];
    rewards: {
        experience: number;
        treasury: number;
        items?: string[];
    };
    status: 'scheduled' | 'active' | 'completed' | 'cancelled';
    createdBy: string;
    createdAt: Date;
}
export interface ClanWar {
    id: string;
    attackingClanId: string;
    defendingClanId: string;
    startTime: Date;
    endTime: Date;
    status: 'preparation' | 'active' | 'completed';
    battles: ClanBattle[];
    score: {
        attacking: number;
        defending: number;
    };
    rewards: {
        winner: {
            experience: number;
            treasury: number;
        };
        loser: {
            experience: number;
            treasury: number;
        };
    };
}
export interface ClanBattle {
    id: string;
    attackingPlayerId: string;
    defendingPlayerId: string;
    gameId: string;
    winnerId?: string;
    completedAt?: Date;
}
export declare class ClanManager {
    private clans;
    private playerClans;
    private applications;
    private events;
    private wars;
    /**
     * Создает новый клан
     */
    createClan(leaderId: string, leaderName: string, clanName: string, tag: string, description?: string, isPublic?: boolean): Clan;
    /**
     * Подает заявку на вступление в клан
     */
    applyToClan(clanId: string, playerId: string, playerName: string, playerRating: number, playerLevel: number, message?: string): ClanApplication;
    /**
     * Одобряет заявку на вступление
     */
    approveApplication(applicationId: string, reviewerId: string): boolean;
    /**
     * Отклоняет заявку на вступление
     */
    rejectApplication(applicationId: string, reviewerId: string): boolean;
    /**
     * Исключает игрока из клана
     */
    kickMember(clanId: string, kickerId: string, targetId: string): boolean;
    /**
     * Покидает клан
     */
    leaveClan(playerId: string): boolean;
    /**
     * Передает лидерство
     */
    transferLeadership(clanId: string, currentLeaderId: string, newLeaderId: string): boolean;
    /**
     * Добавляет опыт клану
     */
    addClanExperience(playerId: string, amount: number): boolean;
    /**
     * Получает информацию о клане
     */
    getClan(clanId: string): Clan | undefined;
    /**
     * Получает клан игрока
     */
    getPlayerClan(playerId: string): Clan | undefined;
    /**
     * Получает список кланов
     */
    getClans(limit?: number, sortBy?: 'level' | 'rating' | 'members'): Clan[];
    /**
     * Получает заявки клана
     */
    getClanApplications(clanId: string): ClanApplication[];
    /**
     * Получает статистику кланов
     */
    getStats(): {
        totalClans: number;
        totalMembers: number;
        averageClanSize: number;
        pendingApplications: number;
        levelDistribution: Record<number, number>;
    };
    private generateClanId;
    private generateApplicationId;
    private isClanNameTaken;
    private isClanTagTaken;
    private updateClanStats;
    private calculateExperienceForLevel;
    private updateClanPerks;
    /**
     * Очищает старые данные
     */
    cleanup(): number;
}
//# sourceMappingURL=ClanManager.d.ts.map