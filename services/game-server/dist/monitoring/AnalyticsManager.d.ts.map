{"version": 3, "file": "AnalyticsManager.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/AnalyticsManager.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,YAAY,GAAG,UAAU,GAAG,eAAe,GAAG,UAAU,GAAG,aAAa,GAAG,QAAQ,GAAG,OAAO,CAAC;IACzG,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1B,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC;IACvC,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,aAAa;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,SAAS,EAAE,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,kBAAkB,EAAE,MAAM,CAAC;IAC3B,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,kBAAkB;IACjC,SAAS,EAAE,IAAI,CAAC;IAChB,UAAU,EAAE;QACV,GAAG,EAAE,MAAM,CAAC;QACZ,MAAM,EAAE,MAAM,CAAC;QACf,iBAAiB,EAAE,MAAM,CAAC;QAC1B,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,WAAW,EAAE;QACX,WAAW,EAAE,MAAM,CAAC;QACpB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,YAAY,EAAE;QACZ,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,CAAC;QACvB,QAAQ,EAAE,MAAM,CAAC;QACjB,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,eAAe,EAAE;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE;YACT,IAAI,EAAE,MAAM,CAAC;YACb,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;SACf,CAAC;QACF,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;CACH;AAED,MAAM,WAAW,sBAAsB;IACrC,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;IAC/D,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,EAAE,MAAM,EAAE,CAAC;IACzB,YAAY,EAAE;QACZ,sBAAsB,EAAE,MAAM,CAAC;QAC/B,eAAe,EAAE,MAAM,CAAC;QACxB,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,cAAc,EAAE;QACd,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,YAAY,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,YAAY,EAAE;QACZ,UAAU,EAAE,MAAM,CAAC;QACnB,eAAe,EAAE,MAAM,CAAC;QACxB,YAAY,CAAC,EAAE,IAAI,CAAC;QACpB,cAAc,EAAE,MAAM,EAAE,CAAC;KAC1B,CAAC;IACF,WAAW,EAAE;QACX,uBAAuB,EAAE,MAAM,CAAC;QAChC,gBAAgB,EAAE,MAAM,CAAC;QACzB,uBAAuB,EAAE,MAAM,CAAC;KACjC,CAAC;CACH;AAED,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC;IACpD,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,cAAc,EAAE;QACd,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;QACpB,WAAW,CAAC,EAAE;YAAE,GAAG,EAAE,MAAM,CAAC;YAAC,GAAG,EAAE,MAAM,CAAA;SAAE,CAAC;QAC3C,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;QACzB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,QAAQ,EAAE,UAAU,EAAE,CAAC;IACvB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,OAAO,CAAC,EAAE,cAAc,CAAC;CAC1B;AAED,MAAM,WAAW,UAAU;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC5B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,cAAc;IAC7B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,uBAAuB,EAAE,MAAM,CAAC;IAChC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC9B;AAED,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAAmB;IACjC,OAAO,CAAC,QAAQ,CAAyC;IACzD,OAAO,CAAC,kBAAkB,CAA4B;IACtD,OAAO,CAAC,eAAe,CAAkD;IACzE,OAAO,CAAC,OAAO,CAAyC;IACxD,OAAO,CAAC,eAAe,CAAkC;IAEzD;;OAEG;IACH,UAAU,CACR,SAAS,EAAE,SAAS,CAAC,WAAW,CAAC,EACjC,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACzB,QAAQ,GAAE,SAAS,CAAC,UAAU,CAAS,EACvC,OAAO,GAAE,MAAgB,GACxB,IAAI;IAqBP;;OAEG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM;IA0BxD;;OAEG;IACH,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAmBnC;;OAEG;IACH,wBAAwB,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI;IAkB3D;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,GAAG,QAAQ,CAAC,GAAG,aAAa;IAazE;;OAEG;IACH,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;IAepC;;OAEG;IACH,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI;IAiCrE;;OAEG;IACH,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,sBAAsB;IAc/D;;OAEG;IACH,qBAAqB,CAAC,SAAS,GAAE,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,OAAe,GAAG,GAAG;IA6BhF;;OAEG;IACH,kBAAkB,IAAI,GAAG;IAYzB;;OAEG;IACH,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,GAAG,MAAM;IAa1E,OAAO,CAAC,aAAa;IAyBrB,OAAO,CAAC,qBAAqB;IAc7B,OAAO,CAAC,wBAAwB;IAWhC,OAAO,CAAC,uBAAuB;IAM/B,OAAO,CAAC,cAAc;IAetB,OAAO,CAAC,2BAA2B;IAgCnC,OAAO,CAAC,oBAAoB;IAW5B,OAAO,CAAC,6BAA6B;IAoBrC,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,kBAAkB;IAe1B,OAAO,CAAC,oBAAoB;IAQ5B,OAAO,CAAC,wBAAwB;IAKhC,OAAO,CAAC,UAAU;IAUlB,OAAO,CAAC,sBAAsB;IAoB9B,OAAO,CAAC,UAAU;IAKlB,OAAO,CAAC,oBAAoB;IAe5B,OAAO,CAAC,sBAAsB;IAW9B,OAAO,CAAC,uBAAuB;IAY/B,OAAO,CAAC,4BAA4B;IAKpC,OAAO,CAAC,2BAA2B;IAInC,OAAO,CAAC,YAAY;IAYpB,OAAO,CAAC,qBAAqB;IAS7B,OAAO,CAAC,gBAAgB;IAWxB,OAAO,CAAC,uBAAuB;IAK/B,OAAO,CAAC,sBAAsB;IAK9B,OAAO,CAAC,qBAAqB;IAK7B,OAAO,CAAC,gCAAgC;IAQxC,OAAO,CAAC,mBAAmB;IAI3B,OAAO,CAAC,YAAY;IAepB,OAAO,CAAC,eAAe;IAIvB,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,gBAAgB;CAGzB"}