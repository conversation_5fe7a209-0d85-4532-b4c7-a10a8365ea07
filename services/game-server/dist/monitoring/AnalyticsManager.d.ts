export interface GameEvent {
    id: string;
    eventType: 'game_start' | 'game_end' | 'player_action' | 'purchase' | 'achievement' | 'social' | 'error';
    playerId: string;
    sessionId: string;
    gameType?: string;
    roomId?: string;
    data: Record<string, any>;
    timestamp: Date;
    userAgent?: string;
    ipAddress?: string;
    platform: 'web' | 'mobile' | 'desktop';
    version: string;
}
export interface PlayerSession {
    sessionId: string;
    playerId: string;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    platform: string;
    events: GameEvent[];
    gamesPlayed: number;
    achievements: number;
    purchases: number;
    socialInteractions: number;
    errors: number;
}
export interface PerformanceMetrics {
    timestamp: Date;
    serverLoad: {
        cpu: number;
        memory: number;
        activeConnections: number;
        requestsPerSecond: number;
    };
    gameMetrics: {
        activeGames: number;
        averageGameDuration: number;
        gamesPerHour: number;
        playerConcurrency: number;
    };
    errorMetrics: {
        errorRate: number;
        criticalErrors: number;
        warnings: number;
        responseTime: number;
    };
    businessMetrics: {
        revenue: number;
        newPlayers: number;
        retention: {
            day1: number;
            day7: number;
            day30: number;
        };
        conversionRate: number;
    };
}
export interface PlayerBehaviorAnalysis {
    playerId: string;
    playerSegment: 'new' | 'casual' | 'core' | 'whale' | 'churned';
    lifetimeValue: number;
    churnRisk: number;
    preferredGames: string[];
    playPatterns: {
        averageSessionDuration: number;
        sessionsPerWeek: number;
        peakPlayTime: string;
        preferredPlatform: string;
    };
    socialBehavior: {
        friendsCount: number;
        clanParticipation: boolean;
        chatActivity: number;
        invitesSent: number;
    };
    monetization: {
        totalSpent: number;
        averagePurchase: number;
        lastPurchase?: Date;
        preferredItems: string[];
    };
    predictions: {
        nextPurchaseProbability: number;
        churnProbability: number;
        lifetimeValuePrediction: number;
    };
}
export interface A_BTestConfig {
    id: string;
    name: string;
    description: string;
    status: 'draft' | 'active' | 'paused' | 'completed';
    startDate: Date;
    endDate?: Date;
    targetAudience: {
        platform?: string[];
        playerLevel?: {
            min: number;
            max: number;
        };
        playerSegment?: string[];
        percentage: number;
    };
    variants: A_BVariant[];
    metrics: string[];
    results?: A_BTestResults;
}
export interface A_BVariant {
    id: string;
    name: string;
    description: string;
    config: Record<string, any>;
    trafficPercentage: number;
    participants: number;
}
export interface A_BTestResults {
    totalParticipants: number;
    conversionRate: Record<string, number>;
    statisticalSignificance: number;
    winningVariant?: string;
    metrics: Record<string, any>;
}
export declare class AnalyticsManager {
    private events;
    private sessions;
    private performanceHistory;
    private playerAnalytics;
    private abTests;
    private realTimeMetrics;
    /**
     * Записывает игровое событие
     */
    trackEvent(eventType: GameEvent['eventType'], playerId: string, sessionId: string, data: Record<string, any>, platform?: GameEvent['platform'], version?: string): void;
    /**
     * Начинает новую сессию игрока
     */
    startSession(playerId: string, platform: string): string;
    /**
     * Завершает сессию игрока
     */
    endSession(sessionId: string): void;
    /**
     * Записывает метрики производительности
     */
    recordPerformanceMetrics(metrics: PerformanceMetrics): void;
    /**
     * Создает A/B тест
     */
    createABTest(config: Omit<A_BTestConfig, 'id' | 'status'>): A_BTestConfig;
    /**
     * Запускает A/B тест
     */
    startABTest(testId: string): boolean;
    /**
     * Получает вариант A/B теста для игрока
     */
    getABTestVariant(testId: string, playerId: string): A_BVariant | null;
    /**
     * Анализирует поведение игрока
     */
    analyzePlayerBehavior(playerId: string): PlayerBehaviorAnalysis;
    /**
     * Получает дашборд аналитики
     */
    getAnalyticsDashboard(timeRange?: 'hour' | 'day' | 'week' | 'month'): any;
    /**
     * Получает отчет по удержанию игроков
     */
    getRetentionReport(): any;
    /**
     * Экспортирует данные для внешнего анализа
     */
    exportData(format: 'json' | 'csv', timeRange: Date, endTime: Date): string;
    private updateSession;
    private updateRealTimeMetrics;
    private processEventForAnalytics;
    private sendToExternalAnalytics;
    private analyzeSession;
    private createInitialPlayerAnalysis;
    private updatePlayerAnalysis;
    private updatePlayerAnalysisFromEvent;
    private calculatePlayerSegment;
    private calculateChurnRisk;
    private calculatePredictions;
    private isPlayerInTargetAudience;
    private hashString;
    private checkPerformanceAlerts;
    private sendAlerts;
    private calculateGameMetrics;
    private calculatePlayerMetrics;
    private calculateRevenueMetrics;
    private calculateAverageGameDuration;
    private getLatestPerformanceMetrics;
    private getTopEvents;
    private getPlayerSegmentation;
    private getABTestResults;
    private calculateCohortAnalysis;
    private calculateChurnAnalysis;
    private getRetentionBySegment;
    private generateRetentionRecommendations;
    private updateABTestResults;
    private convertToCSV;
    private generateEventId;
    private generateSessionId;
    private generateABTestId;
}
//# sourceMappingURL=AnalyticsManager.d.ts.map