{"version": 3, "file": "SeasonalEventManager.d.ts", "sourceRoot": "", "sources": ["../../src/events/SeasonalEventManager.ts"], "names": [], "mappings": "AAAA,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,YAAY,GAAG,WAAW,GAAG,cAAc,GAAG,YAAY,GAAG,aAAa,CAAC;IACjF,KAAK,EAAE,UAAU,GAAG,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,WAAW,GAAG,WAAW,GAAG,aAAa,GAAG,SAAS,CAAC;IAC9G,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,EAAE,IAAI,CAAC;IACd,MAAM,EAAE,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG,WAAW,CAAC;IAC1D,YAAY,EAAE;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,eAAe,CAAC,EAAE,OAAO,CAAC;KAC3B,CAAC;IACF,OAAO,EAAE,cAAc,EAAE,CAAC;IAC1B,WAAW,EAAE,wBAAwB,EAAE,CAAC;IACxC,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IACxC,KAAK,EAAE;QACL,QAAQ,EAAE,MAAM,CAAC;QACjB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;QACxB,YAAY,EAAE,UAAU,EAAE,CAAC;KAC5B,CAAC;IACF,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,OAAO,GAAG,aAAa,GAAG,YAAY,GAAG,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;IACnF,MAAM,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC5D,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,OAAO,CAAC;IACnB,WAAW,EAAE;QACX,IAAI,EAAE,MAAM,GAAG,QAAQ,GAAG,eAAe,GAAG,YAAY,CAAC;QACzD,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH;AAED,MAAM,WAAW,wBAAwB;IACvC,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,IAAI,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,UAAU,EAAE,iBAAiB,EAAE,CAAC;IAChC,QAAQ,EAAE,IAAI,CAAC;IACf,YAAY,EAAE,IAAI,CAAC;CACpB;AAED,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,cAAc,CAAC;IACvB,WAAW,EAAE,OAAO,CAAC;IACrB,WAAW,CAAC,EAAE,IAAI,CAAC;CACpB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC;IACvD,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,MAAM;IACrB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,EAAE,IAAI,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,EAAE,CAAC;IACjB,OAAO,EAAE,cAAc,EAAE,CAAC;IAC1B,WAAW,EAAE,wBAAwB,EAAE,CAAC;IACxC,MAAM,EAAE,UAAU,GAAG,QAAQ,GAAG,WAAW,CAAC;CAC7C;AAED,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,MAAM,CAAyC;IACvD,OAAO,CAAC,OAAO,CAAkC;IACjD,OAAO,CAAC,aAAa,CAAuB;;IAM5C;;OAEG;IACH,WAAW,CACT,IAAI,EAAE,MAAM,EACZ,WAAW,EAAE,MAAM,EACnB,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC,EAC3B,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,EAC7B,SAAS,EAAE,IAAI,EACf,OAAO,EAAE,IAAI,EACb,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,EAC3C,OAAO,EAAE,cAAc,EAAE,EACzB,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,GAC5B,aAAa;IA0BhB;;OAEG;IACH,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,OAAO;IA+CzH;;OAEG;IACH,oBAAoB,CAClB,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,SAAS,EACtD,QAAQ,CAAC,EAAE,GAAG,GACb,gBAAgB,GAAG,IAAI;IAoC1B;;OAEG;IACH,eAAe,IAAI,aAAa,EAAE;IAQlC;;OAEG;IACH,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,aAAa,EAAE;IAMhE;;OAEG;IACH,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAK7E;;OAEG;IACH,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,GAAE,MAAY,GAAG,wBAAwB,EAAE;IAWrF;;OAEG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,IAAI,EACf,OAAO,EAAE,IAAI,EACb,KAAK,EAAE,MAAM,EACb,WAAW,EAAE,MAAM,GAClB,MAAM;IA2BT;;OAEG;IACH,gBAAgB,IAAI,MAAM,GAAG,IAAI;IAIjC;;OAEG;IACH,QAAQ;;;;;;;IAoBR;;OAEG;IACH,mBAAmB,IAAI,IAAI;IA0B3B,OAAO,CAAC,uBAAuB;IA6C/B,OAAO,CAAC,oBAAoB;IAmD5B,OAAO,CAAC,qBAAqB;IAgB7B,OAAO,CAAC,kBAAkB;IAyC1B,OAAO,CAAC,eAAe;IAoBvB,OAAO,CAAC,iBAAiB;IAyBzB,OAAO,CAAC,aAAa;IAqBrB,OAAO,CAAC,eAAe;IAIvB,OAAO,CAAC,gBAAgB;CAGzB"}