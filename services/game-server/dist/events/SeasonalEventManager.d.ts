export interface SeasonalEvent {
    id: string;
    name: string;
    description: string;
    type: 'tournament' | 'challenge' | 'special_game' | 'collection' | 'leaderboard';
    theme: 'new_year' | 'valentine' | 'easter' | 'summer' | 'halloween' | 'christmas' | 'anniversary' | 'special';
    startDate: Date;
    endDate: Date;
    status: 'upcoming' | 'active' | 'completed' | 'cancelled';
    requirements: {
        minLevel: number;
        minRating: number;
        maxParticipants?: number;
        clanMembersOnly?: boolean;
    };
    rewards: SeasonalReward[];
    leaderboard: SeasonalLeaderboardEntry[];
    participants: string[];
    progress: Map<string, SeasonalProgress>;
    rules: {
        gameMode: string;
        specialRules?: string[];
        pointsSystem: PointsRule[];
    };
    createdAt: Date;
    updatedAt: Date;
}
export interface SeasonalReward {
    id: string;
    name: string;
    description: string;
    type: 'title' | 'achievement' | 'experience' | 'currency' | 'cosmetic' | 'special';
    rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
    icon: string;
    value: number;
    isLimited: boolean;
    requirement: {
        type: 'rank' | 'points' | 'participation' | 'completion';
        value: number;
    };
}
export interface SeasonalLeaderboardEntry {
    playerId: string;
    playerName: string;
    clanTag?: string;
    points: number;
    rank: number;
    gamesPlayed: number;
    lastUpdated: Date;
}
export interface SeasonalProgress {
    playerId: string;
    eventId: string;
    points: number;
    gamesPlayed: number;
    achievements: string[];
    milestones: SeasonalMilestone[];
    joinedAt: Date;
    lastActiveAt: Date;
}
export interface SeasonalMilestone {
    id: string;
    name: string;
    description: string;
    requirement: number;
    reward: SeasonalReward;
    isCompleted: boolean;
    completedAt?: Date;
}
export interface PointsRule {
    action: 'win' | 'lose' | 'play' | 'streak' | 'special';
    points: number;
    multiplier?: number;
    condition?: string;
}
export interface Season {
    id: string;
    name: string;
    number: number;
    startDate: Date;
    endDate: Date;
    theme: string;
    description: string;
    events: string[];
    rewards: SeasonalReward[];
    leaderboard: SeasonalLeaderboardEntry[];
    status: 'upcoming' | 'active' | 'completed';
}
export declare class SeasonalEventManager {
    private events;
    private seasons;
    private currentSeason;
    constructor();
    /**
     * Создает новое сезонное событие
     */
    createEvent(name: string, description: string, type: SeasonalEvent['type'], theme: SeasonalEvent['theme'], startDate: Date, endDate: Date, requirements: SeasonalEvent['requirements'], rewards: SeasonalReward[], rules: SeasonalEvent['rules']): SeasonalEvent;
    /**
     * Регистрирует игрока на событие
     */
    registerPlayer(eventId: string, playerId: string, playerName: string, playerLevel: number, playerRating: number): boolean;
    /**
     * Обновляет прогресс игрока в событии
     */
    updatePlayerProgress(eventId: string, playerId: string, action: 'win' | 'lose' | 'play' | 'streak' | 'special', gameData?: any): SeasonalProgress | null;
    /**
     * Получает активные события
     */
    getActiveEvents(): SeasonalEvent[];
    /**
     * Получает события по теме
     */
    getEventsByTheme(theme: SeasonalEvent['theme']): SeasonalEvent[];
    /**
     * Получает прогресс игрока в событии
     */
    getPlayerProgress(eventId: string, playerId: string): SeasonalProgress | null;
    /**
     * Получает лидерборд события
     */
    getEventLeaderboard(eventId: string, limit?: number): SeasonalLeaderboardEntry[];
    /**
     * Создает новый сезон
     */
    createSeason(name: string, number: number, startDate: Date, endDate: Date, theme: string, description: string): Season;
    /**
     * Получает текущий сезон
     */
    getCurrentSeason(): Season | null;
    /**
     * Получает статистику событий
     */
    getStats(): {
        totalEvents: number;
        activeEvents: number;
        totalParticipants: number;
        eventsByTheme: Record<string, number>;
        currentSeason: string;
    };
    /**
     * Обновляет статус событий
     */
    updateEventStatuses(): void;
    private initializeDefaultEvents;
    private generateEventRewards;
    private generateSeasonRewards;
    private generateMilestones;
    private checkMilestones;
    private updateLeaderboard;
    private finalizeEvent;
    private generateEventId;
    private generateSeasonId;
}
//# sourceMappingURL=SeasonalEventManager.d.ts.map