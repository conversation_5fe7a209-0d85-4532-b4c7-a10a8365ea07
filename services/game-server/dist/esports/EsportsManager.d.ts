export interface EsportsEvent {
    id: string;
    name: string;
    description: string;
    type: 'tournament' | 'league' | 'championship' | 'exhibition';
    gameMode: 'durak' | 'poker' | 'preferans' | 'mixed';
    format: 'single_elimination' | 'double_elimination' | 'round_robin' | 'swiss';
    prizePool: {
        total: number;
        currency: 'coins' | 'gems' | 'real_money';
        distribution: {
            place: number;
            amount: number;
            percentage: number;
        }[];
    };
    schedule: {
        registrationStart: Date;
        registrationEnd: Date;
        eventStart: Date;
        eventEnd: Date;
    };
    requirements: {
        minRating: number;
        minLevel: number;
        maxParticipants: number;
        entryFee?: number;
        inviteOnly?: boolean;
        regionRestriction?: string[];
    };
    participants: EsportsParticipant[];
    matches: EsportsMatch[];
    brackets: EsportsBracket[];
    status: 'upcoming' | 'registration' | 'active' | 'completed' | 'cancelled';
    streaming: StreamingInfo;
    sponsors: Sponsor[];
    officials: Official[];
    rules: string[];
    createdAt: Date;
    updatedAt: Date;
}
export interface EsportsParticipant {
    playerId: string;
    playerName: string;
    teamId?: string;
    teamName?: string;
    rating: number;
    seed: number;
    registeredAt: Date;
    status: 'registered' | 'confirmed' | 'disqualified' | 'eliminated' | 'active';
    stats: ParticipantStats;
}
export interface ParticipantStats {
    gamesPlayed: number;
    gamesWon: number;
    gamesLost: number;
    winRate: number;
    averageGameDuration: number;
    totalPrizeWon: number;
    achievements: string[];
}
export interface EsportsMatch {
    id: string;
    eventId: string;
    round: number;
    matchNumber: number;
    participant1: string;
    participant2: string;
    winner?: string;
    score: {
        player1: number;
        player2: number;
    };
    games: GameResult[];
    scheduledTime: Date;
    startTime?: Date;
    endTime?: Date;
    status: 'scheduled' | 'live' | 'completed' | 'postponed' | 'cancelled';
    streaming: MatchStreaming;
    officials: string[];
    chatLog: ChatMessage[];
}
export interface GameResult {
    gameId: string;
    winner: string;
    duration: number;
    moves: number;
    endReason: 'normal' | 'timeout' | 'forfeit' | 'disqualification';
    replay?: string;
}
export interface EsportsBracket {
    id: string;
    eventId: string;
    type: 'winners' | 'losers' | 'finals';
    rounds: BracketRound[];
    participants: string[];
}
export interface BracketRound {
    roundNumber: number;
    matches: string[];
    isCompleted: boolean;
}
export interface StreamingInfo {
    isLive: boolean;
    streamUrl?: string;
    streamKey?: string;
    platform: 'twitch' | 'youtube' | 'internal' | 'multiple';
    viewers: number;
    maxViewers: number;
    streamers: Streamer[];
    chatEnabled: boolean;
    recordingEnabled: boolean;
    highlights: StreamHighlight[];
}
export interface Streamer {
    id: string;
    name: string;
    platform: string;
    channelUrl: string;
    isOfficial: boolean;
    viewerCount: number;
    language: string;
}
export interface StreamHighlight {
    id: string;
    title: string;
    timestamp: Date;
    duration: number;
    clipUrl?: string;
    description: string;
    tags: string[];
}
export interface MatchStreaming {
    isStreamed: boolean;
    streamers: string[];
    viewers: number;
    chatMessages: number;
    highlights: string[];
}
export interface Sponsor {
    id: string;
    name: string;
    logo: string;
    website: string;
    tier: 'title' | 'presenting' | 'official' | 'supporting';
    contribution: number;
}
export interface Official {
    id: string;
    name: string;
    role: 'admin' | 'referee' | 'observer' | 'commentator';
    permissions: string[];
    assignedMatches: string[];
}
export interface ChatMessage {
    id: string;
    userId: string;
    username: string;
    message: string;
    timestamp: Date;
    type: 'chat' | 'system' | 'highlight';
    moderated: boolean;
}
export interface EsportsLeague {
    id: string;
    name: string;
    season: number;
    divisions: Division[];
    schedule: LeagueSchedule;
    standings: LeagueStandings[];
    playoffs: EsportsEvent;
    status: 'upcoming' | 'active' | 'playoffs' | 'completed';
}
export interface Division {
    id: string;
    name: string;
    tier: number;
    teams: string[];
    promotionSpots: number;
    relegationSpots: number;
}
export interface LeagueSchedule {
    regularSeason: {
        start: Date;
        end: Date;
        matchesPerWeek: number;
    };
    playoffs: {
        start: Date;
        end: Date;
        format: string;
    };
}
export interface LeagueStandings {
    teamId: string;
    teamName: string;
    division: string;
    gamesPlayed: number;
    wins: number;
    losses: number;
    winRate: number;
    points: number;
    rank: number;
}
export declare class EsportsManager {
    private events;
    private leagues;
    private participants;
    private streamingData;
    /**
     * Создает киберспортивное событие
     */
    createEvent(name: string, description: string, type: EsportsEvent['type'], gameMode: EsportsEvent['gameMode'], format: EsportsEvent['format'], prizePool: EsportsEvent['prizePool'], schedule: EsportsEvent['schedule'], requirements: EsportsEvent['requirements']): EsportsEvent;
    /**
     * Регистрирует участника в событии
     */
    registerParticipant(eventId: string, playerId: string, playerName: string, rating: number, teamId?: string, teamName?: string): boolean;
    /**
     * Начинает событие
     */
    startEvent(eventId: string): boolean;
    /**
     * Создает матч
     */
    createMatch(eventId: string, round: number, participant1: string, participant2: string, scheduledTime: Date): EsportsMatch;
    /**
     * Завершает матч
     */
    completeMatch(matchId: string, winner: string, games: GameResult[]): boolean;
    /**
     * Начинает стриминг события
     */
    startEventStreaming(event: EsportsEvent): void;
    /**
     * Добавляет стримера к событию
     */
    addStreamer(eventId: string, streamer: Streamer): boolean;
    /**
     * Обновляет количество зрителей
     */
    updateViewerCount(eventId: string, streamerId: string, viewers: number): void;
    /**
     * Создает хайлайт
     */
    createHighlight(eventId: string, title: string, description: string, duration: number, tags: string[]): StreamHighlight;
    /**
     * Добавляет сообщение в чат матча
     */
    addChatMessage(matchId: string, userId: string, username: string, message: string): void;
    /**
     * Получает активные события
     */
    getActiveEvents(): EsportsEvent[];
    /**
     * Получает статистику киберспорта
     */
    getEsportsStats(): {
        totalEvents: number;
        activeEvents: number;
        totalParticipants: number;
        totalPrizePool: number;
        totalViewers: number;
        eventsByGame: Record<string, number>;
    };
    private generateBrackets;
    private generateSingleEliminationBracket;
    private generateDoubleEliminationBracket;
    private generateRoundRobinBracket;
    private createInitialMatches;
    private updateParticipantStats;
    private advanceWinner;
    private findMatch;
    private getDefaultRules;
    private generateEventId;
    private generateMatchId;
    private generateBracketId;
    private generateHighlightId;
    private generateChatMessageId;
}
//# sourceMappingURL=EsportsManager.d.ts.map