{"version": 3, "file": "PreferansGame.d.ts", "sourceRoot": "", "sources": ["../../../src/games/preferans/PreferansGame.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAmB,MAAM,kBAAkB,CAAC;AAE/D,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,IAAI,EAAE,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,cAAc,CAAC;IACtB,UAAU,CAAC,EAAE,YAAY,CAAC;IAC1B,QAAQ,EAAE,OAAO,CAAC;IAClB,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC;IACpB,gBAAgB,CAAC,EAAE,iBAAiB,CAAC;CACtC;AAED,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,QAAQ,GAAG,OAAO,GAAG,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC;IAC9D,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;IAC3B,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,cAAc,EAAE,CAAC;IAC5B,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,cAAc;IAC7B,QAAQ,EAAE,iBAAiB,CAAC;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,cAAc;IAC7B,KAAK,EAAE;QAAE,QAAQ,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,IAAI,CAAA;KAAE,EAAE,CAAC;IAC1C,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,IAAI,CAAC;CAChB;AAED,MAAM,WAAW,kBAAkB;IACjC,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,eAAe,EAAE,CAAC;IAC3B,IAAI,EAAE,IAAI,EAAE,CAAC;IACb,KAAK,EAAE,IAAI,EAAE,CAAC;IACd,YAAY,EAAE,SAAS,GAAG,SAAS,GAAG,gBAAgB,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC;IAC5F,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,cAAc,GAAG,IAAI,CAAC;IACpC,MAAM,EAAE,cAAc,EAAE,CAAC;IACzB,cAAc,EAAE,YAAY,EAAE,CAAC;IAC/B,QAAQ,EAAE,iBAAiB,GAAG,IAAI,CAAC;IACnC,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC;IACvB,QAAQ,EAAE,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;CAC1C;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAM;gBAEpB,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAE,QAAQ,GAAG,MAAM,GAAG,UAAqB;IAmB/E;;OAEG;IACH,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO;IA+BxD;;OAEG;IACH,YAAY,IAAI,IAAI;IAkCpB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAY3B;;OAEG;IACH,OAAO,CAAC,WAAW;IAInB;;OAEG;IACH,OAAO,CAAC,SAAS;IAsBjB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAgBzB;;OAEG;IACH,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,MAAM,GAAG,OAAO;IAkC9D;;OAEG;IACH,OAAO,CAAC,UAAU;IAYlB;;OAEG;IACH,OAAO,CAAC,WAAW;IAanB;;OAEG;IACH,OAAO,CAAC,aAAa;IAUrB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAIxB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAKzB;;OAEG;IACH,OAAO,CAAC,eAAe;IAqCvB;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,GAAG,OAAO;IA6CpE;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,OAAO;IAmD/C;;OAEG;IACH,OAAO,CAAC,WAAW;IAiBnB;;OAEG;IACH,OAAO,CAAC,aAAa;IA4BrB;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAmC5B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAKxB;;OAEG;IACH,OAAO,CAAC,YAAY;IAiDpB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAoB/B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAMzB;;OAEG;IACH,OAAO,CAAC,cAAc;IAOtB,YAAY,IAAI,kBAAkB;IAIlC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,GAAG;IAe1C,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAKvC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;CAgBhD"}