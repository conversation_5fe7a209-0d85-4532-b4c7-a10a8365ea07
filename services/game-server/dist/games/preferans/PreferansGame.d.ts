import { Card, Suit } from '../../types/Card';
export interface PreferansPlayer {
    id: string;
    name: string;
    hand: Card[];
    position: number;
    score: PreferansScore;
    currentBid?: PreferansBid;
    isActive: boolean;
    tricksWon: Card[][];
    declaredContract?: PreferansContract;
}
export interface PreferansBid {
    suit: 'spades' | 'clubs' | 'diamonds' | 'hearts' | 'no_trump';
    level: number;
    points: number;
    player: string;
}
export interface PreferansContract {
    suit: PreferansBid['suit'];
    level: number;
    declarer: string;
    defenders: string[];
    tricksNeeded: number;
}
export interface PreferansScore {
    bullet: number;
    mountain: number;
    pool: number;
    contracts: ContractResult[];
    totalScore: number;
}
export interface ContractResult {
    contract: PreferansContract;
    tricksWon: number;
    success: boolean;
    points: number;
    round: number;
}
export interface PreferansTrick {
    cards: {
        playerId: string;
        card: Card;
    }[];
    winner: string;
    leadSuit: Suit;
}
export interface PreferansGameState {
    id: string;
    players: PreferansPlayer[];
    deck: Card[];
    talon: Card[];
    currentPhase: 'dealing' | 'bidding' | 'talon_exchange' | 'playing' | 'scoring' | 'finished';
    currentPlayer: number;
    currentTrick: PreferansTrick | null;
    tricks: PreferansTrick[];
    biddingHistory: PreferansBid[];
    contract: PreferansContract | null;
    round: number;
    dealer: number;
    trumpSuit: Suit | null;
    gameType: 'bullet' | 'pool' | 'mountain';
}
export declare class PreferansGame {
    private gameState;
    private readonly maxRounds;
    constructor(gameId: string, gameType?: 'bullet' | 'pool' | 'mountain');
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId: string, playerName: string): boolean;
    /**
     * Начинает новую сдачу
     */
    startNewDeal(): void;
    /**
     * Создает колоду для преферанса (32 карты: 7-A)
     */
    private createPreferansDeck;
    /**
     * Перемешивает колоду
     */
    private shuffleDeck;
    /**
     * Раздает карты
     */
    private dealCards;
    /**
     * Сортирует карты по мастям и старшинству для преферанса
     */
    private sortPreferansHand;
    /**
     * Делает ставку в торговле
     */
    makeBid(playerId: string, bid: PreferansBid | 'pass'): boolean;
    /**
     * Проверяет валидность ставки
     */
    private isValidBid;
    /**
     * Сравнивает две ставки
     */
    private compareBids;
    /**
     * Получает самую высокую ставку
     */
    private getHighestBid;
    /**
     * Переходит к следующему игроку в торговле
     */
    private moveToNextBidder;
    /**
     * Проверяет, завершена ли торговля
     */
    private isBiddingComplete;
    /**
     * Завершает торговлю и определяет контракт
     */
    private finalizeBidding;
    /**
     * Обменивается картами с прикупом
     */
    exchangeWithTalon(playerId: string, cardsToDiscard: Card[]): boolean;
    /**
     * Играет карту
     */
    playCard(playerId: string, card: Card): boolean;
    /**
     * Проверяет валидность хода
     */
    private isValidPlay;
    /**
     * Завершает взятку
     */
    private completeTrick;
    /**
     * Определяет победителя взятки
     */
    private determineTrickWinner;
    /**
     * Сравнивает ранги карт в преферансе
     */
    private compareCardRanks;
    /**
     * Завершает сдачу и подсчитывает очки
     */
    private completeDeal;
    /**
     * Вычисляет очки за контракт
     */
    private calculateContractPoints;
    /**
     * Обновляет общие счета игроков
     */
    private updateTotalScores;
    /**
     * Проверяет, завершена ли игра
     */
    private isGameFinished;
    getGameState(): PreferansGameState;
    getPublicGameState(playerId?: string): any;
    canPlayerAct(playerId: string): boolean;
    getAvailableActions(playerId: string): string[];
}
//# sourceMappingURL=PreferansGame.d.ts.map