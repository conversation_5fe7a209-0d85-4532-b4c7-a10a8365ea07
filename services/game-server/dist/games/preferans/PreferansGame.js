"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferansGame = void 0;
const Card_1 = require("../../types/Card");
class PreferansGame {
    constructor(gameId, gameType = 'bullet') {
        this.maxRounds = 10; // стандартная игра до 10 пуль
        this.gameState = {
            id: gameId,
            players: [],
            deck: [],
            talon: [],
            currentPhase: 'dealing',
            currentPlayer: 0,
            currentTrick: null,
            tricks: [],
            biddingHistory: [],
            contract: null,
            round: 1,
            dealer: 0,
            trumpSuit: null,
            gameType
        };
    }
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId, playerName) {
        if (this.gameState.players.length >= 3) {
            return false; // Преферанс только для 3 игроков
        }
        if (this.gameState.players.some(p => p.id === playerId)) {
            return false;
        }
        const player = {
            id: playerId,
            name: player<PERSON><PERSON>,
            hand: [],
            position: this.gameState.players.length,
            score: {
                bullet: 0,
                mountain: 0,
                pool: 0,
                contracts: [],
                totalScore: 0
            },
            isActive: true,
            tricksWon: [],
            currentBid: undefined,
            declaredContract: undefined
        };
        this.gameState.players.push(player);
        return true;
    }
    /**
     * Начинает новую сдачу
     */
    startNewDeal() {
        if (this.gameState.players.length !== 3) {
            throw new Error('Preferans requires exactly 3 players');
        }
        // Сброс состояния
        this.gameState.players.forEach(player => {
            player.hand = [];
            player.tricksWon = [];
            player.currentBid = undefined;
            player.declaredContract = undefined;
        });
        this.gameState.deck = [];
        this.gameState.talon = [];
        this.gameState.currentTrick = null;
        this.gameState.tricks = [];
        this.gameState.biddingHistory = [];
        this.gameState.contract = null;
        this.gameState.trumpSuit = null;
        this.gameState.currentPhase = 'dealing';
        // Создаем и перемешиваем колоду (32 карты для преферанса)
        this.createPreferansDeck();
        this.shuffleDeck();
        // Раздаем карты
        this.dealCards();
        // Начинаем торговлю
        this.gameState.currentPhase = 'bidding';
        this.gameState.currentPlayer = (this.gameState.dealer + 1) % 3;
    }
    /**
     * Создает колоду для преферанса (32 карты: 7-A)
     */
    createPreferansDeck() {
        const suits = ['spades', 'clubs', 'diamonds', 'hearts'];
        const ranks = ['7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        this.gameState.deck = [];
        for (const suit of suits) {
            for (const rank of ranks) {
                this.gameState.deck.push({ suit, rank });
            }
        }
    }
    /**
     * Перемешивает колоду
     */
    shuffleDeck() {
        this.gameState.deck = Card_1.CardUtils.shuffleDeck(this.gameState.deck);
    }
    /**
     * Раздает карты
     */
    dealCards() {
        // Раздаем по 10 карт каждому игроку
        for (let round = 0; round < 10; round++) {
            for (const player of this.gameState.players) {
                if (this.gameState.deck.length > 0) {
                    player.hand.push(this.gameState.deck.pop());
                }
            }
        }
        // Оставшиеся 2 карты в прикуп
        this.gameState.talon = [
            this.gameState.deck.pop(),
            this.gameState.deck.pop()
        ];
        // Сортируем карты игроков
        this.gameState.players.forEach(player => {
            player.hand = this.sortPreferansHand(player.hand);
        });
    }
    /**
     * Сортирует карты по мастям и старшинству для преферанса
     */
    sortPreferansHand(cards) {
        const suitOrder = ['spades', 'clubs', 'diamonds', 'hearts'];
        const rankOrder = ['7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        return cards.sort((a, b) => {
            const aSuitIndex = suitOrder.indexOf(a.suit);
            const bSuitIndex = suitOrder.indexOf(b.suit);
            if (aSuitIndex !== bSuitIndex) {
                return aSuitIndex - bSuitIndex;
            }
            return rankOrder.indexOf(a.rank) - rankOrder.indexOf(b.rank);
        });
    }
    /**
     * Делает ставку в торговле
     */
    makeBid(playerId, bid) {
        if (this.gameState.currentPhase !== 'bidding') {
            return false;
        }
        const player = this.gameState.players[this.gameState.currentPlayer];
        if (!player || player.id !== playerId) {
            return false;
        }
        if (bid === 'pass') {
            // Игрок пасует
            player.currentBid = undefined;
        }
        else {
            // Проверяем валидность ставки
            if (!this.isValidBid(bid)) {
                return false;
            }
            player.currentBid = bid;
            this.gameState.biddingHistory.push(bid);
        }
        // Переходим к следующему игроку
        this.moveToNextBidder();
        // Проверяем, завершена ли торговля
        if (this.isBiddingComplete()) {
            this.finalizeBidding();
        }
        return true;
    }
    /**
     * Проверяет валидность ставки
     */
    isValidBid(bid) {
        const lastBid = this.getHighestBid();
        if (!lastBid) {
            // Первая ставка должна быть минимум 6
            return bid.level >= 6;
        }
        // Новая ставка должна быть выше предыдущей
        return this.compareBids(bid, lastBid) > 0;
    }
    /**
     * Сравнивает две ставки
     */
    compareBids(bid1, bid2) {
        if (bid1.level !== bid2.level) {
            return bid1.level - bid2.level;
        }
        // При одинаковом уровне сравниваем масти
        const suitOrder = ['spades', 'clubs', 'diamonds', 'hearts', 'no_trump'];
        const suit1Index = suitOrder.indexOf(bid1.suit);
        const suit2Index = suitOrder.indexOf(bid2.suit);
        return suit1Index - suit2Index;
    }
    /**
     * Получает самую высокую ставку
     */
    getHighestBid() {
        if (this.gameState.biddingHistory.length === 0) {
            return null;
        }
        return this.gameState.biddingHistory.reduce((highest, current) => {
            return this.compareBids(current, highest) > 0 ? current : highest;
        });
    }
    /**
     * Переходит к следующему игроку в торговле
     */
    moveToNextBidder() {
        this.gameState.currentPlayer = (this.gameState.currentPlayer + 1) % 3;
    }
    /**
     * Проверяет, завершена ли торговля
     */
    isBiddingComplete() {
        const activeBidders = this.gameState.players.filter(p => p.currentBid !== undefined);
        return activeBidders.length <= 1;
    }
    /**
     * Завершает торговлю и определяет контракт
     */
    finalizeBidding() {
        const highestBid = this.getHighestBid();
        if (!highestBid) {
            // Все спасовали - переходим к следующей сдаче
            this.gameState.currentPhase = 'finished';
            return;
        }
        // Определяем контракт
        const declarer = this.gameState.players.find(p => p.currentBid &&
            this.compareBids(p.currentBid, highestBid) === 0);
        if (!declarer) {
            throw new Error('No declarer found');
        }
        this.gameState.contract = {
            suit: highestBid.suit,
            level: highestBid.level,
            declarer: declarer.id,
            defenders: this.gameState.players.filter(p => p.id !== declarer.id).map(p => p.id),
            tricksNeeded: highestBid.level
        };
        declarer.declaredContract = this.gameState.contract;
        // Устанавливаем козырь
        if (highestBid.suit !== 'no_trump') {
            this.gameState.trumpSuit = highestBid.suit;
        }
        // Переходим к обмену с прикупом
        this.gameState.currentPhase = 'talon_exchange';
        this.gameState.currentPlayer = this.gameState.players.indexOf(declarer);
    }
    /**
     * Обменивается картами с прикупом
     */
    exchangeWithTalon(playerId, cardsToDiscard) {
        if (this.gameState.currentPhase !== 'talon_exchange') {
            return false;
        }
        const player = this.gameState.players[this.gameState.currentPlayer];
        if (!player || player.id !== playerId) {
            return false;
        }
        if (cardsToDiscard.length !== 2) {
            return false;
        }
        // Проверяем, что игрок имеет эти карты
        for (const card of cardsToDiscard) {
            const cardIndex = player.hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);
            if (cardIndex === -1) {
                return false;
            }
        }
        // Добавляем прикуп в руку
        player.hand.push(...this.gameState.talon);
        // Убираем выбранные карты
        for (const card of cardsToDiscard) {
            const cardIndex = player.hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);
            player.hand.splice(cardIndex, 1);
        }
        // Сортируем руку
        player.hand = this.sortPreferansHand(player.hand);
        // Переходим к розыгрышу
        this.gameState.currentPhase = 'playing';
        this.gameState.currentPlayer = (this.gameState.dealer + 1) % 3; // Ходит игрок слева от сдающего
        return true;
    }
    /**
     * Играет карту
     */
    playCard(playerId, card) {
        if (this.gameState.currentPhase !== 'playing') {
            return false;
        }
        const player = this.gameState.players[this.gameState.currentPlayer];
        if (!player || player.id !== playerId) {
            return false;
        }
        // Проверяем, что у игрока есть эта карта
        const cardIndex = player.hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);
        if (cardIndex === -1) {
            return false;
        }
        // Проверяем правила хода
        if (!this.isValidPlay(card, player)) {
            return false;
        }
        // Убираем карту из руки
        player.hand.splice(cardIndex, 1);
        // Добавляем карту в текущую взятку
        if (!this.gameState.currentTrick) {
            this.gameState.currentTrick = {
                cards: [],
                winner: '',
                leadSuit: card.suit
            };
        }
        this.gameState.currentTrick.cards.push({
            playerId: player.id,
            card
        });
        // Если взятка завершена
        if (this.gameState.currentTrick.cards.length === 3) {
            this.completeTrick();
        }
        else {
            // Переходим к следующему игроку
            this.gameState.currentPlayer = (this.gameState.currentPlayer + 1) % 3;
        }
        return true;
    }
    /**
     * Проверяет валидность хода
     */
    isValidPlay(card, player) {
        if (!this.gameState.currentTrick || this.gameState.currentTrick.cards.length === 0) {
            // Первый ход в взятке - любая карта
            return true;
        }
        const leadSuit = this.gameState.currentTrick.leadSuit;
        // Если есть карты заявленной масти, нужно ходить ей
        const hasSuit = player.hand.some(c => c.suit === leadSuit);
        if (hasSuit && card.suit !== leadSuit) {
            return false;
        }
        return true;
    }
    /**
     * Завершает взятку
     */
    completeTrick() {
        if (!this.gameState.currentTrick) {
            return;
        }
        // Определяем победителя взятки
        const winner = this.determineTrickWinner(this.gameState.currentTrick);
        this.gameState.currentTrick.winner = winner;
        // Добавляем взятку к выигранным взяткам игрока
        const winnerPlayer = this.gameState.players.find(p => p.id === winner);
        if (winnerPlayer) {
            winnerPlayer.tricksWon.push(this.gameState.currentTrick.cards.map(c => c.card));
        }
        // Сохраняем взятку
        this.gameState.tricks.push(this.gameState.currentTrick);
        this.gameState.currentTrick = null;
        // Проверяем, завершена ли сдача
        if (this.gameState.tricks.length === 10) {
            this.completeDeal();
        }
        else {
            // Следующую взятку начинает победитель
            this.gameState.currentPlayer = this.gameState.players.findIndex(p => p.id === winner);
        }
    }
    /**
     * Определяет победителя взятки
     */
    determineTrickWinner(trick) {
        const leadSuit = trick.leadSuit;
        const trumpSuit = this.gameState.trumpSuit;
        let winningCard = trick.cards[0];
        for (let i = 1; i < trick.cards.length; i++) {
            const currentCard = trick.cards[i];
            // Козырь бьет некозырь
            if (trumpSuit && currentCard.card.suit === trumpSuit && winningCard.card.suit !== trumpSuit) {
                winningCard = currentCard;
            }
            // Старший козырь бьет младший
            else if (trumpSuit && currentCard.card.suit === trumpSuit && winningCard.card.suit === trumpSuit) {
                if (this.compareCardRanks(currentCard.card.rank, winningCard.card.rank) > 0) {
                    winningCard = currentCard;
                }
            }
            // В масти - старшая карта
            else if (currentCard.card.suit === leadSuit && winningCard.card.suit === leadSuit) {
                if (this.compareCardRanks(currentCard.card.rank, winningCard.card.rank) > 0) {
                    winningCard = currentCard;
                }
            }
            // Карта заявленной масти бьет карту другой масти (если нет козыря)
            else if (currentCard.card.suit === leadSuit && winningCard.card.suit !== leadSuit &&
                (!trumpSuit || winningCard.card.suit !== trumpSuit)) {
                winningCard = currentCard;
            }
        }
        return winningCard.playerId;
    }
    /**
     * Сравнивает ранги карт в преферансе
     */
    compareCardRanks(rank1, rank2) {
        const rankOrder = ['7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        return rankOrder.indexOf(rank1) - rankOrder.indexOf(rank2);
    }
    /**
     * Завершает сдачу и подсчитывает очки
     */
    completeDeal() {
        if (!this.gameState.contract) {
            this.gameState.currentPhase = 'finished';
            return;
        }
        const declarer = this.gameState.players.find(p => p.id === this.gameState.contract.declarer);
        if (!declarer) {
            return;
        }
        const tricksWon = declarer.tricksWon.length;
        const tricksNeeded = this.gameState.contract.tricksNeeded;
        const contractSuccess = tricksWon >= tricksNeeded;
        // Подсчитываем очки
        const contractResult = {
            contract: this.gameState.contract,
            tricksWon,
            success: contractSuccess,
            points: this.calculateContractPoints(this.gameState.contract, tricksWon, contractSuccess),
            round: this.gameState.round
        };
        declarer.score.contracts.push(contractResult);
        if (contractSuccess) {
            // Контракт выполнен
            declarer.score.mountain += contractResult.points;
        }
        else {
            // Контракт провален
            declarer.score.bullet += 1;
        }
        // Обновляем общий счет
        this.updateTotalScores();
        this.gameState.currentPhase = 'scoring';
        // Проверяем, завершена ли игра
        if (this.isGameFinished()) {
            this.gameState.currentPhase = 'finished';
        }
        else {
            // Переходим к следующей сдаче
            this.gameState.round++;
            this.gameState.dealer = (this.gameState.dealer + 1) % 3;
        }
    }
    /**
     * Вычисляет очки за контракт
     */
    calculateContractPoints(contract, tricksWon, success) {
        const basePoints = {
            6: 20, 7: 40, 8: 60, 9: 80, 10: 100
        };
        let points = basePoints[contract.level] || 0;
        // Бонус за масть
        if (contract.suit === 'no_trump') {
            points += 10;
        }
        // Бонус за перебор
        if (success && tricksWon > contract.tricksNeeded) {
            points += (tricksWon - contract.tricksNeeded) * 10;
        }
        return success ? points : 0;
    }
    /**
     * Обновляет общие счета игроков
     */
    updateTotalScores() {
        this.gameState.players.forEach(player => {
            player.score.totalScore = player.score.mountain - (player.score.bullet * 10) + player.score.pool;
        });
    }
    /**
     * Проверяет, завершена ли игра
     */
    isGameFinished() {
        // Игра до 10 пуль или до заданного количества раундов
        return this.gameState.round >= this.maxRounds ||
            this.gameState.players.some(p => p.score.bullet >= 10);
    }
    // Геттеры
    getGameState() {
        return { ...this.gameState };
    }
    getPublicGameState(playerId) {
        const publicState = {
            ...this.gameState,
            players: this.gameState.players.map(player => ({
                ...player,
                hand: player.id === playerId ? player.hand : player.hand.map(() => ({ suit: 'hidden', rank: 'hidden' }))
            })),
            deck: undefined, // Скрываем колоду
            talon: this.gameState.currentPhase === 'talon_exchange' &&
                this.gameState.players[this.gameState.currentPlayer]?.id === playerId ?
                this.gameState.talon : []
        };
        return publicState;
    }
    canPlayerAct(playerId) {
        const currentPlayer = this.gameState.players[this.gameState.currentPlayer];
        return currentPlayer?.id === playerId;
    }
    getAvailableActions(playerId) {
        if (!this.canPlayerAct(playerId)) {
            return [];
        }
        switch (this.gameState.currentPhase) {
            case 'bidding':
                return ['bid', 'pass'];
            case 'talon_exchange':
                return ['exchange'];
            case 'playing':
                return ['play_card'];
            default:
                return [];
        }
    }
}
exports.PreferansGame = PreferansGame;
//# sourceMappingURL=PreferansGame.js.map