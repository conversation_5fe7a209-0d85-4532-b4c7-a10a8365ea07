import { Card } from '../../types/Card';
export interface BlackjackPlayer {
    id: string;
    name: string;
    chips: number;
    hands: BlackjackHand[];
    currentBet: number;
    insurance: number;
    isActive: boolean;
    hasPlayed: boolean;
}
export interface BlackjackHand {
    cards: Card[];
    value: number;
    isBusted: boolean;
    isBlackjack: boolean;
    isStanding: boolean;
    bet: number;
    canSplit: boolean;
    canDouble: boolean;
}
export interface BlackjackGameState {
    id: string;
    players: BlackjackPlayer[];
    dealer: {
        cards: Card[];
        value: number;
        hiddenCard: boolean;
        isBusted: boolean;
    };
    deck: Card[];
    phase: 'betting' | 'dealing' | 'playing' | 'dealer_turn' | 'finished';
    currentPlayerIndex: number;
    currentHandIndex: number;
    minBet: number;
    maxBet: number;
    results: Array<{
        playerId: string;
        handIndex: number;
        result: 'win' | 'lose' | 'push' | 'blackjack';
        payout: number;
    }>;
    handNumber: number;
}
export declare class BlackjackGame {
    private gameState;
    private readonly maxPlayers;
    constructor(gameId: string, minBet?: number, maxBet?: number);
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId: string, playerName: string, chips?: number): boolean;
    /**
     * Размещает ставку
     */
    placeBet(playerId: string, amount: number): boolean;
    /**
     * Начинает новую руку
     */
    startNewHand(): void;
    /**
     * Раздает начальные карты
     */
    private dealInitialCards;
    /**
     * Выполняет действие игрока
     */
    makeAction(playerId: string, action: 'hit' | 'stand' | 'double' | 'split' | 'insurance'): boolean;
    /**
     * Взять карту
     */
    private hit;
    /**
     * Остановиться
     */
    private stand;
    /**
     * Удвоить ставку
     */
    private double;
    /**
     * Разделить пару
     */
    private split;
    /**
     * Страховка
     */
    private insurance;
    /**
     * Ход дилера
     */
    private dealerTurn;
    /**
     * Определяет результаты
     */
    private determineResults;
    private createDeck;
    private shuffleDeck;
    private dealCard;
    private calculateHandValue;
    private updateHandValue;
    private checkSplitAndDouble;
    private checkBlackjacks;
    private getActivePlayers;
    private getCurrentPlayer;
    private moveToNextHand;
    private findNextPlayer;
    getGameState(): BlackjackGameState;
    getPublicGameState(playerId?: string): any;
    canPlayerAct(playerId: string): boolean;
    getAvailableActions(playerId: string): string[];
}
//# sourceMappingURL=BlackjackGame.d.ts.map