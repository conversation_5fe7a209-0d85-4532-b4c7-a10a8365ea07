"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlackjackGame = void 0;
const Card_1 = require("../../types/Card");
class BlackjackGame {
    constructor(gameId, minBet = 10, maxBet = 1000) {
        this.maxPlayers = 6;
        this.gameState = {
            id: gameId,
            players: [],
            dealer: {
                cards: [],
                value: 0,
                hiddenCard: true,
                isBusted: false
            },
            deck: [],
            phase: 'betting',
            currentPlayerIndex: 0,
            currentHandIndex: 0,
            minBet,
            maxBet,
            results: [],
            handNumber: 1
        };
    }
    /**
     * Добавляет игрока в игру
     */
    addPlayer(playerId, playerName, chips = 1000) {
        if (this.gameState.players.length >= this.maxPlayers) {
            return false;
        }
        if (this.gameState.players.some(p => p.id === playerId)) {
            return false;
        }
        const player = {
            id: playerId,
            name: player<PERSON><PERSON>,
            chips,
            hands: [],
            currentBet: 0,
            insurance: 0,
            isActive: true,
            hasPlayed: false
        };
        this.gameState.players.push(player);
        return true;
    }
    /**
     * Размещает ставку
     */
    placeBet(playerId, amount) {
        if (this.gameState.phase !== 'betting') {
            return false;
        }
        const player = this.gameState.players.find(p => p.id === playerId);
        if (!player) {
            return false;
        }
        if (amount < this.gameState.minBet || amount > this.gameState.maxBet) {
            return false;
        }
        if (player.chips < amount) {
            return false;
        }
        player.currentBet = amount;
        player.chips -= amount;
        player.isActive = true;
        return true;
    }
    /**
     * Начинает новую руку
     */
    startNewHand() {
        if (this.gameState.players.filter(p => p.currentBet > 0).length === 0) {
            throw new Error('No players with bets');
        }
        // Создаем и перемешиваем колоду
        this.createDeck();
        this.shuffleDeck();
        // Сброс состояния
        this.gameState.dealer.cards = [];
        this.gameState.dealer.value = 0;
        this.gameState.dealer.hiddenCard = true;
        this.gameState.dealer.isBusted = false;
        this.gameState.results = [];
        this.gameState.currentPlayerIndex = 0;
        this.gameState.currentHandIndex = 0;
        // Инициализация рук игроков
        this.gameState.players.forEach(player => {
            player.hands = [];
            player.hasPlayed = false;
            if (player.currentBet > 0) {
                const hand = {
                    cards: [],
                    value: 0,
                    isBusted: false,
                    isBlackjack: false,
                    isStanding: false,
                    bet: player.currentBet,
                    canSplit: false,
                    canDouble: true
                };
                player.hands.push(hand);
            }
        });
        this.gameState.phase = 'dealing';
        this.dealInitialCards();
    }
    /**
     * Раздает начальные карты
     */
    dealInitialCards() {
        const activePlayers = this.getActivePlayers();
        // Раздаем по 2 карты каждому игроку и дилеру
        for (let round = 0; round < 2; round++) {
            // Карты игрокам
            activePlayers.forEach(player => {
                if (player.hands.length > 0) {
                    const card = this.dealCard();
                    player.hands[0].cards.push(card);
                }
            });
            // Карта дилеру
            const dealerCard = this.dealCard();
            this.gameState.dealer.cards.push(dealerCard);
        }
        // Обновляем значения рук
        activePlayers.forEach(player => {
            if (player.hands.length > 0) {
                this.updateHandValue(player.hands[0]);
                this.checkSplitAndDouble(player.hands[0]);
            }
        });
        this.gameState.dealer.value = this.calculateHandValue(this.gameState.dealer.cards);
        // Проверяем блэкджеки
        this.checkBlackjacks();
        this.gameState.phase = 'playing';
        this.findNextPlayer();
    }
    /**
     * Выполняет действие игрока
     */
    makeAction(playerId, action) {
        if (this.gameState.phase !== 'playing') {
            return false;
        }
        const player = this.getCurrentPlayer();
        if (!player || player.id !== playerId) {
            return false;
        }
        const hand = player.hands[this.gameState.currentHandIndex];
        if (!hand || hand.isStanding || hand.isBusted) {
            return false;
        }
        switch (action) {
            case 'hit':
                return this.hit(player, hand);
            case 'stand':
                return this.stand(player, hand);
            case 'double':
                return this.double(player, hand);
            case 'split':
                return this.split(player, hand);
            case 'insurance':
                return this.insurance(player);
            default:
                return false;
        }
    }
    /**
     * Взять карту
     */
    hit(player, hand) {
        const card = this.dealCard();
        hand.cards.push(card);
        this.updateHandValue(hand);
        if (hand.isBusted) {
            this.moveToNextHand();
        }
        return true;
    }
    /**
     * Остановиться
     */
    stand(player, hand) {
        hand.isStanding = true;
        this.moveToNextHand();
        return true;
    }
    /**
     * Удвоить ставку
     */
    double(player, hand) {
        if (!hand.canDouble || player.chips < hand.bet) {
            return false;
        }
        player.chips -= hand.bet;
        hand.bet *= 2;
        const card = this.dealCard();
        hand.cards.push(card);
        this.updateHandValue(hand);
        hand.isStanding = true;
        this.moveToNextHand();
        return true;
    }
    /**
     * Разделить пару
     */
    split(player, hand) {
        if (!hand.canSplit || player.chips < hand.bet) {
            return false;
        }
        // Создаем новую руку
        const newHand = {
            cards: [hand.cards.pop()],
            value: 0,
            isBusted: false,
            isBlackjack: false,
            isStanding: false,
            bet: hand.bet,
            canSplit: false,
            canDouble: true
        };
        player.hands.push(newHand);
        player.chips -= hand.bet;
        // Добавляем по карте в каждую руку
        hand.cards.push(this.dealCard());
        newHand.cards.push(this.dealCard());
        this.updateHandValue(hand);
        this.updateHandValue(newHand);
        this.checkSplitAndDouble(hand);
        this.checkSplitAndDouble(newHand);
        return true;
    }
    /**
     * Страховка
     */
    insurance(player) {
        if (this.gameState.dealer.cards[0].rank !== 'A') {
            return false;
        }
        const insuranceAmount = Math.floor(player.currentBet / 2);
        if (player.chips < insuranceAmount) {
            return false;
        }
        player.insurance = insuranceAmount;
        player.chips -= insuranceAmount;
        return true;
    }
    /**
     * Ход дилера
     */
    dealerTurn() {
        this.gameState.dealer.hiddenCard = false;
        this.gameState.phase = 'dealer_turn';
        // Дилер берет карты до 17
        while (this.gameState.dealer.value < 17) {
            const card = this.dealCard();
            this.gameState.dealer.cards.push(card);
            this.gameState.dealer.value = this.calculateHandValue(this.gameState.dealer.cards);
        }
        if (this.gameState.dealer.value > 21) {
            this.gameState.dealer.isBusted = true;
        }
        this.determineResults();
    }
    /**
     * Определяет результаты
     */
    determineResults() {
        const dealerValue = this.gameState.dealer.value;
        const dealerBlackjack = this.gameState.dealer.cards.length === 2 && dealerValue === 21;
        this.gameState.players.forEach(player => {
            player.hands.forEach((hand, handIndex) => {
                if (hand.bet === 0)
                    return;
                let result;
                let payout = 0;
                if (hand.isBusted) {
                    result = 'lose';
                    payout = 0;
                }
                else if (hand.isBlackjack && !dealerBlackjack) {
                    result = 'blackjack';
                    payout = hand.bet * 2.5; // 3:2 выплата
                }
                else if (dealerBlackjack && !hand.isBlackjack) {
                    result = 'lose';
                    payout = 0;
                }
                else if (hand.isBlackjack && dealerBlackjack) {
                    result = 'push';
                    payout = hand.bet;
                }
                else if (this.gameState.dealer.isBusted) {
                    result = 'win';
                    payout = hand.bet * 2;
                }
                else if (hand.value > dealerValue) {
                    result = 'win';
                    payout = hand.bet * 2;
                }
                else if (hand.value < dealerValue) {
                    result = 'lose';
                    payout = 0;
                }
                else {
                    result = 'push';
                    payout = hand.bet;
                }
                player.chips += payout;
                this.gameState.results.push({
                    playerId: player.id,
                    handIndex,
                    result,
                    payout
                });
            });
            // Обработка страховки
            if (player.insurance > 0) {
                if (dealerBlackjack) {
                    player.chips += player.insurance * 3; // 2:1 выплата
                }
                player.insurance = 0;
            }
            // Сброс ставки
            player.currentBet = 0;
        });
        this.gameState.phase = 'finished';
    }
    // Утилиты
    createDeck() {
        this.gameState.deck = Card_1.CardUtils.createDeck();
    }
    shuffleDeck() {
        this.gameState.deck = Card_1.CardUtils.shuffleDeck(this.gameState.deck);
    }
    dealCard() {
        if (this.gameState.deck.length === 0) {
            this.createDeck();
            this.shuffleDeck();
        }
        return this.gameState.deck.pop();
    }
    calculateHandValue(cards) {
        let value = 0;
        let aces = 0;
        for (const card of cards) {
            if (card.rank === 'A') {
                aces++;
                value += 11;
            }
            else if (['K', 'Q', 'J'].includes(card.rank)) {
                value += 10;
            }
            else {
                value += parseInt(card.rank);
            }
        }
        // Обработка тузов
        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }
        return value;
    }
    updateHandValue(hand) {
        hand.value = this.calculateHandValue(hand.cards);
        hand.isBusted = hand.value > 21;
        hand.isBlackjack = hand.cards.length === 2 && hand.value === 21;
    }
    checkSplitAndDouble(hand) {
        hand.canDouble = hand.cards.length === 2;
        hand.canSplit = hand.cards.length === 2 &&
            Card_1.CardUtils.getCardValue(hand.cards[0].rank) === Card_1.CardUtils.getCardValue(hand.cards[1].rank);
    }
    checkBlackjacks() {
        this.gameState.players.forEach(player => {
            player.hands.forEach(hand => {
                if (hand.isBlackjack) {
                    hand.isStanding = true;
                }
            });
        });
    }
    getActivePlayers() {
        return this.gameState.players.filter(p => p.currentBet > 0);
    }
    getCurrentPlayer() {
        const activePlayers = this.getActivePlayers();
        return activePlayers[this.gameState.currentPlayerIndex] || null;
    }
    moveToNextHand() {
        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer) {
            this.dealerTurn();
            return;
        }
        this.gameState.currentHandIndex++;
        if (this.gameState.currentHandIndex >= currentPlayer.hands.length) {
            this.gameState.currentHandIndex = 0;
            this.gameState.currentPlayerIndex++;
            this.findNextPlayer();
        }
    }
    findNextPlayer() {
        const activePlayers = this.getActivePlayers();
        while (this.gameState.currentPlayerIndex < activePlayers.length) {
            const player = activePlayers[this.gameState.currentPlayerIndex];
            const hand = player.hands[this.gameState.currentHandIndex];
            if (hand && !hand.isStanding && !hand.isBusted && !hand.isBlackjack) {
                return;
            }
            this.moveToNextHand();
        }
        this.dealerTurn();
    }
    // Геттеры
    getGameState() {
        return { ...this.gameState };
    }
    getPublicGameState(playerId) {
        const publicState = {
            ...this.gameState,
            dealer: {
                ...this.gameState.dealer,
                cards: this.gameState.dealer.hiddenCard && this.gameState.phase === 'playing' ?
                    [this.gameState.dealer.cards[0], { suit: 'hidden', rank: 'hidden' }] :
                    this.gameState.dealer.cards
            },
            deck: undefined // Скрываем колоду
        };
        return publicState;
    }
    canPlayerAct(playerId) {
        const currentPlayer = this.getCurrentPlayer();
        return currentPlayer?.id === playerId && this.gameState.phase === 'playing';
    }
    getAvailableActions(playerId) {
        if (!this.canPlayerAct(playerId)) {
            return [];
        }
        const player = this.getCurrentPlayer();
        const hand = player.hands[this.gameState.currentHandIndex];
        const actions = ['hit', 'stand'];
        if (hand.canDouble && player.chips >= hand.bet) {
            actions.push('double');
        }
        if (hand.canSplit && player.chips >= hand.bet) {
            actions.push('split');
        }
        if (this.gameState.dealer.cards[0].rank === 'A' && player.insurance === 0) {
            actions.push('insurance');
        }
        return actions;
    }
}
exports.BlackjackGame = BlackjackGame;
//# sourceMappingURL=BlackjackGame.js.map