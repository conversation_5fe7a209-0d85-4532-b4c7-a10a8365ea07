import { BlackjackGame, BlackjackGameState } from './BlackjackGame';
import { Player } from '../../players/PlayerManager';
export interface BlackjackTable {
    id: string;
    name: string;
    game: BlackjackGame;
    players: Player[];
    maxPlayers: number;
    minBet: number;
    maxBet: number;
    isPrivate: boolean;
    password?: string;
    status: 'waiting' | 'betting' | 'playing' | 'finished';
    createdAt: Date;
    createdBy: string;
    autoStart: boolean;
    roundTimer: number;
}
export declare class BlackjackManager {
    private tables;
    private playerTables;
    /**
     * Создает новый стол для блэкджека
     */
    createTable(creator: Player, tableName: string, minBet?: number, maxBet?: number, maxPlayers?: number, isPrivate?: boolean, password?: string, autoStart?: boolean): BlackjackTable;
    /**
     * Присоединяется к столу
     */
    joinTable(tableId: string, player: Player, password?: string): boolean;
    /**
     * Покидает стол
     */
    leaveTable(tableId: string, player: Player): boolean;
    /**
     * Размещает ставку
     */
    placeBet(tableId: string, playerId: string, amount: number): BlackjackGameState;
    /**
     * Выполняет действие в блэкджеке
     */
    makeAction(tableId: string, playerId: string, action: 'hit' | 'stand' | 'double' | 'split' | 'insurance'): BlackjackGameState;
    /**
     * Начинает игру вручную
     */
    startGame(tableId: string, playerId: string): BlackjackGameState;
    /**
     * Получает состояние игры
     */
    getGameState(tableId: string, playerId?: string): BlackjackGameState | null;
    /**
     * Получает информацию о столе
     */
    getTable(tableId: string): BlackjackTable | undefined;
    /**
     * Получает список публичных столов
     */
    getPublicTables(): BlackjackTable[];
    /**
     * Получает столы игрока
     */
    getPlayerTables(playerId: string): BlackjackTable[];
    /**
     * Получает статистику блэкджека
     */
    getStats(): {
        totalTables: number;
        activeGames: number;
        waitingTables: number;
        totalPlayers: number;
        betLimits: Record<string, number>;
    };
    /**
     * Очищает завершенные игры
     */
    cleanup(): number;
    /**
     * Получает публичную информацию о столе
     */
    getTablePublicInfo(table: BlackjackTable): any;
    /**
     * Проверяет, может ли игрок выполнить действие
     */
    canPlayerAct(tableId: string, playerId: string): boolean;
    /**
     * Получает доступные действия для игрока
     */
    getAvailableActions(tableId: string, playerId: string): string[];
    /**
     * Получает рекомендуемые ставки
     */
    getSuggestedBets(tableId: string): number[];
    private generateTableId;
    private allPlayersBet;
    private resetBets;
}
//# sourceMappingURL=BlackjackManager.d.ts.map