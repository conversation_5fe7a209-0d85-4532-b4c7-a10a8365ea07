"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlackjackManager = void 0;
const BlackjackGame_1 = require("./BlackjackGame");
class BlackjackManager {
    constructor() {
        this.tables = new Map();
        this.playerTables = new Map(); // playerId -> tableId
    }
    /**
     * Создает новый стол для блэкджека
     */
    createTable(creator, tableName, minBet = 10, maxBet = 1000, maxPlayers = 6, isPrivate = false, password, autoStart = true) {
        const tableId = this.generateTableId();
        const game = new BlackjackGame_1.BlackjackGame(tableId, minBet, maxBet);
        const table = {
            id: tableId,
            name: tableName,
            game,
            players: [],
            maxPlayers,
            minBet,
            maxBet,
            isPrivate,
            password,
            status: 'waiting',
            createdAt: new Date(),
            createdBy: creator.id,
            autoStart,
            roundTimer: 30
        };
        this.tables.set(tableId, table);
        // Автоматически добавляем создателя за стол
        this.joinTable(tableId, creator);
        return table;
    }
    /**
     * Присоединяется к столу
     */
    joinTable(tableId, player, password) {
        const table = this.tables.get(tableId);
        if (!table) {
            throw new Error('Table not found');
        }
        if (table.players.length >= table.maxPlayers) {
            throw new Error('Table is full');
        }
        if (table.isPrivate && table.password !== password) {
            throw new Error('Invalid password');
        }
        if (table.players.some(p => p.id === player.id)) {
            throw new Error('Player already at table');
        }
        // Проверяем, не сидит ли игрок за другим столом
        const currentTable = this.playerTables.get(player.id);
        if (currentTable) {
            this.leaveTable(currentTable, player);
        }
        table.players.push(player);
        this.playerTables.set(player.id, tableId);
        // Добавляем игрока в игру
        table.game.addPlayer(player.id, player.name);
        return true;
    }
    /**
     * Покидает стол
     */
    leaveTable(tableId, player) {
        const table = this.tables.get(tableId);
        if (!table) {
            return false;
        }
        const playerIndex = table.players.findIndex(p => p.id === player.id);
        if (playerIndex === -1) {
            return false;
        }
        table.players.splice(playerIndex, 1);
        this.playerTables.delete(player.id);
        // Если стол пустой, удаляем его
        if (table.players.length === 0) {
            this.tables.delete(tableId);
        }
        return true;
    }
    /**
     * Размещает ставку
     */
    placeBet(tableId, playerId, amount) {
        const table = this.tables.get(tableId);
        if (!table) {
            throw new Error('Table not found');
        }
        if (table.status !== 'betting' && table.status !== 'waiting') {
            throw new Error('Betting not allowed at this time');
        }
        const success = table.game.placeBet(playerId, amount);
        if (!success) {
            throw new Error('Invalid bet');
        }
        // Если все игроки сделали ставки, начинаем игру
        if (table.autoStart && this.allPlayersBet(table)) {
            table.status = 'playing';
            table.game.startNewHand();
        }
        return table.game.getGameState();
    }
    /**
     * Выполняет действие в блэкджеке
     */
    makeAction(tableId, playerId, action) {
        const table = this.tables.get(tableId);
        if (!table) {
            throw new Error('Table not found');
        }
        if (table.status !== 'playing') {
            throw new Error('Game not in progress');
        }
        const success = table.game.makeAction(playerId, action);
        if (!success) {
            throw new Error('Invalid action');
        }
        const gameState = table.game.getGameState();
        // Проверяем, завершилась ли игра
        if (gameState.phase === 'finished') {
            table.status = 'finished';
            // Автоматически начинаем новый раунд через некоторое время
            if (table.autoStart) {
                setTimeout(() => {
                    if (table.players.length > 0) {
                        table.status = 'betting';
                        // Сброс ставок для нового раунда
                        this.resetBets(table);
                    }
                }, 5000); // 5 секунд между раундами
            }
        }
        return gameState;
    }
    /**
     * Начинает игру вручную
     */
    startGame(tableId, playerId) {
        const table = this.tables.get(tableId);
        if (!table) {
            throw new Error('Table not found');
        }
        if (table.createdBy !== playerId) {
            throw new Error('Only table creator can start the game');
        }
        if (table.status !== 'betting' && table.status !== 'waiting') {
            throw new Error('Cannot start game at this time');
        }
        // Проверяем, что есть игроки со ставками
        const gameState = table.game.getGameState();
        const playersWithBets = gameState.players.filter(p => p.currentBet > 0);
        if (playersWithBets.length === 0) {
            throw new Error('No players with bets');
        }
        table.status = 'playing';
        table.game.startNewHand();
        return table.game.getGameState();
    }
    /**
     * Получает состояние игры
     */
    getGameState(tableId, playerId) {
        const table = this.tables.get(tableId);
        if (!table) {
            return null;
        }
        return table.game.getPublicGameState(playerId);
    }
    /**
     * Получает информацию о столе
     */
    getTable(tableId) {
        return this.tables.get(tableId);
    }
    /**
     * Получает список публичных столов
     */
    getPublicTables() {
        return Array.from(this.tables.values())
            .filter(table => !table.isPrivate)
            .map(table => ({
            ...table,
            game: undefined // Не передаем игровое состояние в списке
        }));
    }
    /**
     * Получает столы игрока
     */
    getPlayerTables(playerId) {
        const tableId = this.playerTables.get(playerId);
        if (!tableId) {
            return [];
        }
        const table = this.tables.get(tableId);
        return table ? [table] : [];
    }
    /**
     * Получает статистику блэкджека
     */
    getStats() {
        const totalTables = this.tables.size;
        const activeGames = Array.from(this.tables.values()).filter(table => table.status === 'playing').length;
        const waitingTables = Array.from(this.tables.values()).filter(table => table.status === 'waiting' || table.status === 'betting').length;
        const totalPlayers = Array.from(this.tables.values()).reduce((sum, table) => sum + table.players.length, 0);
        const betLimits = Array.from(this.tables.values()).reduce((acc, table) => {
            const key = `${table.minBet}-${table.maxBet}`;
            acc[key] = (acc[key] || 0) + 1;
            return acc;
        }, {});
        return {
            totalTables,
            activeGames,
            waitingTables,
            totalPlayers,
            betLimits
        };
    }
    /**
     * Очищает завершенные игры
     */
    cleanup() {
        let cleaned = 0;
        const now = Date.now();
        const maxAge = 60 * 60 * 1000; // 1 час
        for (const [tableId, table] of this.tables.entries()) {
            // Удаляем старые завершенные игры без игроков
            if (table.status === 'finished' &&
                table.players.length === 0 &&
                now - table.createdAt.getTime() > maxAge) {
                this.tables.delete(tableId);
                cleaned++;
            }
        }
        return cleaned;
    }
    /**
     * Получает публичную информацию о столе
     */
    getTablePublicInfo(table) {
        return {
            id: table.id,
            name: table.name,
            playerCount: table.players.length,
            maxPlayers: table.maxPlayers,
            minBet: table.minBet,
            maxBet: table.maxBet,
            status: table.status,
            isPrivate: table.isPrivate,
            autoStart: table.autoStart,
            createdAt: table.createdAt,
            players: table.players.map(player => ({
                id: player.id,
                name: player.name
            }))
        };
    }
    /**
     * Проверяет, может ли игрок выполнить действие
     */
    canPlayerAct(tableId, playerId) {
        const table = this.tables.get(tableId);
        if (!table || table.status !== 'playing') {
            return false;
        }
        return table.game.canPlayerAct(playerId);
    }
    /**
     * Получает доступные действия для игрока
     */
    getAvailableActions(tableId, playerId) {
        const table = this.tables.get(tableId);
        if (!table || table.status !== 'playing') {
            return [];
        }
        return table.game.getAvailableActions(playerId);
    }
    /**
     * Получает рекомендуемые ставки
     */
    getSuggestedBets(tableId) {
        const table = this.tables.get(tableId);
        if (!table) {
            return [];
        }
        const suggestions = [];
        const min = table.minBet;
        const max = table.maxBet;
        // Минимальная ставка
        suggestions.push(min);
        // Стандартные размеры
        const standardSizes = [25, 50, 100, 250, 500];
        standardSizes.forEach(size => {
            if (size >= min && size <= max && !suggestions.includes(size)) {
                suggestions.push(size);
            }
        });
        // Максимальная ставка
        if (!suggestions.includes(max)) {
            suggestions.push(max);
        }
        return suggestions.slice(0, 6); // Максимум 6 предложений
    }
    // Утилиты
    generateTableId() {
        return 'blackjack_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    allPlayersBet(table) {
        const gameState = table.game.getGameState();
        return table.players.every(player => {
            const gamePlayer = gameState.players.find(p => p.id === player.id);
            return gamePlayer && gamePlayer.currentBet > 0;
        });
    }
    resetBets(table) {
        const gameState = table.game.getGameState();
        gameState.players.forEach(player => {
            player.currentBet = 0;
            player.hands = [];
            player.hasPlayed = false;
        });
    }
}
exports.BlackjackManager = BlackjackManager;
//# sourceMappingURL=BlackjackManager.js.map