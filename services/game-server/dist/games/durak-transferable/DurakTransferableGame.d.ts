import { Card } from '../../types/Card';
import { DurakGame, DurakPlayer, DurakGameState, DefenseCard } from '../durak/DurakGame';
export interface TransferablePlayer extends DurakPlayer {
    canTransfer: boolean;
    transferredCards: Card[];
}
export interface TransferableGameState extends DurakGameState {
    players: TransferablePlayer[];
    transferPhase: boolean;
    transferTarget?: string;
    originalAttacker?: string;
    transferHistory: TransferMove[];
}
export interface TransferMove {
    fromPlayer: string;
    toPlayer: string;
    cards: Card[];
    timestamp: Date;
}
export declare class DurakTransferableGame extends DurakGame {
    protected gameState: TransferableGameState;
    constructor(gameId: string);
    /**
     * Добавляет игрока в игру (переопределяем для TransferablePlayer)
     */
    addPlayer(playerId: string, playerName: string): boolean;
    /**
     * Начинает новую игру (переопределяем для сброса переводных полей)
     */
    startNewGame(): void;
    /**
     * Начинает новый раунд (переопределяем для переводного дурака)
     */
    protected startNewRound(): void;
    /**
     * Атакует картами (переопределяем для поддержки перевода)
     */
    attack(playerId: string, cards: Card[]): boolean;
    /**
     * Защищается картами (переопределяем для поддержки перевода)
     */
    defend(playerId: string, defenses: DefenseCard[]): boolean;
    /**
     * Переводит карты на следующего игрока
     */
    transfer(playerId: string, cards: Card[]): boolean;
    /**
     * Проверяет валидность перевода
     */
    private isValidTransfer;
    /**
     * Выполняет перевод карт
     */
    private executeTransfer;
    /**
     * Получает следующего активного игрока
     */
    private getNextActivePlayer;
    /**
     * Берет карты (переопределяем для обработки перевода)
     */
    takeCards(playerId: string): boolean;
    /**
     * Завершает ход (переопределяем для обработки перевода)
     */
    endTurn(playerId: string): boolean;
    /**
     * Получает доступные действия для игрока (переопределяем)
     */
    getAvailableActions(playerId: string): string[];
    /**
     * Получает карты, которые можно использовать для перевода
     */
    getTransferableCards(playerId: string): Card[];
    /**
     * Проверяет, может ли игрок переводить
     */
    canPlayerTransfer(playerId: string): boolean;
    /**
     * Получает историю переводов в текущем раунде
     */
    getTransferHistory(): TransferMove[];
    /**
     * Получает публичное состояние игры (переопределяем)
     */
    getPublicGameState(playerId?: string): any;
    /**
     * Получает статистику переводов
     */
    getTransferStats(): any;
}
//# sourceMappingURL=DurakTransferableGame.d.ts.map