{"version": 3, "file": "DurakTransferableGame.js", "sourceRoot": "", "sources": ["../../../src/games/durak-transferable/DurakTransferableGame.ts"], "names": [], "mappings": ";;;AACA,kDAAqG;AAsBrG,MAAa,qBAAsB,SAAQ,qBAAS;IAGlD,YAAY,MAAc;QACxB,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,+CAA+C;QAC/C,IAAI,CAAC,SAAS,GAAG;YACf,GAAG,IAAI,CAAC,SAAS;YACjB,aAAa,EAAE,KAAK;YACpB,eAAe,EAAE,EAAE;SACK,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,QAAgB,EAAE,UAAkB;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACvC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,MAAM,GAAuB;YACjC,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,KAAK;YAClB,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,KAAK;YACpB,WAAW,EAAE,KAAK;YAClB,gBAAgB,EAAE,EAAE;SACrB,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,YAAY;QACV,KAAK,CAAC,YAAY,EAAE,CAAC;QAErB,6BAA6B;QAC7B,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAC5C,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,CAAC;QAEpC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrC,MAA6B,CAAC,WAAW,GAAG,KAAK,CAAC;YAClD,MAA6B,CAAC,gBAAgB,GAAG,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,KAAK,CAAC,aAAa,EAAE,CAAC;QAEtB,+CAA+C;QAC/C,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAE5C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrC,MAA6B,CAAC,WAAW,GAAG,KAAK,CAAC;YAClD,MAA6B,CAAC,gBAAgB,GAAG,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,oDAAoD;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,QAAQ,EAAE,CAAC;YACZ,QAA+B,CAAC,WAAW,GAAG,IAAI,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAgB,EAAE,KAAa;QACpC,4CAA4C;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAgB,EAAE,QAAuB;QAC9C,6CAA6C;QAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,QAAgB,EAAE,KAAa;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;QACzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,yCAAyC;QACzC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC;gBAChC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,gEAAgE;QAChE,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4DAA4D;QAC5D,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5E,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,kBAAkB,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAa;QACnC,iEAAiE;QACjE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAElE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,uEAAuE;QACvE,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,UAA8B,EAAE,QAA4B,EAAE,KAAa;QACjG,qCAAqC;QACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAC7C,CAAC;YACF,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,UAAU,CAAC,EAAE;aACxB,CAAC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAE3C,MAAM,YAAY,GAAiB;YACjC,UAAU,EAAE,UAAU,CAAC,EAAE;YACzB,QAAQ,EAAE,QAAQ,CAAC,EAAE;YACrB,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAElD,sBAAsB;QACtB,UAAU,CAAC,WAAW,GAAG,KAAK,CAAC;QAC/B,UAAU,CAAC,WAAW,GAAG,KAAK,CAAC;QAE/B,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,QAA+B,CAAC,WAAW,GAAG,IAAI,CAAC;QAEpD,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,QAAQ,CAAC,EAAE,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,CAAC;QAC3D,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,EAAE,CAAC;QAE3C,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;YAC1B,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE,UAAU,CAAC,EAAE;YACvB,UAAU,EAAE,UAAU,CAAC,IAAI;YAC3B,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;YACjB,YAAY,EAAE,QAAQ,CAAC,IAAI;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,aAAiC;QAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACnE,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;YACrE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAuB,CAAC;YAE3E,IAAI,UAAU,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACnD,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,QAAgB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;QACzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2EAA2E;QAC3E,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,2CAA2C;YAC3C,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;QAC7B,CAAC;QAED,oCAAoC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,MAAM,EAAE,CAAC;YACX,gCAAgC;YAChC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAChC,CAAwB,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC7C,CAAwB,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAgB;QACtB,sEAAsE;QACtE,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;YACzF,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;gBACjC,yEAAyE;gBACzE,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;gBAC3B,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAgB;QAClC,MAAM,WAAW,GAAG,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;QAEzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,2DAA2D;QAC3D,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChE,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAgB;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;QACzF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAuB,CAAC;QACzF,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAiB;QAClC,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEvD,OAAO;YACL,GAAG,WAAW;YACd,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa;YAC3C,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc;YAC7C,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAC/C,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC3C,MAAM,kBAAkB,GAAG,MAA4B,CAAC;gBACxD,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;oBACxG,WAAW,EAAE,kBAAkB,CAAC,WAAW;oBAC3C,gBAAgB,EAAE,kBAAkB,CAAC,gBAAgB;iBACtD,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,KAAK,GAAG;YACZ,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM;YACrD,iBAAiB,EAAE,EAA4B;YAC/C,mBAAmB,EAAE,IAAmB;YACxC,mBAAmB,EAAE,CAAC;SACvB,CAAC;QAEF,mCAAmC;QACnC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACtD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC1C,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC;QAED,uCAAuC;QACvC,MAAM,UAAU,GAAyB,EAA0B,CAAC;QACpE,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YACtD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAClC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzD,UAAU,EAAE,CAAC;YACf,CAAC;QACH,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACnB,KAAK,CAAC,mBAAmB,GAAG,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC;YAE/E,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvD,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;oBACrB,QAAQ,GAAG,KAAK,CAAC;oBACjB,KAAK,CAAC,mBAAmB,GAAG,IAAY,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAtaD,sDAsaC"}