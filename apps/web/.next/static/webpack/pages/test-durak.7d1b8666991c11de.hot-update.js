"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_created',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока (пересоздание игры с новыми игроками)\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const currentState = game.getState();\n      const newPlayer = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isActive: false\n      };\n\n      // Создаем новую игру с обновленным списком игроков\n      const allPlayers = [...currentState.players, newPlayer];\n      const rules = {\n        variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakVariant.CLASSIC,\n        numberOfPlayers: allPlayers.length,\n        initialHandSize: 6,\n        attackLimit: 6\n      };\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(allPlayers, rules);\n\n      // Подписываемся на события новой игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      addEvent({\n        type: 'player_joined',\n        playerId: newPlayer.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n        return; // addPlayer пересоздаст игру, поэтому выходим\n      }\n\n      game.startGame();\n      updateGameState();\n      addEvent({\n        type: 'game_started',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const result = game.makeMove(action);\n      updateGameState();\n      if (result.success) {\n        addEvent({\n          type: 'card_played',\n          playerId: action.playerId,\n          message: `Игрок выполнил действие: ${action.action}`,\n          data: action\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.phase === 'finished') {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.players.find(p => p.id === newState.winner)?.name}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: action.playerId,\n          message: result.error || 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});