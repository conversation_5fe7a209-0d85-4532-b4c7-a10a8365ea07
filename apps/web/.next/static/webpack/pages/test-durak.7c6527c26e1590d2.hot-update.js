"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_created',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока (пересоздание игры с новыми игроками)\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const currentState = game.getState();\n      const newPlayer = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isActive: false\n      };\n\n      // Создаем новую игру с обновленным списком игроков\n      const allPlayers = [...currentState.players, newPlayer];\n      const rules = {\n        variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakVariant.CLASSIC,\n        numberOfPlayers: allPlayers.length,\n        initialHandSize: 6,\n        attackLimit: 6\n      };\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(allPlayers, rules);\n\n      // Подписываемся на события новой игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      addEvent({\n        type: 'player_joined',\n        playerId: newPlayer.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n        return; // addPlayer пересоздаст игру, поэтому выходим\n      }\n\n      game.startGame();\n      updateGameState();\n      addEvent({\n        type: 'game_started',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action, cardIndex) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const success = game.makeMove(currentPlayerId, action, cardIndex);\n      updateGameState();\n      if (success) {\n        addEvent({\n          type: 'player_moved',\n          playerId: currentPlayerId,\n          message: `Игрок выполнил действие: ${action}`,\n          data: {\n            action,\n            cardIndex\n          }\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.FINISHED) {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.winner.name || newState.winner.id}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: currentPlayerId,\n          message: 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: currentPlayerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, currentPlayerId, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});