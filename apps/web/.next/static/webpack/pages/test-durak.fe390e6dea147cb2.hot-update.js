"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_created',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока (пересоздание игры с новыми игроками)\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const currentState = game.getState();\n      const newPlayer = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isActive: false\n      };\n\n      // Создаем новую игру с обновленным списком игроков\n      const allPlayers = [...currentState.players, newPlayer];\n      const rules = {\n        variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakVariant.CLASSIC,\n        numberOfPlayers: allPlayers.length,\n        initialHandSize: 6,\n        attackLimit: 6\n      };\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(allPlayers, rules);\n\n      // Подписываемся на события новой игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      addEvent({\n        type: 'player_joined',\n        playerId: newPlayer.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n      }\n      game.start();\n      updateGameState();\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const result = game.makeMove(action);\n      updateGameState();\n      if (result.success) {\n        addEvent({\n          type: 'card_played',\n          playerId: action.playerId,\n          message: `Игрок выполнил действие: ${action.action}`,\n          data: action\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.phase === 'finished') {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.players.find(p => p.id === newState.winner)?.name}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: action.playerId,\n          message: result.error || 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});