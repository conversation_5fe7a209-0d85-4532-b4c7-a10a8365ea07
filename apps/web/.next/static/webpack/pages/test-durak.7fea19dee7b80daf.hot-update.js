"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/pages/test-durak.tsx":
/*!**********************************!*\
  !*** ./src/pages/test-durak.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useDurakGame */ \"./src/hooks/useDurakGame.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst GameArea = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c2 = GameArea;\nconst Controls = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n_c3 = Controls;\nconst Button = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].button`\n  padding: 10px 20px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  \n  &:hover {\n    background: #0056b3;\n  }\n  \n  &:disabled {\n    background: #ccc;\n    cursor: not-allowed;\n  }\n`;\n_c4 = Button;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c5 = GameInfo;\nconst PlayerHand = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-top: 10px;\n`;\n_c6 = PlayerHand;\nconst Card = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 60px;\n  height: 80px;\n  border: 2px solid ${props => props.isPlayable ? '#28a745' : '#dee2e6'};\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};\n  font-size: 12px;\n  text-align: center;\n  \n  &:hover {\n    ${props => props.isPlayable && `\n      border-color: #1e7e34;\n      transform: translateY(-2px);\n    `}\n  }\n`;\n_c7 = Card;\nconst EventLog = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c8 = EventLog;\nconst Event = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c9 = Event;\nconst TestDurakPage = () => {\n  _s();\n  const {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    resetGame\n  } = (0,_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame)();\n  const handleCreateGame = () => {\n    const rules = {\n      variant: 'classic',\n      cardsPerPlayer: 6,\n      deckType: '36cards'\n    };\n    createNewGame(rules);\n  };\n  const handleAddPlayer = () => {\n    addPlayer('Игрок 1');\n  };\n  const handleAddBot = () => {\n    addPlayer('Бот', true);\n  };\n  const handleCardClick = card => {\n    if (!canPlayCard(card) || !isMyTurn) return;\n    const validMoves = getValidMoves();\n    const moveWithCard = validMoves.find(move => move.card && move.card.suit === card.suit && move.card.rank === card.rank);\n    if (moveWithCard) {\n      makeMove(moveWithCard);\n    }\n  };\n  const handleAction = actionType => {\n    const validMoves = getValidMoves();\n    const move = validMoves.find(m => m.action === actionType);\n    if (move) {\n      makeMove(move);\n    }\n  };\n  const getSuitSymbol = suit => {\n    const symbols = {\n      hearts: '♥️',\n      diamonds: '♦️',\n      clubs: '♣️',\n      spades: '♠️'\n    };\n    return symbols[suit] || suit;\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"title\", {\n        children: \"\\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h1\", {\n      children: \"\\u0422\\u0435\\u0441\\u0442 \\u0438\\u0433\\u0440\\u044B \\u0414\\u0443\\u0440\\u0430\\u043A\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(GameArea, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleCreateGame,\n          disabled: !!game,\n          children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleAddPlayer,\n          disabled: !game || !!gameState?.players.find(p => !p.isBot),\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleAddBot,\n          disabled: !game,\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u0442\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: startGame,\n          disabled: !game || gameState?.phase !== 'waiting',\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: resetGame,\n          children: \"\\u0421\\u0431\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      }), gameState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0418\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043E\\u0431 \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0424\\u0430\\u0437\\u0430:\"\n          }), \" \", gameState.phase]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u0438:\"\n          }), \" \", gameState.players.length]\n        }), gameState.trumpCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C:\"\n          }), \" \", gameState.trumpCard.rank, \" \", getSuitSymbol(gameState.trumpCard.suit)]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u041A\\u0430\\u0440\\u0442 \\u0432 \\u043A\\u043E\\u043B\\u043E\\u0434\\u0435:\"\n          }), \" \", gameState.deck.length]\n        }), gameState.currentPlayerIndex >= 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0425\\u043E\\u0434 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430:\"\n          }), \" \", gameState.players[gameState.currentPlayerIndex]?.name]\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"p\", {\n          style: {\n            color: 'green'\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0412\\u0430\\u0448 \\u0445\\u043E\\u0434!\"\n          })\n        })]\n      }), currentPlayer && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"h3\", {\n          children: [\"\\u0412\\u0430\\u0448\\u0438 \\u043A\\u0430\\u0440\\u0442\\u044B (\", currentPlayer.name, \")\"]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlayerHand, {\n          children: currentPlayer.hand.map((card, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n            isPlayable: canPlayCard(card) && isMyTurn,\n            onClick: () => handleCardClick(card),\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n              children: card.rank\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n              children: getSuitSymbol(card.suit)\n            })]\n          }, `${card.suit}-${card.rank}-${index}`))\n        })]\n      }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: () => handleAction('pass'),\n          children: \"\\u041F\\u0430\\u0441\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: () => handleAction('take'),\n          children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n        })]\n      }), gameState?.table && gameState.table.length > 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0421\\u0442\\u043E\\u043B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlayerHand, {\n          children: gameState.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: tableCard.attackCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.attackCard.suit)\n              })]\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: tableCard.defendCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.defendCard.suit)\n              })]\n            })]\n          }, index))\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0421\\u043E\\u0431\\u044B\\u0442\\u0438\\u044F \\u0438\\u0433\\u0440\\u044B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(EventLog, {\n          children: gameEvents.map((event, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Event, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"strong\", {\n              children: [new Date(event.timestamp).toLocaleTimeString(), \":\"]\n            }), \" \", event.message]\n          }, index))\n        })]\n      })]\n    })]\n  });\n};\n_s(TestDurakPage, \"aq9NcMA/pY19Dl8ODsGzko8SlEQ=\", false, function () {\n  return [_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame];\n});\n_c10 = TestDurakPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TestDurakPage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"GameArea\");\n$RefreshReg$(_c3, \"Controls\");\n$RefreshReg$(_c4, \"Button\");\n$RefreshReg$(_c5, \"GameInfo\");\n$RefreshReg$(_c6, \"PlayerHand\");\n$RefreshReg$(_c7, \"Card\");\n$RefreshReg$(_c8, \"EventLog\");\n$RefreshReg$(_c9, \"Event\");\n$RefreshReg$(_c10, \"TestDurakPage\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/test-durak.tsx\n"));

/***/ })

});