"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_created',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const player = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isBot\n      };\n      game.addPlayer(player);\n      updateGameState();\n      addEvent({\n        type: 'player_joined',\n        playerId: player.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, updateGameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n      }\n      game.start();\n      updateGameState();\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const result = game.makeMove(action);\n      updateGameState();\n      if (result.success) {\n        addEvent({\n          type: 'card_played',\n          playerId: action.playerId,\n          message: `Игрок выполнил действие: ${action.action}`,\n          data: action\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.phase === 'finished') {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.players.find(p => p.id === newState.winner)?.name}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: action.playerId,\n          message: result.error || 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});