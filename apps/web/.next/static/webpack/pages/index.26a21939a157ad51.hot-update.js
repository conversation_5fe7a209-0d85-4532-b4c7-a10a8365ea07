"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"../../node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Динамическая загрузка тяжелых компонентов\n\nconst LoadingScreen = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = () => __webpack_require__.e(/*! import() */ \"src_components_LoadingScreen_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\")), {\n  ssr: false\n});\n_c2 = LoadingScreen;\nconst Hero3D = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c3 = () => __webpack_require__.e(/*! import() */ \"src_components_Hero3D_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Hero3D */ \"./src/components/Hero3D.tsx\")), {\n  ssr: false,\n  loading: () => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n    children: \"\\u0417\\u0430\\u0433\\u0440\\u0443\\u0437\\u043A\\u0430 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0433\\u043E \\u0434\\u0432\\u0438\\u0436\\u043A\\u0430...\"\n  })\n});\n_c4 = Hero3D;\nconst RevolutionaryFeatures = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c5 = () => __webpack_require__.e(/*! import() */ \"src_components_RevolutionaryFeatures_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RevolutionaryFeatures */ \"./src/components/RevolutionaryFeatures.tsx\")), {\n  ssr: false\n});\n_c6 = RevolutionaryFeatures;\nconst MetaversePreview = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c7 = () => __webpack_require__.e(/*! import() */ \"src_components_MetaversePreview-simple_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/MetaversePreview-simple */ \"./src/components/MetaversePreview-simple.tsx\")), {\n  ssr: false\n});\n_c8 = MetaversePreview;\nconst AIShowcase = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c9 = () => __webpack_require__.e(/*! import() */ \"src_components_AIShowcase_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/AIShowcase */ \"./src/components/AIShowcase.tsx\")), {\n  ssr: false\n});\n_c10 = AIShowcase;\nconst Web3Dashboard = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c11 = () => __webpack_require__.e(/*! import() */ \"src_components_Web3Dashboard_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Web3Dashboard */ \"./src/components/Web3Dashboard.tsx\")), {\n  ssr: false\n});\n_c12 = Web3Dashboard;\nconst StreamingPlatform = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c13 = () => __webpack_require__.e(/*! import() */ \"src_components_StreamingPlatform_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/StreamingPlatform */ \"./src/components/StreamingPlatform.tsx\")), {\n  ssr: false\n});\n_c14 = StreamingPlatform;\nconst GameDemo = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c15 = () => __webpack_require__.e(/*! import() */ \"src_components_GameDemo_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/GameDemo */ \"./src/components/GameDemo.tsx\")), {\n  ssr: false\n});\n_c16 = GameDemo;\nconst Footer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c17 = () => __webpack_require__.e(/*! import() */ \"src_components_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Footer */ \"./src/components/Footer.tsx\")), {\n  ssr: false\n});\n_c18 = Footer;\nconst HomePage = () => {\n  _s();\n  const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n  const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [quantumStatus, setQuantumStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    isQuantumAvailable: false,\n    metrics: {\n      entropy: 0\n    }\n  });\n  const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    happiness: 0.5\n  });\n  const [web3Status, setWeb3Status] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    connected: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Инициализация всех систем\n    const initializeSystems = async () => {\n      try {\n        // Симуляция загрузки квантовых систем\n        await new Promise(resolve => setTimeout(resolve, 3000));\n        setQuantumStatus({\n          isQuantumAvailable: true,\n          metrics: {\n            entropy: 0.999\n          }\n        });\n        setEmotionalState({\n          happiness: 0.8\n        });\n        setIsLoaded(true);\n      } catch (error) {\n        console.error('Error initializing systems:', error);\n        setIsLoaded(true);\n      }\n    };\n    initializeSystems();\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Обработка скролла для анимаций\n    const handleScroll = () => {\n      const scrollY = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const section = Math.floor(scrollY / windowHeight);\n      setCurrentSection(section);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const connectWallet = () => {\n    setWeb3Status({\n      connected: true\n    });\n  };\n  if (!isLoaded) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(LoadingScreen, {\n      message: \"\\u0418\\u043D\\u0438\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0445 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C...\"\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"title\", {\n        children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0 - \\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u044F \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"meta\", {\n        name: \"description\",\n        content: \"\\u041F\\u0435\\u0440\\u0432\\u0430\\u044F \\u0432 \\u043C\\u0438\\u0440\\u0435 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440 \\u0441 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E, \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u0418\\u0418 \\u0438 3D \\u043C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0439\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"link\", {\n        rel: \"icon\",\n        href: \"/favicon.ico\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(MainContent, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        mode: \"wait\",\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: currentSection === 0 ? 1 : 0.3\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Hero3D, {\n            quantumStatus: quantumStatus,\n            emotionalState: emotionalState,\n            onStartJourney: () => setCurrentSection(1)\n          })\n        }, \"hero\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            y: 100\n          },\n          animate: {\n            opacity: currentSection >= 1 ? 1 : 0,\n            y: currentSection >= 1 ? 0 : 100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(RevolutionaryFeatures, {\n            currentSection: currentSection\n          })\n        }, \"features\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: currentSection >= 2 ? 1 : 0,\n            scale: currentSection >= 2 ? 1 : 0.8\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(MetaversePreview, {})\n        }, \"metaverse\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            rotateY: 90\n          },\n          animate: {\n            opacity: currentSection >= 3 ? 1 : 0,\n            rotateY: currentSection >= 3 ? 0 : 90\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(AIShowcase, {\n            emotionalState: emotionalState\n          })\n        }, \"ai\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            x: -100\n          },\n          animate: {\n            opacity: currentSection >= 4 ? 1 : 0,\n            x: currentSection >= 4 ? 0 : -100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Web3Dashboard, {\n            web3Status: web3Status,\n            onConnectWallet: connectWallet\n          })\n        }, \"web3\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            x: 100\n          },\n          animate: {\n            opacity: currentSection >= 5 ? 1 : 0,\n            x: currentSection >= 5 ? 0 : 100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.6\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StreamingPlatform, {})\n        }, \"streaming\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: currentSection >= 6 ? 1 : 0,\n            scale: currentSection >= 6 ? 1 : 0.8\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.7\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameDemo, {\n            onStartGame: () => router.push('/games')\n          })\n        }, \"game-demo\")]\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Footer, {\n      onSubscribe: email => console.log('Подписка:', email)\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(NavigationDots, {\n      children: [0, 1, 2, 3, 4, 5, 6].map(section => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(NavDot, {\n        active: currentSection === section,\n        onClick: () => {\n          window.scrollTo({\n            top: section * window.innerHeight,\n            behavior: 'smooth'\n          });\n        },\n        whileHover: {\n          scale: 1.2\n        },\n        whileTap: {\n          scale: 0.9\n        }\n      }, section))\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(QuantumStatus, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      transition: {\n        delay: 1\n      },\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusIndicator, {\n        active: quantumStatus?.isQuantumAvailable\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusText, {\n        children: quantumStatus?.isQuantumAvailable ? 'Квантовая связь активна' : 'Криптографический режим'\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(StatusDetail, {\n        children: [\"\\u042D\\u043D\\u0442\\u0440\\u043E\\u043F\\u0438\\u044F: \", quantumStatus?.metrics?.entropy?.toFixed(3) || 'N/A']\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(AIStatus, {\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 1.5\n      },\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusIndicator, {\n        active: true,\n        color: \"#4a90e2\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusText, {\n        children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437 \\u0430\\u043A\\u0442\\u0438\\u0432\\u0435\\u043D\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(StatusDetail, {\n        children: [\"\\u041D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0435\\u043D\\u0438\\u0435: \", emotionalState?.happiness > 0.7 ? '😊' : emotionalState?.happiness > 0.4 ? '😐' : '😔']\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(TestButton, {\n        onClick: () => router.push('/test-durak'),\n        children: \"\\uD83C\\uDFAE \\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A\"\n      })]\n    })]\n  });\n};\n\n// Стилизованные компоненты\n_s(HomePage, \"sv0aXmVk3AUZ6uIBXbZVADyRi9Y=\", false, function () {\n  return [next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter];\n});\n_c19 = HomePage;\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  min-height: 100vh;\n  background: #000;\n  overflow-x: hidden;\n`;\n_c20 = Container;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  position: relative;\n  z-index: 10;\n`;\n_c21 = MainContent;\nconst Section = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section)`\n  min-height: 100vh;\n  width: 100%;\n`;\n_c22 = Section;\nconst PlaceholderSection = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  min-height: 100vh;\n  background: ${props => props.background};\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  text-align: center;\n  \n  h2 {\n    font-size: 3rem;\n    margin-bottom: 1rem;\n    background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n  \n  p {\n    font-size: 1.5rem;\n    color: rgba(255, 255, 255, 0.7);\n  }\n`;\nconst NavigationDots = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  position: fixed;\n  right: 2rem;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 50;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c23 = NavigationDots;\nconst NavDot = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button)`\n  width: 1rem;\n  height: 1rem;\n  border-radius: 50%;\n  border: 2px solid ${props => props.active ? 'white' : 'rgba(255, 255, 255, 0.4)'};\n  background: ${props => props.active ? 'linear-gradient(45deg, #4a90e2, #7b68ee)' : 'transparent'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: white;\n  }\n`;\n_c24 = NavDot;\nconst QuantumStatus = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div)`\n  position: fixed;\n  top: 1rem;\n  right: 1rem;\n  z-index: 50;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n_c25 = QuantumStatus;\nconst AIStatus = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div)`\n  position: fixed;\n  bottom: 1rem;\n  left: 1rem;\n  z-index: 50;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n_c26 = AIStatus;\nconst StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  width: 0.75rem;\n  height: 0.75rem;\n  border-radius: 50%;\n  background: ${props => props.active ? props.color || '#4ade80' : '#fbbf24'};\n  display: inline-block;\n  margin-right: 0.5rem;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.5; }\n  }\n`;\n_c27 = StatusIndicator;\nconst StatusText = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].span`\n  font-size: 0.875rem;\n  font-weight: 500;\n`;\n_c28 = StatusText;\nconst StatusDetail = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin-top: 0.25rem;\n`;\n_c29 = StatusDetail;\nconst TestButton = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].button`\n  margin-top: 0.5rem;\n  padding: 0.25rem 0.5rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  color: white;\n  border: none;\n  border-radius: 5px;\n  font-size: 0.75rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);\n  }\n`;\n_c30 = TestButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30;\n$RefreshReg$(_c, \"LoadingScreen$dynamic\");\n$RefreshReg$(_c2, \"LoadingScreen\");\n$RefreshReg$(_c3, \"Hero3D$dynamic\");\n$RefreshReg$(_c4, \"Hero3D\");\n$RefreshReg$(_c5, \"RevolutionaryFeatures$dynamic\");\n$RefreshReg$(_c6, \"RevolutionaryFeatures\");\n$RefreshReg$(_c7, \"MetaversePreview$dynamic\");\n$RefreshReg$(_c8, \"MetaversePreview\");\n$RefreshReg$(_c9, \"AIShowcase$dynamic\");\n$RefreshReg$(_c10, \"AIShowcase\");\n$RefreshReg$(_c11, \"Web3Dashboard$dynamic\");\n$RefreshReg$(_c12, \"Web3Dashboard\");\n$RefreshReg$(_c13, \"StreamingPlatform$dynamic\");\n$RefreshReg$(_c14, \"StreamingPlatform\");\n$RefreshReg$(_c15, \"GameDemo$dynamic\");\n$RefreshReg$(_c16, \"GameDemo\");\n$RefreshReg$(_c17, \"Footer$dynamic\");\n$RefreshReg$(_c18, \"Footer\");\n$RefreshReg$(_c19, \"HomePage\");\n$RefreshReg$(_c20, \"Container\");\n$RefreshReg$(_c21, \"MainContent\");\n$RefreshReg$(_c22, \"Section\");\n$RefreshReg$(_c23, \"NavigationDots\");\n$RefreshReg$(_c24, \"NavDot\");\n$RefreshReg$(_c25, \"QuantumStatus\");\n$RefreshReg$(_c26, \"AIStatus\");\n$RefreshReg$(_c27, \"StatusIndicator\");\n$RefreshReg$(_c28, \"StatusText\");\n$RefreshReg$(_c29, \"StatusDetail\");\n$RefreshReg$(_c30, \"TestButton\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});