"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/pages/test-durak.tsx":
/*!**********************************!*\
  !*** ./src/pages/test-durak.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useDurakGame */ \"./src/hooks/useDurakGame.ts\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst GameArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c2 = GameArea;\nconst Controls = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n_c3 = Controls;\nconst Button = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button`\n  padding: 10px 20px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  \n  &:hover {\n    background: #0056b3;\n  }\n  \n  &:disabled {\n    background: #ccc;\n    cursor: not-allowed;\n  }\n`;\n_c4 = Button;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c5 = GameInfo;\nconst PlayerHand = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-top: 10px;\n`;\n_c6 = PlayerHand;\nconst Card = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  width: 60px;\n  height: 80px;\n  border: 2px solid ${props => props.isPlayable ? '#28a745' : '#dee2e6'};\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};\n  font-size: 12px;\n  text-align: center;\n  \n  &:hover {\n    ${props => props.isPlayable && `\n      border-color: #1e7e34;\n      transform: translateY(-2px);\n    `}\n  }\n`;\n_c7 = Card;\nconst EventLog = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c8 = EventLog;\nconst Event = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c9 = Event;\nconst TestDurakPage = () => {\n  _s();\n  const {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    resetGame\n  } = (0,_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame)();\n  const handleCreateGame = () => {\n    const rules = {\n      variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.DurakVariant.CLASSIC,\n      numberOfPlayers: 2,\n      initialHandSize: 6,\n      attackLimit: 6\n    };\n    createNewGame(rules);\n  };\n  const handleAddPlayer = () => {\n    addPlayer('Игрок 1');\n  };\n  const handleAddBot = () => {\n    addPlayer('Бот', true);\n  };\n  const handleCardClick = (card, cardIndex) => {\n    if (!canPlayCard(card) || !isMyTurn) return;\n\n    // Пробуем атаку с этой картой\n    makeMove(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.ATTACK, cardIndex);\n  };\n  const handleAction = actionType => {\n    let action;\n    switch (actionType) {\n      case 'pass':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.PASS;\n        break;\n      case 'take':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.TAKE;\n        break;\n      case 'defend':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.DEFEND;\n        break;\n      default:\n        return;\n    }\n    makeMove(action);\n  };\n  const getSuitSymbol = suit => {\n    const symbols = {\n      hearts: '♥️',\n      diamonds: '♦️',\n      clubs: '♣️',\n      spades: '♠️'\n    };\n    return symbols[suit] || suit;\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"title\", {\n        children: \"\\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h1\", {\n      children: \"\\u0422\\u0435\\u0441\\u0442 \\u0438\\u0433\\u0440\\u044B \\u0414\\u0443\\u0440\\u0430\\u043A\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameArea, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleCreateGame,\n          disabled: !!game,\n          children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleAddPlayer,\n          disabled: !game || !!gameState?.players.find(p => !p.isBot),\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleAddBot,\n          disabled: !game,\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u0442\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: startGame,\n          disabled: !game || gameState?.phase !== 'waiting',\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: resetGame,\n          children: \"\\u0421\\u0431\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      }), gameState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0418\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043E\\u0431 \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0424\\u0430\\u0437\\u0430:\"\n          }), \" \", gameState.phase]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u0438:\"\n          }), \" \", gameState.players.length]\n        }), gameState.trumpCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C:\"\n          }), \" \", gameState.trumpCard.rank, \" \", getSuitSymbol(gameState.trumpCard.suit)]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u041A\\u0430\\u0440\\u0442 \\u0432 \\u043A\\u043E\\u043B\\u043E\\u0434\\u0435:\"\n          }), \" \", gameState.deck.length]\n        }), gameState.currentPlayerIndex >= 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0425\\u043E\\u0434 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430:\"\n          }), \" \", gameState.players[gameState.currentPlayerIndex]?.name]\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"p\", {\n          style: {\n            color: 'green'\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0412\\u0430\\u0448 \\u0445\\u043E\\u0434!\"\n          })\n        })]\n      }), currentPlayer && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"h3\", {\n          children: [\"\\u0412\\u0430\\u0448\\u0438 \\u043A\\u0430\\u0440\\u0442\\u044B (\", currentPlayer.name, \")\"]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerHand, {\n          children: currentPlayer.hand.map((card, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n            isPlayable: canPlayCard(card) && isMyTurn,\n            onClick: () => handleCardClick(card),\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n              children: card.rank\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n              children: getSuitSymbol(card.suit)\n            })]\n          }, `${card.suit}-${card.rank}-${index}`))\n        })]\n      }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: () => handleAction('pass'),\n          children: \"\\u041F\\u0430\\u0441\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: () => handleAction('take'),\n          children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n        })]\n      }), gameState?.table && gameState.table.length > 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0421\\u0442\\u043E\\u043B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerHand, {\n          children: gameState.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: tableCard.attackCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.attackCard.suit)\n              })]\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: tableCard.defendCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.defendCard.suit)\n              })]\n            })]\n          }, index))\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0421\\u043E\\u0431\\u044B\\u0442\\u0438\\u044F \\u0438\\u0433\\u0440\\u044B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(EventLog, {\n          children: gameEvents.map((event, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Event, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"strong\", {\n              children: [new Date(event.timestamp).toLocaleTimeString(), \":\"]\n            }), \" \", event.message]\n          }, index))\n        })]\n      })]\n    })]\n  });\n};\n_s(TestDurakPage, \"aq9NcMA/pY19Dl8ODsGzko8SlEQ=\", false, function () {\n  return [_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame];\n});\n_c10 = TestDurakPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TestDurakPage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"GameArea\");\n$RefreshReg$(_c3, \"Controls\");\n$RefreshReg$(_c4, \"Button\");\n$RefreshReg$(_c5, \"GameInfo\");\n$RefreshReg$(_c6, \"PlayerHand\");\n$RefreshReg$(_c7, \"Card\");\n$RefreshReg$(_c8, \"EventLog\");\n$RefreshReg$(_c9, \"Event\");\n$RefreshReg$(_c10, \"TestDurakPage\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/test-durak.tsx\n"));

/***/ })

});