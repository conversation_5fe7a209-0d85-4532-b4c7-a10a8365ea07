"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"../../node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Динамическая загрузка тяжелых компонентов\n\nconst LoadingScreen = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = () => __webpack_require__.e(/*! import() */ \"src_components_LoadingScreen_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\")), {\n  ssr: false\n});\n_c2 = LoadingScreen;\nconst Hero3D = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c3 = () => __webpack_require__.e(/*! import() */ \"src_components_Hero3D_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Hero3D */ \"./src/components/Hero3D.tsx\")), {\n  ssr: false,\n  loading: () => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n    children: \"\\u0417\\u0430\\u0433\\u0440\\u0443\\u0437\\u043A\\u0430 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0433\\u043E \\u0434\\u0432\\u0438\\u0436\\u043A\\u0430...\"\n  })\n});\n_c4 = Hero3D;\nconst RevolutionaryFeatures = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c5 = () => __webpack_require__.e(/*! import() */ \"src_components_RevolutionaryFeatures_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RevolutionaryFeatures */ \"./src/components/RevolutionaryFeatures.tsx\")), {\n  ssr: false\n});\n_c6 = RevolutionaryFeatures;\nconst MetaversePreview = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c7 = () => __webpack_require__.e(/*! import() */ \"src_components_MetaversePreview-simple_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/MetaversePreview-simple */ \"./src/components/MetaversePreview-simple.tsx\")), {\n  ssr: false\n});\n_c8 = MetaversePreview;\nconst AIShowcase = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c9 = () => __webpack_require__.e(/*! import() */ \"src_components_AIShowcase_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/AIShowcase */ \"./src/components/AIShowcase.tsx\")), {\n  ssr: false\n});\n_c10 = AIShowcase;\nconst Web3Dashboard = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c11 = () => __webpack_require__.e(/*! import() */ \"src_components_Web3Dashboard_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Web3Dashboard */ \"./src/components/Web3Dashboard.tsx\")), {\n  ssr: false\n});\n_c12 = Web3Dashboard;\nconst StreamingPlatform = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c13 = () => __webpack_require__.e(/*! import() */ \"src_components_StreamingPlatform_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/StreamingPlatform */ \"./src/components/StreamingPlatform.tsx\")), {\n  ssr: false\n});\n_c14 = StreamingPlatform;\nconst GameDemo = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c15 = () => __webpack_require__.e(/*! import() */ \"src_components_GameDemo_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/GameDemo */ \"./src/components/GameDemo.tsx\")), {\n  ssr: false\n});\n_c16 = GameDemo;\nconst Footer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c17 = () => __webpack_require__.e(/*! import() */ \"src_components_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Footer */ \"./src/components/Footer.tsx\")), {\n  ssr: false\n});\n_c18 = Footer;\nconst HomePage = () => {\n  _s();\n  const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n  const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [currentSection, setCurrentSection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [quantumStatus, setQuantumStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    isQuantumAvailable: false,\n    metrics: {\n      entropy: 0\n    }\n  });\n  const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    happiness: 0.5\n  });\n  const [web3Status, setWeb3Status] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    connected: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Инициализация всех систем\n    const initializeSystems = async () => {\n      try {\n        // Симуляция загрузки квантовых систем\n        await new Promise(resolve => setTimeout(resolve, 3000));\n        setQuantumStatus({\n          isQuantumAvailable: true,\n          metrics: {\n            entropy: 0.999\n          }\n        });\n        setEmotionalState({\n          happiness: 0.8\n        });\n        setIsLoaded(true);\n      } catch (error) {\n        console.error('Error initializing systems:', error);\n        setIsLoaded(true);\n      }\n    };\n    initializeSystems();\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Обработка скролла для анимаций\n    const handleScroll = () => {\n      const scrollY = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const section = Math.floor(scrollY / windowHeight);\n      setCurrentSection(section);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const connectWallet = () => {\n    setWeb3Status({\n      connected: true\n    });\n  };\n  if (!isLoaded) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(LoadingScreen, {\n      message: \"\\u0418\\u043D\\u0438\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0445 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C...\"\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"title\", {\n        children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0 - \\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u044F \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"meta\", {\n        name: \"description\",\n        content: \"\\u041F\\u0435\\u0440\\u0432\\u0430\\u044F \\u0432 \\u043C\\u0438\\u0440\\u0435 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440 \\u0441 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E, \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u0418\\u0418 \\u0438 3D \\u043C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0439\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"link\", {\n        rel: \"icon\",\n        href: \"/favicon.ico\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(MainContent, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        mode: \"wait\",\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: currentSection === 0 ? 1 : 0.3\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Hero3D, {\n            quantumStatus: quantumStatus,\n            emotionalState: emotionalState,\n            onStartJourney: () => setCurrentSection(1)\n          })\n        }, \"hero\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            y: 100\n          },\n          animate: {\n            opacity: currentSection >= 1 ? 1 : 0,\n            y: currentSection >= 1 ? 0 : 100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(RevolutionaryFeatures, {\n            currentSection: currentSection\n          })\n        }, \"features\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: currentSection >= 2 ? 1 : 0,\n            scale: currentSection >= 2 ? 1 : 0.8\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(MetaversePreview, {})\n        }, \"metaverse\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            rotateY: 90\n          },\n          animate: {\n            opacity: currentSection >= 3 ? 1 : 0,\n            rotateY: currentSection >= 3 ? 0 : 90\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(AIShowcase, {\n            emotionalState: emotionalState\n          })\n        }, \"ai\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            x: -100\n          },\n          animate: {\n            opacity: currentSection >= 4 ? 1 : 0,\n            x: currentSection >= 4 ? 0 : -100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Web3Dashboard, {\n            web3Status: web3Status,\n            onConnectWallet: connectWallet\n          })\n        }, \"web3\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            x: 100\n          },\n          animate: {\n            opacity: currentSection >= 5 ? 1 : 0,\n            x: currentSection >= 5 ? 0 : 100\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.6\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StreamingPlatform, {})\n        }, \"streaming\"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Section, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: currentSection >= 6 ? 1 : 0,\n            scale: currentSection >= 6 ? 1 : 0.8\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.7\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(GameDemo, {\n            onStartGame: () => router.push('/games')\n          })\n        }, \"game-demo\")]\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Footer, {\n      onSubscribe: email => console.log('Подписка:', email)\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(NavigationDots, {\n      children: [0, 1, 2, 3, 4, 5, 6].map(section => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(NavDot, {\n        active: currentSection === section,\n        onClick: () => {\n          window.scrollTo({\n            top: section * window.innerHeight,\n            behavior: 'smooth'\n          });\n        },\n        whileHover: {\n          scale: 1.2\n        },\n        whileTap: {\n          scale: 0.9\n        }\n      }, section))\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(QuantumStatus, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      transition: {\n        delay: 1\n      },\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusIndicator, {\n        active: quantumStatus?.isQuantumAvailable\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusText, {\n        children: quantumStatus?.isQuantumAvailable ? 'Квантовая связь активна' : 'Криптографический режим'\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(StatusDetail, {\n        children: [\"\\u042D\\u043D\\u0442\\u0440\\u043E\\u043F\\u0438\\u044F: \", quantumStatus?.metrics?.entropy?.toFixed(3) || 'N/A']\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(AIStatus, {\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 1.5\n      },\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusIndicator, {\n        active: true,\n        color: \"#4a90e2\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(StatusText, {\n        children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437 \\u0430\\u043A\\u0442\\u0438\\u0432\\u0435\\u043D\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(StatusDetail, {\n        children: [\"\\u041D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0435\\u043D\\u0438\\u0435: \", emotionalState?.happiness > 0.7 ? '😊' : emotionalState?.happiness > 0.4 ? '😐' : '😔']\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(TestButton, {\n        onClick: () => router.push('/test-durak'),\n        children: \"\\uD83C\\uDFAE \\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A\"\n      })]\n    })]\n  });\n};\n\n// Стилизованные компоненты\n_s(HomePage, \"sv0aXmVk3AUZ6uIBXbZVADyRi9Y=\", false, function () {\n  return [next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter];\n});\n_c19 = HomePage;\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  min-height: 100vh;\n  background: #000;\n  overflow-x: hidden;\n`;\n_c20 = Container;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  position: relative;\n  z-index: 10;\n`;\n_c21 = MainContent;\nconst Section = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section)`\n  min-height: 100vh;\n  width: 100%;\n`;\n_c22 = Section;\nconst PlaceholderSection = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  min-height: 100vh;\n  background: ${props => props.background};\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  text-align: center;\n  \n  h2 {\n    font-size: 3rem;\n    margin-bottom: 1rem;\n    background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n  \n  p {\n    font-size: 1.5rem;\n    color: rgba(255, 255, 255, 0.7);\n  }\n`;\nconst NavigationDots = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  position: fixed;\n  right: 2rem;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 50;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c23 = NavigationDots;\nconst NavDot = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button)`\n  width: 1rem;\n  height: 1rem;\n  border-radius: 50%;\n  border: 2px solid ${props => props.active ? 'white' : 'rgba(255, 255, 255, 0.4)'};\n  background: ${props => props.active ? 'linear-gradient(45deg, #4a90e2, #7b68ee)' : 'transparent'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: white;\n  }\n`;\n_c24 = NavDot;\nconst QuantumStatus = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div)`\n  position: fixed;\n  top: 1rem;\n  right: 1rem;\n  z-index: 50;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n_c25 = QuantumStatus;\nconst AIStatus = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div)`\n  position: fixed;\n  bottom: 1rem;\n  left: 1rem;\n  z-index: 50;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n_c26 = AIStatus;\nconst StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  width: 0.75rem;\n  height: 0.75rem;\n  border-radius: 50%;\n  background: ${props => props.active ? props.color || '#4ade80' : '#fbbf24'};\n  display: inline-block;\n  margin-right: 0.5rem;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.5; }\n  }\n`;\n_c27 = StatusIndicator;\nconst StatusText = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].span`\n  font-size: 0.875rem;\n  font-weight: 500;\n`;\n_c28 = StatusText;\nconst StatusDetail = styled_components__WEBPACK_IMPORTED_MODULE_6__[\"default\"].div`\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin-top: 0.25rem;\n`;\n_c29 = StatusDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29;\n$RefreshReg$(_c, \"LoadingScreen$dynamic\");\n$RefreshReg$(_c2, \"LoadingScreen\");\n$RefreshReg$(_c3, \"Hero3D$dynamic\");\n$RefreshReg$(_c4, \"Hero3D\");\n$RefreshReg$(_c5, \"RevolutionaryFeatures$dynamic\");\n$RefreshReg$(_c6, \"RevolutionaryFeatures\");\n$RefreshReg$(_c7, \"MetaversePreview$dynamic\");\n$RefreshReg$(_c8, \"MetaversePreview\");\n$RefreshReg$(_c9, \"AIShowcase$dynamic\");\n$RefreshReg$(_c10, \"AIShowcase\");\n$RefreshReg$(_c11, \"Web3Dashboard$dynamic\");\n$RefreshReg$(_c12, \"Web3Dashboard\");\n$RefreshReg$(_c13, \"StreamingPlatform$dynamic\");\n$RefreshReg$(_c14, \"StreamingPlatform\");\n$RefreshReg$(_c15, \"GameDemo$dynamic\");\n$RefreshReg$(_c16, \"GameDemo\");\n$RefreshReg$(_c17, \"Footer$dynamic\");\n$RefreshReg$(_c18, \"Footer\");\n$RefreshReg$(_c19, \"HomePage\");\n$RefreshReg$(_c20, \"Container\");\n$RefreshReg$(_c21, \"MainContent\");\n$RefreshReg$(_c22, \"Section\");\n$RefreshReg$(_c23, \"NavigationDots\");\n$RefreshReg$(_c24, \"NavDot\");\n$RefreshReg$(_c25, \"QuantumStatus\");\n$RefreshReg$(_c26, \"AIStatus\");\n$RefreshReg$(_c27, \"StatusIndicator\");\n$RefreshReg$(_c28, \"StatusText\");\n$RefreshReg$(_c29, \"StatusDetail\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});