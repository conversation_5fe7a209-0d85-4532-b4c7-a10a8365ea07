"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/pages/test-durak.tsx":
/*!**********************************!*\
  !*** ./src/pages/test-durak.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useDurakGame */ \"./src/hooks/useDurakGame.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst GameArea = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c2 = GameArea;\nconst Controls = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n_c3 = Controls;\nconst Button = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].button`\n  padding: 10px 20px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  \n  &:hover {\n    background: #0056b3;\n  }\n  \n  &:disabled {\n    background: #ccc;\n    cursor: not-allowed;\n  }\n`;\n_c4 = Button;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c5 = GameInfo;\nconst PlayerHand = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-top: 10px;\n`;\n_c6 = PlayerHand;\nconst Card = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 60px;\n  height: 80px;\n  border: 2px solid ${props => props.isPlayable ? '#28a745' : '#dee2e6'};\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};\n  font-size: 12px;\n  text-align: center;\n  \n  &:hover {\n    ${props => props.isPlayable && `\n      border-color: #1e7e34;\n      transform: translateY(-2px);\n    `}\n  }\n`;\n_c7 = Card;\nconst EventLog = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c8 = EventLog;\nconst Event = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c9 = Event;\nconst TestDurakPage = () => {\n  _s();\n  const {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  } = (0,_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame)();\n  const handleCreateGame = () => {\n    const rules = {\n      variant: 'classic',\n      cardsPerPlayer: 6,\n      deckType: '36cards'\n    };\n    createNewGame(rules);\n  };\n  const handleAddPlayer = () => {\n    addPlayer('Игрок 1');\n  };\n  const handleAddBot = () => {\n    addPlayer('Бот', true);\n  };\n  const handleCardClick = card => {\n    if (!canPlayCard(card) || !isMyTurn) return;\n    const validMoves = getValidMoves();\n    const moveWithCard = validMoves.find(move => move.card && move.card.suit === card.suit && move.card.rank === card.rank);\n    if (moveWithCard) {\n      makeMove(moveWithCard);\n    }\n  };\n  const handleAction = actionType => {\n    const validMoves = getValidMoves();\n    const move = validMoves.find(m => m.action === actionType);\n    if (move) {\n      makeMove(move);\n    }\n  };\n  const getSuitSymbol = suit => {\n    const symbols = {\n      hearts: '♥️',\n      diamonds: '♦️',\n      clubs: '♣️',\n      spades: '♠️'\n    };\n    return symbols[suit] || suit;\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"title\", {\n        children: \"\\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h1\", {\n      children: \"\\u0422\\u0435\\u0441\\u0442 \\u0438\\u0433\\u0440\\u044B \\u0414\\u0443\\u0440\\u0430\\u043A\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(GameArea, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleCreateGame,\n          disabled: !!game,\n          children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleAddPlayer,\n          disabled: !game || !!gameState?.players.find(p => !p.isBot),\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: handleAddBot,\n          disabled: !game,\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u0442\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: startGame,\n          disabled: !game || gameState?.phase !== 'waiting',\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: resetGame,\n          children: \"\\u0421\\u0431\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      }), gameState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0418\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043E\\u0431 \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0424\\u0430\\u0437\\u0430:\"\n          }), \" \", gameState.phase]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u0438:\"\n          }), \" \", gameState.players.length]\n        }), gameState.trumpCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C:\"\n          }), \" \", gameState.trumpCard.rank, \" \", getSuitSymbol(gameState.trumpCard.suit)]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u041A\\u0430\\u0440\\u0442 \\u0432 \\u043A\\u043E\\u043B\\u043E\\u0434\\u0435:\"\n          }), \" \", gameState.deck.length]\n        }), gameState.currentPlayerIndex >= 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0425\\u043E\\u0434 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430:\"\n          }), \" \", gameState.players[gameState.currentPlayerIndex]?.name]\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"p\", {\n          style: {\n            color: 'green'\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"strong\", {\n            children: \"\\u0412\\u0430\\u0448 \\u0445\\u043E\\u0434!\"\n          })\n        })]\n      }), currentPlayer && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"h3\", {\n          children: [\"\\u0412\\u0430\\u0448\\u0438 \\u043A\\u0430\\u0440\\u0442\\u044B (\", currentPlayer.name, \")\"]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlayerHand, {\n          children: currentPlayer.hand.map((card, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n            isPlayable: canPlayCard(card) && isMyTurn,\n            onClick: () => handleCardClick(card),\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n              children: card.rank\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n              children: getSuitSymbol(card.suit)\n            })]\n          }, `${card.suit}-${card.rank}-${index}`))\n        })]\n      }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: () => handleAction('pass'),\n          children: \"\\u041F\\u0430\\u0441\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Button, {\n          onClick: () => handleAction('take'),\n          children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n        })]\n      }), gameState?.table && gameState.table.length > 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0421\\u0442\\u043E\\u043B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlayerHand, {\n          children: gameState.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: tableCard.attackCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.attackCard.suit)\n              })]\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: tableCard.defendCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.defendCard.suit)\n              })]\n            })]\n          }, index))\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"h3\", {\n          children: \"\\u0421\\u043E\\u0431\\u044B\\u0442\\u0438\\u044F \\u0438\\u0433\\u0440\\u044B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(EventLog, {\n          children: gameEvents.map((event, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Event, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"strong\", {\n              children: [new Date(event.timestamp).toLocaleTimeString(), \":\"]\n            }), \" \", event.message]\n          }, index))\n        })]\n      })]\n    })]\n  });\n};\n_s(TestDurakPage, \"ljZnkMQcU7eUsqPGwYcgAa2a7T0=\", false, function () {\n  return [_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame];\n});\n_c10 = TestDurakPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TestDurakPage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"GameArea\");\n$RefreshReg$(_c3, \"Controls\");\n$RefreshReg$(_c4, \"Button\");\n$RefreshReg$(_c5, \"GameInfo\");\n$RefreshReg$(_c6, \"PlayerHand\");\n$RefreshReg$(_c7, \"Card\");\n$RefreshReg$(_c8, \"EventLog\");\n$RefreshReg$(_c9, \"Event\");\n$RefreshReg$(_c10, \"TestDurakPage\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/test-durak.tsx\n"));

/***/ })

});