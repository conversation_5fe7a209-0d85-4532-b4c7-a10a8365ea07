"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message,\n          data: event.data\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_started',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const player = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isBot\n      };\n      game.addPlayer(player);\n      updateGameState();\n      addEvent({\n        type: 'player_joined',\n        playerId: player.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, updateGameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n      }\n      game.start();\n      updateGameState();\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const result = game.makeMove(action);\n      updateGameState();\n      if (result.success) {\n        addEvent({\n          type: 'card_played',\n          playerId: action.playerId,\n          message: `Игрок выполнил действие: ${action.action}`,\n          data: action\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.phase === 'finished') {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.players.find(p => p.id === newState.winner)?.name}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: action.playerId,\n          message: result.error || 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});