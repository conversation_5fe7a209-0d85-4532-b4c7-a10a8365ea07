"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/pages/test-durak.tsx":
/*!**********************************!*\
  !*** ./src/pages/test-durak.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var _hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useDurakGame */ \"./src/hooks/useDurakGame.ts\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Container = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n`;\n_c = Container;\nconst GameArea = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c2 = GameArea;\nconst Controls = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n_c3 = Controls;\nconst Button = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].button`\n  padding: 10px 20px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 5px;\n  cursor: pointer;\n  \n  &:hover {\n    background: #0056b3;\n  }\n  \n  &:disabled {\n    background: #ccc;\n    cursor: not-allowed;\n  }\n`;\n_c4 = Button;\nconst GameInfo = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c5 = GameInfo;\nconst PlayerHand = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n  margin-top: 10px;\n`;\n_c6 = PlayerHand;\nconst Card = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  width: 60px;\n  height: 80px;\n  border: 2px solid ${props => props.isPlayable ? '#28a745' : '#dee2e6'};\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};\n  font-size: 12px;\n  text-align: center;\n  \n  &:hover {\n    ${props => props.isPlayable && `\n      border-color: #1e7e34;\n      transform: translateY(-2px);\n    `}\n  }\n`;\n_c7 = Card;\nconst EventLog = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 5px;\n  border: 1px solid #dee2e6;\n`;\n_c8 = EventLog;\nconst Event = styled_components__WEBPACK_IMPORTED_MODULE_5__[\"default\"].div`\n  padding: 5px 0;\n  border-bottom: 1px solid #eee;\n  font-size: 14px;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c9 = Event;\nconst TestDurakPage = () => {\n  _s();\n  const {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    resetGame\n  } = (0,_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame)();\n  const handleCreateGame = () => {\n    const rules = {\n      variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.DurakVariant.CLASSIC,\n      numberOfPlayers: 2,\n      initialHandSize: 6,\n      attackLimit: 6\n    };\n    createNewGame(rules);\n  };\n  const handleAddPlayer = () => {\n    addPlayer('Игрок 1');\n  };\n  const handleAddBot = () => {\n    addPlayer('Бот', true);\n  };\n  const handleCardClick = (card, cardIndex) => {\n    if (!canPlayCard(card) || !isMyTurn) return;\n\n    // Пробуем атаку с этой картой\n    makeMove(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.ATTACK, cardIndex);\n  };\n  const handleAction = actionType => {\n    let action;\n    switch (actionType) {\n      case 'pass':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.PASS;\n        break;\n      case 'take':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.TAKE;\n        break;\n      case 'defend':\n        action = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.PlayerAction.DEFEND;\n        break;\n      default:\n        return;\n    }\n    makeMove(action);\n  };\n  const getSuitSymbol = suit => {\n    const symbols = {\n      hearts: '♥️',\n      diamonds: '♦️',\n      clubs: '♣️',\n      spades: '♠️'\n    };\n    return symbols[suit] || suit;\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"title\", {\n        children: \"\\u0422\\u0435\\u0441\\u0442 \\u0414\\u0443\\u0440\\u0430\\u043A - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h1\", {\n      children: \"\\u0422\\u0435\\u0441\\u0442 \\u0438\\u0433\\u0440\\u044B \\u0414\\u0443\\u0440\\u0430\\u043A\"\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameArea, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleCreateGame,\n          disabled: !!game,\n          children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleAddPlayer,\n          disabled: !game || !!gameState?.players.find(p => !p.isBot),\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: handleAddBot,\n          disabled: !game,\n          children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u0442\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: startGame,\n          disabled: !game || gameState?.gameStatus !== _kozyr_master_core__WEBPACK_IMPORTED_MODULE_3__.GameStatus.NOT_STARTED,\n          children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: resetGame,\n          children: \"\\u0421\\u0431\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n        })]\n      }), gameState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(GameInfo, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0418\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043E\\u0431 \\u0438\\u0433\\u0440\\u0435\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0421\\u0442\\u0430\\u0442\\u0443\\u0441:\"\n          }), \" \", gameState.gameStatus]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u0438:\"\n          }), \" \", gameState.players.length]\n        }), gameState.trumpCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C:\"\n          }), \" \", gameState.trumpCard.rank, \" \", getSuitSymbol(gameState.trumpCard.suit)]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u041A\\u0430\\u0440\\u0442 \\u0432 \\u043A\\u043E\\u043B\\u043E\\u0434\\u0435:\"\n          }), \" \", gameState.deck.length]\n        }), gameState.currentPlayerIndex >= 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"p\", {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0425\\u043E\\u0434 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430:\"\n          }), \" \", gameState.players[gameState.currentPlayerIndex]?.name || gameState.players[gameState.currentPlayerIndex]?.id]\n        }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"p\", {\n          style: {\n            color: 'green'\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"strong\", {\n            children: \"\\u0412\\u0430\\u0448 \\u0445\\u043E\\u0434!\"\n          })\n        })]\n      }), currentPlayer && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"h3\", {\n          children: [\"\\u0412\\u0430\\u0448\\u0438 \\u043A\\u0430\\u0440\\u0442\\u044B (\", currentPlayer.name || currentPlayer.id, \")\"]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerHand, {\n          children: currentPlayer.hand.map((card, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n            isPlayable: canPlayCard(card) && isMyTurn,\n            onClick: () => handleCardClick(card, index),\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n              children: card.rank\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n              children: getSuitSymbol(card.suit)\n            })]\n          }, `${card.suit}-${card.rank}-${index}`))\n        })]\n      }), isMyTurn && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Controls, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: () => handleAction('pass'),\n          children: \"\\u041F\\u0430\\u0441\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Button, {\n          onClick: () => handleAction('take'),\n          children: \"\\u0412\\u0437\\u044F\\u0442\\u044C \\u043A\\u0430\\u0440\\u0442\\u044B\"\n        })]\n      }), gameState?.table && gameState.table.length > 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0421\\u0442\\u043E\\u043B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(PlayerHand, {\n          children: gameState.table.map((tableCard, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '5px'\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: tableCard.attackCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.attackCard.suit)\n              })]\n            }), tableCard.defendCard && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Card, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: tableCard.defendCard.rank\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", {\n                children: getSuitSymbol(tableCard.defendCard.suit)\n              })]\n            })]\n          }, index))\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"div\", {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"h3\", {\n          children: \"\\u0421\\u043E\\u0431\\u044B\\u0442\\u0438\\u044F \\u0438\\u0433\\u0440\\u044B\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(EventLog, {\n          children: gameEvents.map((event, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(Event, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(\"strong\", {\n              children: [new Date(event.timestamp).toLocaleTimeString(), \":\"]\n            }), \" \", event.message]\n          }, index))\n        })]\n      })]\n    })]\n  });\n};\n_s(TestDurakPage, \"aq9NcMA/pY19Dl8ODsGzko8SlEQ=\", false, function () {\n  return [_hooks_useDurakGame__WEBPACK_IMPORTED_MODULE_2__.useDurakGame];\n});\n_c10 = TestDurakPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TestDurakPage);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"GameArea\");\n$RefreshReg$(_c3, \"Controls\");\n$RefreshReg$(_c4, \"Button\");\n$RefreshReg$(_c5, \"GameInfo\");\n$RefreshReg$(_c6, \"PlayerHand\");\n$RefreshReg$(_c7, \"Card\");\n$RefreshReg$(_c8, \"EventLog\");\n$RefreshReg$(_c9, \"Event\");\n$RefreshReg$(_c10, \"TestDurakPage\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/test-durak.tsx\n"));

/***/ })

});