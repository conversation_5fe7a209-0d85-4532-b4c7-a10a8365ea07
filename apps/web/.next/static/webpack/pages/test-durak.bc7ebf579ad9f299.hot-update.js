"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_created',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока (пересоздание игры с новыми игроками)\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const currentState = game.getState();\n      const newPlayer = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isActive: false\n      };\n\n      // Создаем новую игру с обновленным списком игроков\n      const allPlayers = [...currentState.players, newPlayer];\n      const rules = {\n        variant: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakVariant.CLASSIC,\n        numberOfPlayers: allPlayers.length,\n        initialHandSize: 6,\n        attackLimit: 6\n      };\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(allPlayers, rules);\n\n      // Подписываемся на события новой игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message || `Game event: ${event.type}`,\n          playerId: event.playerId,\n          data: event\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      addEvent({\n        type: 'player_joined',\n        playerId: newPlayer.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n        return; // addPlayer пересоздаст игру, поэтому выходим\n      }\n\n      game.startGame();\n      updateGameState();\n      addEvent({\n        type: 'game_started',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action, cardIndex) => {\n    if (!game) {\n      addEvent({\n        type: 'error',\n        message: 'Игра не создана'\n      });\n      return;\n    }\n    try {\n      const success = game.makeMove(currentPlayerId, action, cardIndex);\n      updateGameState();\n      if (success) {\n        addEvent({\n          type: 'player_moved',\n          playerId: currentPlayerId,\n          message: `Игрок выполнил действие: ${action}`,\n          data: {\n            action,\n            cardIndex\n          }\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.FINISHED) {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.winner.name || newState.winner.id}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: currentPlayerId,\n          message: 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: currentPlayerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, currentPlayerId, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход или можем ли мы играть\n    const isMyTurn = gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n    const isActive = currentPlayer.isActive;\n    return isMyTurn || isActive;\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.FINISHED) return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer || !currentGamePlayer.name?.includes('бот') && !currentGamePlayer.name?.includes('ИИ')) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        // Простая логика: пробуем разные действия\n        const actions = [_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.PlayerAction.ATTACK, _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.PlayerAction.DEFEND, _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.PlayerAction.TAKE, _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.PlayerAction.PASS];\n        for (const action of actions) {\n          // Для атаки пробуем случайную карту\n          if (action === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.PlayerAction.ATTACK && currentGamePlayer.hand.length > 0) {\n            const randomCardIndex = Math.floor(Math.random() * currentGamePlayer.hand.length);\n            const success = game.makeMove(currentGamePlayer.id, action, randomCardIndex);\n            if (success) {\n              updateGameState();\n              break;\n            }\n          } else {\n            // Для других действий не нужен индекс карты\n            const success = game.makeMove(currentGamePlayer.id, action);\n            if (success) {\n              updateGameState();\n              break;\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, updateGameState]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"fUcreo2Y7kHxjTsvPRH8smxk75o=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});