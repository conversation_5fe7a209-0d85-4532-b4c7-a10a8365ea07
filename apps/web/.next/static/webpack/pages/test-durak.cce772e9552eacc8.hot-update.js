"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/test-durak",{

/***/ "./src/hooks/useDurakGame.ts":
/*!***********************************!*\
  !*** ./src/hooks/useDurakGame.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDurakGame: function() { return /* binding */ useDurakGame; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nconst useDurakGame = playerId => {\n  _s();\n  const [game, setGame] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [gameEvents, setGameEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [currentPlayerId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(playerId || `player-${Date.now()}`);\n\n  // Обновление состояния игры\n  const updateGameState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (game) {\n      setGameState(game.getState());\n    }\n  }, [game]);\n\n  // Добавление события\n  const addEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const newEvent = {\n      ...event,\n      timestamp: Date.now()\n    };\n    setGameEvents(prev => [...prev, newEvent]);\n  }, []);\n\n  // Создание новой игры\n  const createNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rules => {\n    try {\n      const players = [];\n      const newGame = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(players, rules);\n\n      // Подписываемся на события игры\n      newGame.addEventListener(event => {\n        addEvent({\n          type: event.type,\n          message: event.message,\n          data: event.data\n        });\n      });\n      setGame(newGame);\n      setGameState(newGame.getState());\n      setGameEvents([]);\n      addEvent({\n        type: 'game_started',\n        message: 'Новая игра создана'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка создания игры'\n      });\n    }\n  }, [addEvent]);\n\n  // Добавление игрока\n  const addPlayer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name, isBot = false) => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const player = {\n        id: isBot ? `bot-${Date.now()}` : currentPlayerId,\n        name,\n        hand: [],\n        isBot\n      };\n      game.addPlayer(player);\n      updateGameState();\n      addEvent({\n        type: 'player_joined',\n        playerId: player.id,\n        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'\n      });\n    }\n  }, [game, currentPlayerId, updateGameState, addEvent]);\n\n  // Начало игры\n  const startGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      // Добавляем бота если недостаточно игроков\n      const currentState = game.getState();\n      if (currentState.players.length < 2) {\n        addPlayer('ИИ Противник', true);\n      }\n      game.start();\n      updateGameState();\n      addEvent({\n        type: 'cards_dealt',\n        message: 'Карты розданы, игра началась!'\n      });\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Ошибка начала игры'\n      });\n    }\n  }, [game, addPlayer, updateGameState, addEvent]);\n\n  // Выполнение хода\n  const makeMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(action => {\n    if (!game) {\n      throw new Error('Игра не создана');\n    }\n    try {\n      const result = game.makeMove(action);\n      updateGameState();\n      if (result.success) {\n        addEvent({\n          type: 'card_played',\n          playerId: action.playerId,\n          message: `Игрок выполнил действие: ${action.action}`,\n          data: action\n        });\n\n        // Проверяем окончание игры\n        const newState = game.getState();\n        if (newState.phase === 'finished') {\n          addEvent({\n            type: 'game_finished',\n            message: newState.winner ? `Игра окончена! Победитель: ${newState.players.find(p => p.id === newState.winner)?.name}` : 'Игра окончена'\n          });\n        }\n      } else {\n        addEvent({\n          type: 'error',\n          playerId: action.playerId,\n          message: result.error || 'Недопустимый ход'\n        });\n      }\n    } catch (error) {\n      addEvent({\n        type: 'error',\n        playerId: action.playerId,\n        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'\n      });\n    }\n  }, [game, updateGameState, addEvent]);\n\n  // Проверка, можно ли сыграть карту\n  const canPlayCard = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(card => {\n    if (!game || !gameState) return false;\n    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);\n    if (!currentPlayer) return false;\n\n    // Проверяем, есть ли карта у игрока\n    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);\n    if (!hasCard) return false;\n\n    // Проверяем, наш ли ход\n    return gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);\n  }, [game, gameState, currentPlayerId]);\n\n  // Получение доступных ходов\n  const getValidMoves = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!game || !gameState) return [];\n    try {\n      return game.getValidMoves(currentPlayerId);\n    } catch {\n      return [];\n    }\n  }, [game, gameState, currentPlayerId]);\n\n  // Сброс игры\n  const resetGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setGame(null);\n    setGameState(null);\n    setGameEvents([]);\n  }, []);\n\n  // Вычисляемые значения\n  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;\n  const isMyTurn = gameState ? gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;\n\n  // Автоматические действия ботов\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!game || !gameState || gameState.phase === 'finished') return;\n    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];\n    if (!currentGamePlayer?.isBot) return;\n\n    // Простая логика бота - случайное действие через небольшую задержку\n    const timer = setTimeout(() => {\n      try {\n        const validMoves = game.getValidMoves(currentGamePlayer.id);\n        if (validMoves.length > 0) {\n          const randomMove = validMoves[Math.floor(Math.random() * validMoves.length)];\n          makeMove(randomMove);\n        }\n      } catch (error) {\n        console.error('Ошибка хода бота:', error);\n      }\n    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка\n\n    return () => clearTimeout(timer);\n  }, [game, gameState, makeMove]);\n  return {\n    game,\n    gameState,\n    currentPlayer,\n    isMyTurn,\n    gameEvents,\n    createNewGame,\n    addPlayer,\n    startGame,\n    makeMove,\n    canPlayCard,\n    getValidMoves,\n    resetGame\n  };\n};\n_s(useDurakGame, \"ie4r5E4dJtWBjbLwvFlxBl+/9dk=\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlRHVyYWtHYW1lLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5RDtBQVk3QjtBQTRCckIsTUFBTUksWUFBWSxHQUFJQyxRQUFpQixJQUF5QjtFQUFBQyxFQUFBO0VBQ3JFLE1BQU0sQ0FBQ0MsSUFBSSxFQUFFQyxPQUFPLENBQUMsR0FBR1IsK0NBQVEsQ0FBbUIsSUFBSSxDQUFDO0VBQ3hELE1BQU0sQ0FBQ1MsU0FBUyxFQUFFQyxZQUFZLENBQUMsR0FBR1YsK0NBQVEsQ0FBbUIsSUFBSSxDQUFDO0VBQ2xFLE1BQU0sQ0FBQ1csVUFBVSxFQUFFQyxhQUFhLENBQUMsR0FBR1osK0NBQVEsQ0FBYyxFQUFFLENBQUM7RUFDN0QsTUFBTSxDQUFDYSxlQUFlLENBQUMsR0FBR2IsK0NBQVEsQ0FDaENLLFFBQVEsSUFBSyxVQUFTUyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFFLEVBQ25DLENBQUM7O0VBRUQ7RUFDQSxNQUFNQyxlQUFlLEdBQUdmLGtEQUFXLENBQUMsTUFBTTtJQUN4QyxJQUFJTSxJQUFJLEVBQUU7TUFDUkcsWUFBWSxDQUFDSCxJQUFJLENBQUNVLFFBQVEsQ0FBQyxDQUFDLENBQUM7SUFDL0I7RUFDRixDQUFDLEVBQUUsQ0FBQ1YsSUFBSSxDQUFDLENBQUM7O0VBRVY7RUFDQSxNQUFNVyxRQUFRLEdBQUdqQixrREFBVyxDQUFFa0IsS0FBbUMsSUFBSztJQUNwRSxNQUFNQyxRQUFtQixHQUFHO01BQzFCLEdBQUdELEtBQUs7TUFDUkUsU0FBUyxFQUFFUCxJQUFJLENBQUNDLEdBQUcsQ0FBQztJQUN0QixDQUFDO0lBQ0RILGFBQWEsQ0FBQ1UsSUFBSSxJQUFJLENBQUMsR0FBR0EsSUFBSSxFQUFFRixRQUFRLENBQUMsQ0FBQztFQUM1QyxDQUFDLEVBQUUsRUFBRSxDQUFDOztFQUVOO0VBQ0EsTUFBTUcsYUFBYSxHQUFHdEIsa0RBQVcsQ0FBRXVCLEtBQWdCLElBQUs7SUFDdEQsSUFBSTtNQUNGLE1BQU1DLE9BQWlCLEdBQUcsRUFBRTtNQUM1QixNQUFNQyxPQUFPLEdBQUcsSUFBSXZCLHlEQUFTLENBQUNzQixPQUFPLEVBQUVELEtBQUssQ0FBQzs7TUFFN0M7TUFDQUUsT0FBTyxDQUFDQyxnQkFBZ0IsQ0FBRVIsS0FBSyxJQUFLO1FBQ2xDRCxRQUFRLENBQUM7VUFDUFUsSUFBSSxFQUFFVCxLQUFLLENBQUNTLElBQVc7VUFDdkJDLE9BQU8sRUFBRVYsS0FBSyxDQUFDVSxPQUFPO1VBQ3RCQyxJQUFJLEVBQUVYLEtBQUssQ0FBQ1c7UUFDZCxDQUFDLENBQUM7TUFDSixDQUFDLENBQUM7TUFFRnRCLE9BQU8sQ0FBQ2tCLE9BQU8sQ0FBQztNQUNoQmhCLFlBQVksQ0FBQ2dCLE9BQU8sQ0FBQ1QsUUFBUSxDQUFDLENBQUMsQ0FBQztNQUNoQ0wsYUFBYSxDQUFDLEVBQUUsQ0FBQztNQUVqQk0sUUFBUSxDQUFDO1FBQ1BVLElBQUksRUFBRSxjQUFjO1FBQ3BCQyxPQUFPLEVBQUU7TUFDWCxDQUFDLENBQUM7SUFDSixDQUFDLENBQUMsT0FBT0UsS0FBSyxFQUFFO01BQ2RiLFFBQVEsQ0FBQztRQUNQVSxJQUFJLEVBQUUsT0FBTztRQUNiQyxPQUFPLEVBQUVFLEtBQUssWUFBWUMsS0FBSyxHQUFHRCxLQUFLLENBQUNGLE9BQU8sR0FBRztNQUNwRCxDQUFDLENBQUM7SUFDSjtFQUNGLENBQUMsRUFBRSxDQUFDWCxRQUFRLENBQUMsQ0FBQzs7RUFFZDtFQUNBLE1BQU1lLFNBQVMsR0FBR2hDLGtEQUFXLENBQUMsQ0FBQ2lDLElBQVksRUFBRUMsS0FBYyxHQUFHLEtBQUssS0FBSztJQUN0RSxJQUFJLENBQUM1QixJQUFJLEVBQUU7TUFDVCxNQUFNLElBQUl5QixLQUFLLENBQUMsaUJBQWlCLENBQUM7SUFDcEM7SUFFQSxJQUFJO01BQ0YsTUFBTUksTUFBYyxHQUFHO1FBQ3JCQyxFQUFFLEVBQUVGLEtBQUssR0FBSSxPQUFNckIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBRSxFQUFDLEdBQUdGLGVBQWU7UUFDakRxQixJQUFJO1FBQ0pJLElBQUksRUFBRSxFQUFFO1FBQ1JIO01BQ0YsQ0FBQztNQUVENUIsSUFBSSxDQUFDMEIsU0FBUyxDQUFDRyxNQUFNLENBQUM7TUFDdEJwQixlQUFlLENBQUMsQ0FBQztNQUVqQkUsUUFBUSxDQUFDO1FBQ1BVLElBQUksRUFBRSxlQUFlO1FBQ3JCdkIsUUFBUSxFQUFFK0IsTUFBTSxDQUFDQyxFQUFFO1FBQ25CUixPQUFPLEVBQUcsR0FBRUssSUFBSyxJQUFHQyxLQUFLLEdBQUcsT0FBTyxHQUFHLEVBQUc7TUFDM0MsQ0FBQyxDQUFDO0lBQ0osQ0FBQyxDQUFDLE9BQU9KLEtBQUssRUFBRTtNQUNkYixRQUFRLENBQUM7UUFDUFUsSUFBSSxFQUFFLE9BQU87UUFDYkMsT0FBTyxFQUFFRSxLQUFLLFlBQVlDLEtBQUssR0FBR0QsS0FBSyxDQUFDRixPQUFPLEdBQUc7TUFDcEQsQ0FBQyxDQUFDO0lBQ0o7RUFDRixDQUFDLEVBQUUsQ0FBQ3RCLElBQUksRUFBRU0sZUFBZSxFQUFFRyxlQUFlLEVBQUVFLFFBQVEsQ0FBQyxDQUFDOztFQUV0RDtFQUNBLE1BQU1xQixTQUFTLEdBQUd0QyxrREFBVyxDQUFDLE1BQU07SUFDbEMsSUFBSSxDQUFDTSxJQUFJLEVBQUU7TUFDVCxNQUFNLElBQUl5QixLQUFLLENBQUMsaUJBQWlCLENBQUM7SUFDcEM7SUFFQSxJQUFJO01BQ0Y7TUFDQSxNQUFNUSxZQUFZLEdBQUdqQyxJQUFJLENBQUNVLFFBQVEsQ0FBQyxDQUFDO01BQ3BDLElBQUl1QixZQUFZLENBQUNmLE9BQU8sQ0FBQ2dCLE1BQU0sR0FBRyxDQUFDLEVBQUU7UUFDbkNSLFNBQVMsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDO01BQ2pDO01BRUExQixJQUFJLENBQUNtQyxLQUFLLENBQUMsQ0FBQztNQUNaMUIsZUFBZSxDQUFDLENBQUM7TUFFakJFLFFBQVEsQ0FBQztRQUNQVSxJQUFJLEVBQUUsYUFBYTtRQUNuQkMsT0FBTyxFQUFFO01BQ1gsQ0FBQyxDQUFDO0lBQ0osQ0FBQyxDQUFDLE9BQU9FLEtBQUssRUFBRTtNQUNkYixRQUFRLENBQUM7UUFDUFUsSUFBSSxFQUFFLE9BQU87UUFDYkMsT0FBTyxFQUFFRSxLQUFLLFlBQVlDLEtBQUssR0FBR0QsS0FBSyxDQUFDRixPQUFPLEdBQUc7TUFDcEQsQ0FBQyxDQUFDO0lBQ0o7RUFDRixDQUFDLEVBQUUsQ0FBQ3RCLElBQUksRUFBRTBCLFNBQVMsRUFBRWpCLGVBQWUsRUFBRUUsUUFBUSxDQUFDLENBQUM7O0VBRWhEO0VBQ0EsTUFBTXlCLFFBQVEsR0FBRzFDLGtEQUFXLENBQUUyQyxNQUFvQixJQUFLO0lBQ3JELElBQUksQ0FBQ3JDLElBQUksRUFBRTtNQUNULE1BQU0sSUFBSXlCLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQztJQUNwQztJQUVBLElBQUk7TUFDRixNQUFNYSxNQUFNLEdBQUd0QyxJQUFJLENBQUNvQyxRQUFRLENBQUNDLE1BQU0sQ0FBQztNQUNwQzVCLGVBQWUsQ0FBQyxDQUFDO01BRWpCLElBQUk2QixNQUFNLENBQUNDLE9BQU8sRUFBRTtRQUNsQjVCLFFBQVEsQ0FBQztVQUNQVSxJQUFJLEVBQUUsYUFBYTtVQUNuQnZCLFFBQVEsRUFBRXVDLE1BQU0sQ0FBQ3ZDLFFBQVE7VUFDekJ3QixPQUFPLEVBQUcsNEJBQTJCZSxNQUFNLENBQUNBLE1BQU8sRUFBQztVQUNwRGQsSUFBSSxFQUFFYztRQUNSLENBQUMsQ0FBQzs7UUFFRjtRQUNBLE1BQU1HLFFBQVEsR0FBR3hDLElBQUksQ0FBQ1UsUUFBUSxDQUFDLENBQUM7UUFDaEMsSUFBSThCLFFBQVEsQ0FBQ0MsS0FBSyxLQUFLLFVBQVUsRUFBRTtVQUNqQzlCLFFBQVEsQ0FBQztZQUNQVSxJQUFJLEVBQUUsZUFBZTtZQUNyQkMsT0FBTyxFQUFFa0IsUUFBUSxDQUFDRSxNQUFNLEdBQ25CLDhCQUE2QkYsUUFBUSxDQUFDdEIsT0FBTyxDQUFDeUIsSUFBSSxDQUFDQyxDQUFDLElBQUlBLENBQUMsQ0FBQ2QsRUFBRSxLQUFLVSxRQUFRLENBQUNFLE1BQU0sQ0FBQyxFQUFFZixJQUFLLEVBQUMsR0FDMUY7VUFDTixDQUFDLENBQUM7UUFDSjtNQUNGLENBQUMsTUFBTTtRQUNMaEIsUUFBUSxDQUFDO1VBQ1BVLElBQUksRUFBRSxPQUFPO1VBQ2J2QixRQUFRLEVBQUV1QyxNQUFNLENBQUN2QyxRQUFRO1VBQ3pCd0IsT0FBTyxFQUFFZ0IsTUFBTSxDQUFDZCxLQUFLLElBQUk7UUFDM0IsQ0FBQyxDQUFDO01BQ0o7SUFDRixDQUFDLENBQUMsT0FBT0EsS0FBSyxFQUFFO01BQ2RiLFFBQVEsQ0FBQztRQUNQVSxJQUFJLEVBQUUsT0FBTztRQUNidkIsUUFBUSxFQUFFdUMsTUFBTSxDQUFDdkMsUUFBUTtRQUN6QndCLE9BQU8sRUFBRUUsS0FBSyxZQUFZQyxLQUFLLEdBQUdELEtBQUssQ0FBQ0YsT0FBTyxHQUFHO01BQ3BELENBQUMsQ0FBQztJQUNKO0VBQ0YsQ0FBQyxFQUFFLENBQUN0QixJQUFJLEVBQUVTLGVBQWUsRUFBRUUsUUFBUSxDQUFDLENBQUM7O0VBRXJDO0VBQ0EsTUFBTWtDLFdBQVcsR0FBR25ELGtEQUFXLENBQUVvRCxJQUFVLElBQWM7SUFDdkQsSUFBSSxDQUFDOUMsSUFBSSxJQUFJLENBQUNFLFNBQVMsRUFBRSxPQUFPLEtBQUs7SUFFckMsTUFBTTZDLGFBQWEsR0FBRzdDLFNBQVMsQ0FBQ2dCLE9BQU8sQ0FBQ3lCLElBQUksQ0FBQ0MsQ0FBQyxJQUFJQSxDQUFDLENBQUNkLEVBQUUsS0FBS3hCLGVBQWUsQ0FBQztJQUMzRSxJQUFJLENBQUN5QyxhQUFhLEVBQUUsT0FBTyxLQUFLOztJQUVoQztJQUNBLE1BQU1DLE9BQU8sR0FBR0QsYUFBYSxDQUFDaEIsSUFBSSxDQUFDa0IsSUFBSSxDQUFDQyxDQUFDLElBQUlBLENBQUMsQ0FBQ0MsSUFBSSxLQUFLTCxJQUFJLENBQUNLLElBQUksSUFBSUQsQ0FBQyxDQUFDRSxJQUFJLEtBQUtOLElBQUksQ0FBQ00sSUFBSSxDQUFDO0lBQzFGLElBQUksQ0FBQ0osT0FBTyxFQUFFLE9BQU8sS0FBSzs7SUFFMUI7SUFDQSxPQUFPOUMsU0FBUyxDQUFDbUQsa0JBQWtCLEtBQUtuRCxTQUFTLENBQUNnQixPQUFPLENBQUNvQyxTQUFTLENBQUNWLENBQUMsSUFBSUEsQ0FBQyxDQUFDZCxFQUFFLEtBQUt4QixlQUFlLENBQUM7RUFDcEcsQ0FBQyxFQUFFLENBQUNOLElBQUksRUFBRUUsU0FBUyxFQUFFSSxlQUFlLENBQUMsQ0FBQzs7RUFFdEM7RUFDQSxNQUFNaUQsYUFBYSxHQUFHN0Qsa0RBQVcsQ0FBQyxNQUFzQjtJQUN0RCxJQUFJLENBQUNNLElBQUksSUFBSSxDQUFDRSxTQUFTLEVBQUUsT0FBTyxFQUFFO0lBRWxDLElBQUk7TUFDRixPQUFPRixJQUFJLENBQUN1RCxhQUFhLENBQUNqRCxlQUFlLENBQUM7SUFDNUMsQ0FBQyxDQUFDLE1BQU07TUFDTixPQUFPLEVBQUU7SUFDWDtFQUNGLENBQUMsRUFBRSxDQUFDTixJQUFJLEVBQUVFLFNBQVMsRUFBRUksZUFBZSxDQUFDLENBQUM7O0VBRXRDO0VBQ0EsTUFBTWtELFNBQVMsR0FBRzlELGtEQUFXLENBQUMsTUFBTTtJQUNsQ08sT0FBTyxDQUFDLElBQUksQ0FBQztJQUNiRSxZQUFZLENBQUMsSUFBSSxDQUFDO0lBQ2xCRSxhQUFhLENBQUMsRUFBRSxDQUFDO0VBQ25CLENBQUMsRUFBRSxFQUFFLENBQUM7O0VBRU47RUFDQSxNQUFNMEMsYUFBYSxHQUFHN0MsU0FBUyxFQUFFZ0IsT0FBTyxDQUFDeUIsSUFBSSxDQUFDQyxDQUFDLElBQUlBLENBQUMsQ0FBQ2QsRUFBRSxLQUFLeEIsZUFBZSxDQUFDLElBQUksSUFBSTtFQUNwRixNQUFNbUQsUUFBUSxHQUFHdkQsU0FBUyxHQUN4QkEsU0FBUyxDQUFDZ0IsT0FBTyxDQUFDaEIsU0FBUyxDQUFDbUQsa0JBQWtCLENBQUMsRUFBRXZCLEVBQUUsS0FBS3hCLGVBQWUsR0FBRyxLQUFLOztFQUVqRjtFQUNBWCxnREFBUyxDQUFDLE1BQU07SUFDZCxJQUFJLENBQUNLLElBQUksSUFBSSxDQUFDRSxTQUFTLElBQUlBLFNBQVMsQ0FBQ3VDLEtBQUssS0FBSyxVQUFVLEVBQUU7SUFFM0QsTUFBTWlCLGlCQUFpQixHQUFHeEQsU0FBUyxDQUFDZ0IsT0FBTyxDQUFDaEIsU0FBUyxDQUFDbUQsa0JBQWtCLENBQUM7SUFDekUsSUFBSSxDQUFDSyxpQkFBaUIsRUFBRTlCLEtBQUssRUFBRTs7SUFFL0I7SUFDQSxNQUFNK0IsS0FBSyxHQUFHQyxVQUFVLENBQUMsTUFBTTtNQUM3QixJQUFJO1FBQ0YsTUFBTUMsVUFBVSxHQUFHN0QsSUFBSSxDQUFDdUQsYUFBYSxDQUFDRyxpQkFBaUIsQ0FBQzVCLEVBQUUsQ0FBQztRQUMzRCxJQUFJK0IsVUFBVSxDQUFDM0IsTUFBTSxHQUFHLENBQUMsRUFBRTtVQUN6QixNQUFNNEIsVUFBVSxHQUFHRCxVQUFVLENBQUNFLElBQUksQ0FBQ0MsS0FBSyxDQUFDRCxJQUFJLENBQUNFLE1BQU0sQ0FBQyxDQUFDLEdBQUdKLFVBQVUsQ0FBQzNCLE1BQU0sQ0FBQyxDQUFDO1VBQzVFRSxRQUFRLENBQUMwQixVQUFVLENBQUM7UUFDdEI7TUFDRixDQUFDLENBQUMsT0FBT3RDLEtBQUssRUFBRTtRQUNkMEMsT0FBTyxDQUFDMUMsS0FBSyxDQUFDLG1CQUFtQixFQUFFQSxLQUFLLENBQUM7TUFDM0M7SUFDRixDQUFDLEVBQUUsSUFBSSxHQUFHdUMsSUFBSSxDQUFDRSxNQUFNLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUM7O0lBRWpDLE9BQU8sTUFBTUUsWUFBWSxDQUFDUixLQUFLLENBQUM7RUFDbEMsQ0FBQyxFQUFFLENBQUMzRCxJQUFJLEVBQUVFLFNBQVMsRUFBRWtDLFFBQVEsQ0FBQyxDQUFDO0VBRS9CLE9BQU87SUFDTHBDLElBQUk7SUFDSkUsU0FBUztJQUNUNkMsYUFBYTtJQUNiVSxRQUFRO0lBQ1JyRCxVQUFVO0lBRVZZLGFBQWE7SUFDYlUsU0FBUztJQUNUTSxTQUFTO0lBQ1RJLFFBQVE7SUFFUlMsV0FBVztJQUNYVSxhQUFhO0lBQ2JDO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFBQ3pELEVBQUEsQ0ExT1dGLFlBQVkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2hvb2tzL3VzZUR1cmFrR2FtZS50cz9lMjgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgRHVyYWtHYW1lLFxuICBHYW1lU3RhdGUsXG4gIFBsYXllcixcbiAgQ2FyZCxcbiAgR2FtZVJ1bGVzLFxuICBQbGF5ZXJBY3Rpb24sXG4gIEdhbWVFdmVudCxcbiAgR2FtZUV2ZW50RGF0YSxcbiAgRHVyYWtWYXJpYW50LFxuICBHYW1lU3RhdHVzXG59IGZyb20gJ0Brb3p5ci1tYXN0ZXIvY29yZSc7XG5cbmludGVyZmFjZSBHYW1lRXZlbnRMb2cge1xuICB0eXBlOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIHBsYXllcklkPzogc3RyaW5nO1xuICBkYXRhPzogYW55O1xufVxuXG5pbnRlcmZhY2UgVXNlRHVyYWtHYW1lUmV0dXJuIHtcbiAgZ2FtZTogRHVyYWtHYW1lIHwgbnVsbDtcbiAgZ2FtZVN0YXRlOiBHYW1lU3RhdGUgfCBudWxsO1xuICBjdXJyZW50UGxheWVyOiBQbGF5ZXIgfCBudWxsO1xuICBpc015VHVybjogYm9vbGVhbjtcbiAgZ2FtZUV2ZW50czogR2FtZUV2ZW50TG9nW107XG5cbiAgLy8g0JTQtdC50YHRgtCy0LjRj1xuICBjcmVhdGVOZXdHYW1lOiAocnVsZXM6IEdhbWVSdWxlcykgPT4gdm9pZDtcbiAgYWRkUGxheWVyOiAobmFtZTogc3RyaW5nLCBpc0JvdD86IGJvb2xlYW4pID0+IHZvaWQ7XG4gIHN0YXJ0R2FtZTogKCkgPT4gdm9pZDtcbiAgbWFrZU1vdmU6IChhY3Rpb246IFBsYXllckFjdGlvbiwgY2FyZEluZGV4PzogbnVtYmVyKSA9PiB2b2lkO1xuXG4gIC8vINCj0YLQuNC70LjRgtGLXG4gIGNhblBsYXlDYXJkOiAoY2FyZDogQ2FyZCkgPT4gYm9vbGVhbjtcbiAgcmVzZXRHYW1lOiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgY29uc3QgdXNlRHVyYWtHYW1lID0gKHBsYXllcklkPzogc3RyaW5nKTogVXNlRHVyYWtHYW1lUmV0dXJuID0+IHtcbiAgY29uc3QgW2dhbWUsIHNldEdhbWVdID0gdXNlU3RhdGU8RHVyYWtHYW1lIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtnYW1lU3RhdGUsIHNldEdhbWVTdGF0ZV0gPSB1c2VTdGF0ZTxHYW1lU3RhdGUgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2dhbWVFdmVudHMsIHNldEdhbWVFdmVudHNdID0gdXNlU3RhdGU8R2FtZUV2ZW50W10+KFtdKTtcbiAgY29uc3QgW2N1cnJlbnRQbGF5ZXJJZF0gPSB1c2VTdGF0ZTxzdHJpbmc+KFxuICAgIHBsYXllcklkIHx8IGBwbGF5ZXItJHtEYXRlLm5vdygpfWBcbiAgKTtcblxuICAvLyDQntCx0L3QvtCy0LvQtdC90LjQtSDRgdC+0YHRgtC+0Y/QvdC40Y8g0LjQs9GA0YtcbiAgY29uc3QgdXBkYXRlR2FtZVN0YXRlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChnYW1lKSB7XG4gICAgICBzZXRHYW1lU3RhdGUoZ2FtZS5nZXRTdGF0ZSgpKTtcbiAgICB9XG4gIH0sIFtnYW1lXSk7XG5cbiAgLy8g0JTQvtCx0LDQstC70LXQvdC40LUg0YHQvtCx0YvRgtC40Y9cbiAgY29uc3QgYWRkRXZlbnQgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IE9taXQ8R2FtZUV2ZW50LCAndGltZXN0YW1wJz4pID0+IHtcbiAgICBjb25zdCBuZXdFdmVudDogR2FtZUV2ZW50ID0ge1xuICAgICAgLi4uZXZlbnQsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICB9O1xuICAgIHNldEdhbWVFdmVudHMocHJldiA9PiBbLi4ucHJldiwgbmV3RXZlbnRdKTtcbiAgfSwgW10pO1xuXG4gIC8vINCh0L7Qt9C00LDQvdC40LUg0L3QvtCy0L7QuSDQuNCz0YDRi1xuICBjb25zdCBjcmVhdGVOZXdHYW1lID0gdXNlQ2FsbGJhY2soKHJ1bGVzOiBHYW1lUnVsZXMpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcGxheWVyczogUGxheWVyW10gPSBbXTtcbiAgICAgIGNvbnN0IG5ld0dhbWUgPSBuZXcgRHVyYWtHYW1lKHBsYXllcnMsIHJ1bGVzKTtcbiAgICAgIFxuICAgICAgLy8g0J/QvtC00L/QuNGB0YvQstCw0LXQvNGB0Y8g0L3QsCDRgdC+0LHRi9GC0LjRjyDQuNCz0YDRi1xuICAgICAgbmV3R2FtZS5hZGRFdmVudExpc3RlbmVyKChldmVudCkgPT4ge1xuICAgICAgICBhZGRFdmVudCh7XG4gICAgICAgICAgdHlwZTogZXZlbnQudHlwZSBhcyBhbnksXG4gICAgICAgICAgbWVzc2FnZTogZXZlbnQubWVzc2FnZSxcbiAgICAgICAgICBkYXRhOiBldmVudC5kYXRhXG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIHNldEdhbWUobmV3R2FtZSk7XG4gICAgICBzZXRHYW1lU3RhdGUobmV3R2FtZS5nZXRTdGF0ZSgpKTtcbiAgICAgIHNldEdhbWVFdmVudHMoW10pO1xuICAgICAgXG4gICAgICBhZGRFdmVudCh7XG4gICAgICAgIHR5cGU6ICdnYW1lX3N0YXJ0ZWQnLFxuICAgICAgICBtZXNzYWdlOiAn0J3QvtCy0LDRjyDQuNCz0YDQsCDRgdC+0LfQtNCw0L3QsCdcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBhZGRFdmVudCh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ9Ce0YjQuNCx0LrQsCDRgdC+0LfQtNCw0L3QuNGPINC40LPRgNGLJ1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbYWRkRXZlbnRdKTtcblxuICAvLyDQlNC+0LHQsNCy0LvQtdC90LjQtSDQuNCz0YDQvtC60LBcbiAgY29uc3QgYWRkUGxheWVyID0gdXNlQ2FsbGJhY2soKG5hbWU6IHN0cmluZywgaXNCb3Q6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgIGlmICghZ2FtZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfQmNCz0YDQsCDQvdC1INGB0L7Qt9C00LDQvdCwJyk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBsYXllcjogUGxheWVyID0ge1xuICAgICAgICBpZDogaXNCb3QgPyBgYm90LSR7RGF0ZS5ub3coKX1gIDogY3VycmVudFBsYXllcklkLFxuICAgICAgICBuYW1lLFxuICAgICAgICBoYW5kOiBbXSxcbiAgICAgICAgaXNCb3RcbiAgICAgIH07XG4gICAgICBcbiAgICAgIGdhbWUuYWRkUGxheWVyKHBsYXllcik7XG4gICAgICB1cGRhdGVHYW1lU3RhdGUoKTtcbiAgICAgIFxuICAgICAgYWRkRXZlbnQoe1xuICAgICAgICB0eXBlOiAncGxheWVyX2pvaW5lZCcsXG4gICAgICAgIHBsYXllcklkOiBwbGF5ZXIuaWQsXG4gICAgICAgIG1lc3NhZ2U6IGAke25hbWV9ICR7aXNCb3QgPyAnKNCx0L7RgiknIDogJyd9INC/0YDQuNGB0L7QtdC00LjQvdC40LvRgdGPINC6INC40LPRgNC1YFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGFkZEV2ZW50KHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn0J7RiNC40LHQutCwINC00L7QsdCw0LLQu9C10L3QuNGPINC40LPRgNC+0LrQsCdcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2dhbWUsIGN1cnJlbnRQbGF5ZXJJZCwgdXBkYXRlR2FtZVN0YXRlLCBhZGRFdmVudF0pO1xuXG4gIC8vINCd0LDRh9Cw0LvQviDQuNCz0YDRi1xuICBjb25zdCBzdGFydEdhbWUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFnYW1lKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ9CY0LPRgNCwINC90LUg0YHQvtC30LTQsNC90LAnKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8g0JTQvtCx0LDQstC70Y/QtdC8INCx0L7RgtCwINC10YHQu9C4INC90LXQtNC+0YHRgtCw0YLQvtGH0L3QviDQuNCz0YDQvtC60L7QslxuICAgICAgY29uc3QgY3VycmVudFN0YXRlID0gZ2FtZS5nZXRTdGF0ZSgpO1xuICAgICAgaWYgKGN1cnJlbnRTdGF0ZS5wbGF5ZXJzLmxlbmd0aCA8IDIpIHtcbiAgICAgICAgYWRkUGxheWVyKCfQmNCYINCf0YDQvtGC0LjQstC90LjQuicsIHRydWUpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBnYW1lLnN0YXJ0KCk7XG4gICAgICB1cGRhdGVHYW1lU3RhdGUoKTtcbiAgICAgIFxuICAgICAgYWRkRXZlbnQoe1xuICAgICAgICB0eXBlOiAnY2FyZHNfZGVhbHQnLFxuICAgICAgICBtZXNzYWdlOiAn0JrQsNGA0YLRiyDRgNC+0LfQtNCw0L3Riywg0LjQs9GA0LAg0L3QsNGH0LDQu9Cw0YHRjCEnXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWRkRXZlbnQoe1xuICAgICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfQntGI0LjQsdC60LAg0L3QsNGH0LDQu9CwINC40LPRgNGLJ1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbZ2FtZSwgYWRkUGxheWVyLCB1cGRhdGVHYW1lU3RhdGUsIGFkZEV2ZW50XSk7XG5cbiAgLy8g0JLRi9C/0L7Qu9C90LXQvdC40LUg0YXQvtC00LBcbiAgY29uc3QgbWFrZU1vdmUgPSB1c2VDYWxsYmFjaygoYWN0aW9uOiBQbGF5ZXJBY3Rpb24pID0+IHtcbiAgICBpZiAoIWdhbWUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcign0JjQs9GA0LAg0L3QtSDRgdC+0LfQtNCw0L3QsCcpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBnYW1lLm1ha2VNb3ZlKGFjdGlvbik7XG4gICAgICB1cGRhdGVHYW1lU3RhdGUoKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIGFkZEV2ZW50KHtcbiAgICAgICAgICB0eXBlOiAnY2FyZF9wbGF5ZWQnLFxuICAgICAgICAgIHBsYXllcklkOiBhY3Rpb24ucGxheWVySWQsXG4gICAgICAgICAgbWVzc2FnZTogYNCY0LPRgNC+0Log0LLRi9C/0L7Qu9C90LjQuyDQtNC10LnRgdGC0LLQuNC1OiAke2FjdGlvbi5hY3Rpb259YCxcbiAgICAgICAgICBkYXRhOiBhY3Rpb25cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g0J/RgNC+0LLQtdGA0Y/QtdC8INC+0LrQvtC90YfQsNC90LjQtSDQuNCz0YDRi1xuICAgICAgICBjb25zdCBuZXdTdGF0ZSA9IGdhbWUuZ2V0U3RhdGUoKTtcbiAgICAgICAgaWYgKG5ld1N0YXRlLnBoYXNlID09PSAnZmluaXNoZWQnKSB7XG4gICAgICAgICAgYWRkRXZlbnQoe1xuICAgICAgICAgICAgdHlwZTogJ2dhbWVfZmluaXNoZWQnLFxuICAgICAgICAgICAgbWVzc2FnZTogbmV3U3RhdGUud2lubmVyIFxuICAgICAgICAgICAgICA/IGDQmNCz0YDQsCDQvtC60L7QvdGH0LXQvdCwISDQn9C+0LHQtdC00LjRgtC10LvRjDogJHtuZXdTdGF0ZS5wbGF5ZXJzLmZpbmQocCA9PiBwLmlkID09PSBuZXdTdGF0ZS53aW5uZXIpPy5uYW1lfWBcbiAgICAgICAgICAgICAgOiAn0JjQs9GA0LAg0L7QutC+0L3Rh9C10L3QsCdcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWRkRXZlbnQoe1xuICAgICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgICAgcGxheWVySWQ6IGFjdGlvbi5wbGF5ZXJJZCxcbiAgICAgICAgICBtZXNzYWdlOiByZXN1bHQuZXJyb3IgfHwgJ9Cd0LXQtNC+0L/Rg9GB0YLQuNC80YvQuSDRhdC+0LQnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBhZGRFdmVudCh7XG4gICAgICAgIHR5cGU6ICdlcnJvcicsXG4gICAgICAgIHBsYXllcklkOiBhY3Rpb24ucGxheWVySWQsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ9Ce0YjQuNCx0LrQsCDQstGL0L/QvtC70L3QtdC90LjRjyDRhdC+0LTQsCdcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2dhbWUsIHVwZGF0ZUdhbWVTdGF0ZSwgYWRkRXZlbnRdKTtcblxuICAvLyDQn9GA0L7QstC10YDQutCwLCDQvNC+0LbQvdC+INC70Lgg0YHRi9Cz0YDQsNGC0Ywg0LrQsNGA0YLRg1xuICBjb25zdCBjYW5QbGF5Q2FyZCA9IHVzZUNhbGxiYWNrKChjYXJkOiBDYXJkKTogYm9vbGVhbiA9PiB7XG4gICAgaWYgKCFnYW1lIHx8ICFnYW1lU3RhdGUpIHJldHVybiBmYWxzZTtcbiAgICBcbiAgICBjb25zdCBjdXJyZW50UGxheWVyID0gZ2FtZVN0YXRlLnBsYXllcnMuZmluZChwID0+IHAuaWQgPT09IGN1cnJlbnRQbGF5ZXJJZCk7XG4gICAgaWYgKCFjdXJyZW50UGxheWVyKSByZXR1cm4gZmFsc2U7XG4gICAgXG4gICAgLy8g0J/RgNC+0LLQtdGA0Y/QtdC8LCDQtdGB0YLRjCDQu9C4INC60LDRgNGC0LAg0YMg0LjQs9GA0L7QutCwXG4gICAgY29uc3QgaGFzQ2FyZCA9IGN1cnJlbnRQbGF5ZXIuaGFuZC5zb21lKGMgPT4gYy5zdWl0ID09PSBjYXJkLnN1aXQgJiYgYy5yYW5rID09PSBjYXJkLnJhbmspO1xuICAgIGlmICghaGFzQ2FyZCkgcmV0dXJuIGZhbHNlO1xuICAgIFxuICAgIC8vINCf0YDQvtCy0LXRgNGP0LXQvCwg0L3QsNGIINC70Lgg0YXQvtC0XG4gICAgcmV0dXJuIGdhbWVTdGF0ZS5jdXJyZW50UGxheWVySW5kZXggPT09IGdhbWVTdGF0ZS5wbGF5ZXJzLmZpbmRJbmRleChwID0+IHAuaWQgPT09IGN1cnJlbnRQbGF5ZXJJZCk7XG4gIH0sIFtnYW1lLCBnYW1lU3RhdGUsIGN1cnJlbnRQbGF5ZXJJZF0pO1xuXG4gIC8vINCf0L7Qu9GD0YfQtdC90LjQtSDQtNC+0YHRgtGD0L/QvdGL0YUg0YXQvtC00L7QslxuICBjb25zdCBnZXRWYWxpZE1vdmVzID0gdXNlQ2FsbGJhY2soKCk6IFBsYXllckFjdGlvbltdID0+IHtcbiAgICBpZiAoIWdhbWUgfHwgIWdhbWVTdGF0ZSkgcmV0dXJuIFtdO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICByZXR1cm4gZ2FtZS5nZXRWYWxpZE1vdmVzKGN1cnJlbnRQbGF5ZXJJZCk7XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9LCBbZ2FtZSwgZ2FtZVN0YXRlLCBjdXJyZW50UGxheWVySWRdKTtcblxuICAvLyDQodCx0YDQvtGBINC40LPRgNGLXG4gIGNvbnN0IHJlc2V0R2FtZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRHYW1lKG51bGwpO1xuICAgIHNldEdhbWVTdGF0ZShudWxsKTtcbiAgICBzZXRHYW1lRXZlbnRzKFtdKTtcbiAgfSwgW10pO1xuXG4gIC8vINCS0YvRh9C40YHQu9GP0LXQvNGL0LUg0LfQvdCw0YfQtdC90LjRj1xuICBjb25zdCBjdXJyZW50UGxheWVyID0gZ2FtZVN0YXRlPy5wbGF5ZXJzLmZpbmQocCA9PiBwLmlkID09PSBjdXJyZW50UGxheWVySWQpIHx8IG51bGw7XG4gIGNvbnN0IGlzTXlUdXJuID0gZ2FtZVN0YXRlID8gXG4gICAgZ2FtZVN0YXRlLnBsYXllcnNbZ2FtZVN0YXRlLmN1cnJlbnRQbGF5ZXJJbmRleF0/LmlkID09PSBjdXJyZW50UGxheWVySWQgOiBmYWxzZTtcblxuICAvLyDQkNCy0YLQvtC80LDRgtC40YfQtdGB0LrQuNC1INC00LXQudGB0YLQstC40Y8g0LHQvtGC0L7QslxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZ2FtZSB8fCAhZ2FtZVN0YXRlIHx8IGdhbWVTdGF0ZS5waGFzZSA9PT0gJ2ZpbmlzaGVkJykgcmV0dXJuO1xuXG4gICAgY29uc3QgY3VycmVudEdhbWVQbGF5ZXIgPSBnYW1lU3RhdGUucGxheWVyc1tnYW1lU3RhdGUuY3VycmVudFBsYXllckluZGV4XTtcbiAgICBpZiAoIWN1cnJlbnRHYW1lUGxheWVyPy5pc0JvdCkgcmV0dXJuO1xuXG4gICAgLy8g0J/RgNC+0YHRgtCw0Y8g0LvQvtCz0LjQutCwINCx0L7RgtCwIC0g0YHQu9GD0YfQsNC50L3QvtC1INC00LXQudGB0YLQstC40LUg0YfQtdGA0LXQtyDQvdC10LHQvtC70YzRiNGD0Y4g0LfQsNC00LXRgNC20LrRg1xuICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB2YWxpZE1vdmVzID0gZ2FtZS5nZXRWYWxpZE1vdmVzKGN1cnJlbnRHYW1lUGxheWVyLmlkKTtcbiAgICAgICAgaWYgKHZhbGlkTW92ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IHJhbmRvbU1vdmUgPSB2YWxpZE1vdmVzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHZhbGlkTW92ZXMubGVuZ3RoKV07XG4gICAgICAgICAgbWFrZU1vdmUocmFuZG9tTW92ZSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ9Ce0YjQuNCx0LrQsCDRhdC+0LTQsCDQsdC+0YLQsDonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfSwgMTAwMCArIE1hdGgucmFuZG9tKCkgKiAyMDAwKTsgLy8gMS0zINGB0LXQutGD0L3QtNGLINC30LDQtNC10YDQttC60LBcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbZ2FtZSwgZ2FtZVN0YXRlLCBtYWtlTW92ZV0pO1xuXG4gIHJldHVybiB7XG4gICAgZ2FtZSxcbiAgICBnYW1lU3RhdGUsXG4gICAgY3VycmVudFBsYXllcixcbiAgICBpc015VHVybixcbiAgICBnYW1lRXZlbnRzLFxuXG4gICAgY3JlYXRlTmV3R2FtZSxcbiAgICBhZGRQbGF5ZXIsXG4gICAgc3RhcnRHYW1lLFxuICAgIG1ha2VNb3ZlLFxuXG4gICAgY2FuUGxheUNhcmQsXG4gICAgZ2V0VmFsaWRNb3ZlcyxcbiAgICByZXNldEdhbWVcbiAgfTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsIkR1cmFrR2FtZSIsInVzZUR1cmFrR2FtZSIsInBsYXllcklkIiwiX3MiLCJnYW1lIiwic2V0R2FtZSIsImdhbWVTdGF0ZSIsInNldEdhbWVTdGF0ZSIsImdhbWVFdmVudHMiLCJzZXRHYW1lRXZlbnRzIiwiY3VycmVudFBsYXllcklkIiwiRGF0ZSIsIm5vdyIsInVwZGF0ZUdhbWVTdGF0ZSIsImdldFN0YXRlIiwiYWRkRXZlbnQiLCJldmVudCIsIm5ld0V2ZW50IiwidGltZXN0YW1wIiwicHJldiIsImNyZWF0ZU5ld0dhbWUiLCJydWxlcyIsInBsYXllcnMiLCJuZXdHYW1lIiwiYWRkRXZlbnRMaXN0ZW5lciIsInR5cGUiLCJtZXNzYWdlIiwiZGF0YSIsImVycm9yIiwiRXJyb3IiLCJhZGRQbGF5ZXIiLCJuYW1lIiwiaXNCb3QiLCJwbGF5ZXIiLCJpZCIsImhhbmQiLCJzdGFydEdhbWUiLCJjdXJyZW50U3RhdGUiLCJsZW5ndGgiLCJzdGFydCIsIm1ha2VNb3ZlIiwiYWN0aW9uIiwicmVzdWx0Iiwic3VjY2VzcyIsIm5ld1N0YXRlIiwicGhhc2UiLCJ3aW5uZXIiLCJmaW5kIiwicCIsImNhblBsYXlDYXJkIiwiY2FyZCIsImN1cnJlbnRQbGF5ZXIiLCJoYXNDYXJkIiwic29tZSIsImMiLCJzdWl0IiwicmFuayIsImN1cnJlbnRQbGF5ZXJJbmRleCIsImZpbmRJbmRleCIsImdldFZhbGlkTW92ZXMiLCJyZXNldEdhbWUiLCJpc015VHVybiIsImN1cnJlbnRHYW1lUGxheWVyIiwidGltZXIiLCJzZXRUaW1lb3V0IiwidmFsaWRNb3ZlcyIsInJhbmRvbU1vdmUiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJjb25zb2xlIiwiY2xlYXJUaW1lb3V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/hooks/useDurakGame.ts\n"));

/***/ })

});