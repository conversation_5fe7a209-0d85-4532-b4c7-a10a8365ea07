"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_AIShowcase_tsx"],{

/***/ "./src/components/AIShowcase.tsx":
/*!***************************************!*\
  !*** ./src/components/AIShowcase.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst emotionalMetrics = [{\n  name: 'Счастье',\n  value: 0.85,\n  color: '#4ade80',\n  icon: '😊',\n  description: 'Уровень позитивных эмоций игрока'\n}, {\n  name: 'Фокус',\n  value: 0.92,\n  color: '#3b82f6',\n  icon: '🎯',\n  description: 'Концентрация и внимание к игре'\n}, {\n  name: 'Уверенность',\n  value: 0.78,\n  color: '#8b5cf6',\n  icon: '💪',\n  description: 'Уверенность в принятии решений'\n}, {\n  name: 'Стресс',\n  value: 0.23,\n  color: '#ef4444',\n  icon: '😰',\n  description: 'Уровень стресса и напряжения'\n}, {\n  name: 'Мотивация',\n  value: 0.89,\n  color: '#f59e0b',\n  icon: '🔥',\n  description: 'Желание продолжать игру'\n}, {\n  name: 'Усталость',\n  value: 0.31,\n  color: '#6b7280',\n  icon: '😴',\n  description: 'Уровень усталости игрока'\n}];\nconst aiFeatures = [{\n  title: 'Анализ эмоций в реальном времени',\n  description: 'ИИ анализирует микровыражения, тон голоса и поведенческие паттерны',\n  icon: '🧠',\n  metrics: ['Точность: 95%', 'Задержка: <100мс', 'Источников данных: 8']\n}, {\n  title: 'Предиктивная аналитика',\n  description: 'Предсказание следующих ходов и вероятности победы',\n  icon: '🔮',\n  metrics: ['Точность предсказаний: 85%', 'Анализируемых факторов: 50+', 'Обновлений/сек: 1000+']\n}, {\n  title: 'Персонализированное обучение',\n  description: 'Адаптивные уроки и рекомендации на основе стиля игры',\n  icon: '📚',\n  metrics: ['Персональных путей: ∞', 'Адаптация: в реальном времени', 'Эффективность: +300%']\n}, {\n  title: 'Детекция тильта и выгорания',\n  description: 'Раннее обнаружение негативных состояний и превентивные меры',\n  icon: '🛡️',\n  metrics: ['Точность детекции: 99%', 'Время реакции: <1сек', 'Предотвращённых тильтов: 10K+']\n}];\nconst AIShowcase = ({\n  emotionalState\n}) => {\n  _s();\n  const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [currentMetrics, setCurrentMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(emotionalMetrics);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция обновления метрик в реальном времени\n    const interval = setInterval(() => {\n      setCurrentMetrics(prev => prev.map(metric => ({\n        ...metric,\n        value: Math.max(0, Math.min(1, metric.value + (Math.random() - 0.5) * 0.1))\n      })));\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Автоматическое переключение функций\n    const interval = setInterval(() => {\n      setActiveFeature(prev => (prev + 1) % aiFeatures.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AIContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n          children: \"\\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionSubtitle, {\n          children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u0430\\u044F \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430 \\u043F\\u043E\\u043D\\u0438\\u043C\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430 \\u0438\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MainContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricsPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PanelHeader, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PanelTitle, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AnalysisIndicator, {\n                active: isAnalyzing\n              }), \"\\u0410\\u043D\\u0430\\u043B\\u0438\\u0437 \\u0432 \\u0440\\u0435\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u0438\"]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PanelSubtitle, {\n              children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u0438\\u0440\\u0443\\u0435\\u0442 8 \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0441\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u0438\\u0439 \\u043E\\u0434\\u043D\\u043E\\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u043D\\u043E\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricsGrid, {\n            children: currentMetrics.map((metric, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricCard, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              whileHover: {\n                scale: 1.05\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricHeader, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricIcon, {\n                  children: metric.icon\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricName, {\n                  children: metric.name\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricValue, {\n                color: metric.color,\n                children: [(metric.value * 100).toFixed(0), \"%\"]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricBar, {\n                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricProgress, {\n                  color: metric.color,\n                  width: metric.value * 100,\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: `${metric.value * 100}%`\n                  },\n                  transition: {\n                    duration: 0.8,\n                    delay: index * 0.1\n                  }\n                })\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricDescription, {\n                children: metric.description\n              })]\n            }, metric.name))\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeaturesPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureSelector, {\n            children: aiFeatures.map((feature, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeatureTab, {\n              active: index === activeFeature,\n              onClick: () => setActiveFeature(index),\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureTabIcon, {\n                children: feature.icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureTabTitle, {\n                children: feature.title\n              })]\n            }, index))\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureContent, {\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n              mode: \"wait\",\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 50\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                exit: {\n                  opacity: 0,\n                  x: -50\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeatureHeader, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureIcon, {\n                    children: aiFeatures[activeFeature].icon\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureTitle, {\n                      children: aiFeatures[activeFeature].title\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureDescription, {\n                      children: aiFeatures[activeFeature].description\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureMetrics, {\n                  children: aiFeatures[activeFeature].metrics.map((metric, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeatureMetric, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: index * 0.1\n                    },\n                    children: [\"\\u2728 \", metric]\n                  }, index))\n                })]\n              }, activeFeature)\n            })\n          })]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DemoSection, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoTitle, {\n          children: \"\\u041F\\u043E\\u043F\\u0440\\u043E\\u0431\\u0443\\u0439\\u0442\\u0435 \\u0418\\u0418 \\u0432 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u0438\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DemoGrid, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoIcon, {\n              children: \"\\uD83C\\uDFAE\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardTitle, {\n              children: \"\\u0418\\u0433\\u0440\\u043E\\u0432\\u0430\\u044F \\u0441\\u0435\\u0441\\u0441\\u0438\\u044F\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardDescription, {\n              children: \"\\u041D\\u0430\\u0447\\u043D\\u0438\\u0442\\u0435 \\u0438\\u0433\\u0440\\u0443 \\u0438 \\u043D\\u0430\\u0431\\u043B\\u044E\\u0434\\u0430\\u0439\\u0442\\u0435 \\u0437\\u0430 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u043E\\u043C \\u0418\\u0418\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoIcon, {\n              children: \"\\uD83D\\uDCCA\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardTitle, {\n              children: \"\\u0410\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardDescription, {\n              children: \"\\u0418\\u0437\\u0443\\u0447\\u0438\\u0442\\u0435 \\u0434\\u0435\\u0442\\u0430\\u043B\\u044C\\u043D\\u0443\\u044E \\u0430\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0443 \\u0441\\u0432\\u043E\\u0435\\u0439 \\u0438\\u0433\\u0440\\u044B\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoIcon, {\n              children: \"\\uD83C\\uDFAF\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardTitle, {\n              children: \"\\u041F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DemoCardDescription, {\n              children: \"\\u041F\\u043E\\u043B\\u0443\\u0447\\u0438\\u0442\\u0435 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0440\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0430\\u0446\\u0438\\u0438\"\n            })]\n          })]\n        })]\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\n_s(AIShowcase, \"FjnxRK5S3iqx0fF16F3gEj++vRk=\");\n_c = AIShowcase;\nconst AIContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c2 = AIContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1400px;\n  width: 100%;\n`;\n_c3 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c5 = SectionSubtitle;\nconst MainContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 3rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n`;\n_c6 = MainContent;\nconst MetricsPanel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 2rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c7 = MetricsPanel;\nconst PanelHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  margin-bottom: 2rem;\n`;\n_c8 = PanelHeader;\nconst PanelTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c9 = PanelTitle;\nconst PanelSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\n_c10 = PanelSubtitle;\nconst AnalysisIndicator = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.active ? '#4ade80' : '#6b7280'};\n  animation: ${props => props.active ? 'pulse 2s infinite' : 'none'};\n`;\n_c11 = AnalysisIndicator;\nconst MetricsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c12 = MetricsGrid;\nconst MetricCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c13 = MetricCard;\nconst MetricHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n`;\n_c14 = MetricHeader;\nconst MetricIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-size: 1.5rem;\n`;\n_c15 = MetricIcon;\nconst MetricName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-weight: 600;\n  color: white;\n`;\n_c16 = MetricName;\nconst MetricValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 2rem;\n  font-weight: 700;\n  color: ${props => props.color};\n  margin-bottom: 0.5rem;\n`;\n_c17 = MetricValue;\nconst MetricBar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 100%;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 3px;\n  overflow: hidden;\n  margin-bottom: 0.5rem;\n`;\n_c18 = MetricBar;\nconst MetricProgress = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  height: 100%;\n  background: ${props => props.color};\n  border-radius: 3px;\n`;\n_c19 = MetricProgress;\nconst MetricDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n  line-height: 1.4;\n`;\n_c20 = MetricDescription;\nconst FeaturesPanel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 2rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c21 = FeaturesPanel;\nconst FeatureSelector = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c22 = FeatureSelector;\nconst FeatureTab = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.2)' : 'transparent'};\n  border: 1px solid ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.1)'};\n  border-radius: 10px;\n  padding: 1rem;\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n`;\n_c23 = FeatureTab;\nconst FeatureTabIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 1.5rem;\n  margin-bottom: 0.5rem;\n`;\n_c24 = FeatureTabIcon;\nconst FeatureTabTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 0.9rem;\n  font-weight: 600;\n`;\n_c25 = FeatureTabTitle;\nconst FeatureContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 200px;\n`;\n_c26 = FeatureContent;\nconst FeatureHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n_c27 = FeatureHeader;\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 3rem;\n  color: #4a90e2;\n`;\n_c28 = FeatureIcon;\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\n_c29 = FeatureTitle;\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.7);\n  line-height: 1.6;\n`;\n_c30 = FeatureDescription;\nconst FeatureMetrics = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\n_c31 = FeatureMetrics;\nconst FeatureMetric = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n_c32 = FeatureMetric;\nconst DemoSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  text-align: center;\n`;\n_c33 = DemoSection;\nconst DemoTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3`\n  font-size: 2rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 2rem;\n`;\n_c34 = DemoTitle;\nconst DemoGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2rem;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c35 = DemoGrid;\nconst DemoCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 2rem;\n  text-align: center;\n  cursor: pointer;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c36 = DemoCard;\nconst DemoIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 3rem;\n  margin-bottom: 1rem;\n`;\n_c37 = DemoIcon;\nconst DemoCardTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\n_c38 = DemoCardTitle;\nconst DemoCardDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\n_c39 = DemoCardDescription;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AIShowcase);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39;\n$RefreshReg$(_c, \"AIShowcase\");\n$RefreshReg$(_c2, \"AIContainer\");\n$RefreshReg$(_c3, \"ContentWrapper\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"SectionSubtitle\");\n$RefreshReg$(_c6, \"MainContent\");\n$RefreshReg$(_c7, \"MetricsPanel\");\n$RefreshReg$(_c8, \"PanelHeader\");\n$RefreshReg$(_c9, \"PanelTitle\");\n$RefreshReg$(_c10, \"PanelSubtitle\");\n$RefreshReg$(_c11, \"AnalysisIndicator\");\n$RefreshReg$(_c12, \"MetricsGrid\");\n$RefreshReg$(_c13, \"MetricCard\");\n$RefreshReg$(_c14, \"MetricHeader\");\n$RefreshReg$(_c15, \"MetricIcon\");\n$RefreshReg$(_c16, \"MetricName\");\n$RefreshReg$(_c17, \"MetricValue\");\n$RefreshReg$(_c18, \"MetricBar\");\n$RefreshReg$(_c19, \"MetricProgress\");\n$RefreshReg$(_c20, \"MetricDescription\");\n$RefreshReg$(_c21, \"FeaturesPanel\");\n$RefreshReg$(_c22, \"FeatureSelector\");\n$RefreshReg$(_c23, \"FeatureTab\");\n$RefreshReg$(_c24, \"FeatureTabIcon\");\n$RefreshReg$(_c25, \"FeatureTabTitle\");\n$RefreshReg$(_c26, \"FeatureContent\");\n$RefreshReg$(_c27, \"FeatureHeader\");\n$RefreshReg$(_c28, \"FeatureIcon\");\n$RefreshReg$(_c29, \"FeatureTitle\");\n$RefreshReg$(_c30, \"FeatureDescription\");\n$RefreshReg$(_c31, \"FeatureMetrics\");\n$RefreshReg$(_c32, \"FeatureMetric\");\n$RefreshReg$(_c33, \"DemoSection\");\n$RefreshReg$(_c34, \"DemoTitle\");\n$RefreshReg$(_c35, \"DemoGrid\");\n$RefreshReg$(_c36, \"DemoCard\");\n$RefreshReg$(_c37, \"DemoIcon\");\n$RefreshReg$(_c38, \"DemoCardTitle\");\n$RefreshReg$(_c39, \"DemoCardDescription\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AIShowcase.tsx\n"));

/***/ })

}]);