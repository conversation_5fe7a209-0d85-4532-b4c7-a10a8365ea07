"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_Web3Dashboard_tsx"],{

/***/ "./src/components/Web3Dashboard.tsx":
/*!******************************************!*\
  !*** ./src/components/Web3Dashboard.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst Web3Dashboard = ({\n  web3Status,\n  onConnectWallet\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('wallet');\n  const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [userAddress, setUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const [tokenBalances, setTokenBalances] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [nftCollection, setNftCollection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [defiPools, setDefiPools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [totalPortfolioValue, setTotalPortfolioValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция данных Web3\n    if (walletConnected) {\n      setUserAddress('******************************************');\n      setTokenBalances([{\n        symbol: 'KOZYR',\n        balance: 15420.5,\n        value: 7710.25,\n        change24h: 12.5\n      }, {\n        symbol: 'ETH',\n        balance: 2.45,\n        value: 4900,\n        change24h: -3.2\n      }, {\n        symbol: 'USDC',\n        balance: 1250,\n        value: 1250,\n        change24h: 0.1\n      }]);\n      setNftCollection([{\n        id: '1',\n        name: 'Legendary King',\n        image: '👑',\n        rarity: 'legendary',\n        power: 95,\n        price: 2.5,\n        isStaked: true\n      }, {\n        id: '2',\n        name: 'Epic Queen',\n        image: '👸',\n        rarity: 'epic',\n        power: 85,\n        price: 1.2,\n        isStaked: false\n      }, {\n        id: '3',\n        name: 'Rare Ace',\n        image: '🃏',\n        rarity: 'rare',\n        power: 75,\n        price: 0.8,\n        isStaked: true\n      }]);\n      setDefiPools([{\n        id: '1',\n        name: 'KOZYR/ETH',\n        apr: 145.2,\n        tvl: 2500000,\n        userStaked: 1500,\n        rewards: 25.4\n      }, {\n        id: '2',\n        name: 'KOZYR/USDC',\n        apr: 89.7,\n        tvl: 1800000,\n        userStaked: 800,\n        rewards: 12.1\n      }]);\n      setTotalPortfolioValue(13860.25);\n    }\n  }, [walletConnected]);\n  const connectWallet = async () => {\n    // Симуляция подключения кошелька\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    setWalletConnected(true);\n    onConnectWallet();\n  };\n  const getRarityColor = rarity => {\n    switch (rarity) {\n      case 'legendary':\n        return '#ffd700';\n      case 'epic':\n        return '#9370db';\n      case 'rare':\n        return '#4a90e2';\n      default:\n        return '#6b7280';\n    }\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Web3Container, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n          children: \"Web3 \\u042D\\u043A\\u043E\\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionSubtitle, {\n          children: \"\\u0414\\u0435\\u0446\\u0435\\u043D\\u0442\\u0440\\u0430\\u043B\\u0438\\u0437\\u043E\\u0432\\u0430\\u043D\\u043D\\u043E\\u0435 \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0435 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440\"\n        })]\n      }), !walletConnected ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletConnection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ConnectionCard, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ConnectionIcon, {\n            children: \"\\uD83D\\uDD17\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ConnectionTitle, {\n            children: \"\\u041F\\u043E\\u0434\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 \\u043A\\u043E\\u0448\\u0435\\u043B\\u0451\\u043A\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ConnectionDescription, {\n            children: \"\\u041F\\u043E\\u0434\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 Web3 \\u043A\\u043E\\u0448\\u0435\\u043B\\u0451\\u043A \\u0434\\u043B\\u044F \\u0434\\u043E\\u0441\\u0442\\u0443\\u043F\\u0430 \\u043A NFT, DeFi \\u0438 DAO \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u044F\\u043C\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(WalletOptions, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(WalletOption, {\n              onClick: connectWallet,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletIcon, {\n                children: \"\\uD83E\\uDD8A\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletName, {\n                children: \"MetaMask\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(WalletOption, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletIcon, {\n                children: \"\\uD83C\\uDF08\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletName, {\n                children: \"Rainbow\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(WalletOption, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletIcon, {\n                children: \"\\uD83D\\uDC99\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(WalletName, {\n                children: \"Coinbase\"\n              })]\n            })]\n          })]\n        })\n      }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DashboardContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DashboardHeader, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(UserInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(UserAvatar, {\n              children: \"\\uD83D\\uDC64\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(UserDetails, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(UserAddress, {\n                children: [userAddress.slice(0, 6), \"...\", userAddress.slice(-4)]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PortfolioValue, {\n                children: [\"$\", totalPortfolioValue.toLocaleString()]\n              })]\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NetworkInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NetworkIndicator, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NetworkName, {\n              children: \"Ethereum Mainnet\"\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabNavigation, {\n          children: [{\n            id: 'wallet',\n            label: 'Кошелёк',\n            icon: '💰'\n          }, {\n            id: 'nft',\n            label: 'NFT',\n            icon: '🎴'\n          }, {\n            id: 'defi',\n            label: 'DeFi',\n            icon: '🏦'\n          }, {\n            id: 'dao',\n            label: 'DAO',\n            icon: '🗳️'\n          }].map(tab => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TabButton, {\n            active: activeTab === tab.id,\n            onClick: () => setActiveTab(tab.id),\n            whileHover: {\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabIcon, {\n              children: tab.icon\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabLabel, {\n              children: tab.label\n            })]\n          }, tab.id))\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabContent, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n            mode: \"wait\",\n            children: [activeTab === 'wallet' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TokenGrid, {\n                children: tokenBalances.map(token => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TokenCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TokenHeader, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TokenSymbol, {\n                      children: token.symbol\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TokenChange, {\n                      positive: token.change24h > 0,\n                      children: [token.change24h > 0 ? '↗' : '↘', \" \", Math.abs(token.change24h), \"%\"]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TokenBalance, {\n                    children: token.balance.toLocaleString()\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TokenValue, {\n                    children: [\"$\", token.value.toLocaleString()]\n                  })]\n                }, token.symbol))\n              })\n            }, \"wallet\"), activeTab === 'nft' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NFTGrid, {\n                children: nftCollection.map(nft => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NFTCard, {\n                  rarity: nft.rarity,\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NFTImage, {\n                    children: nft.image\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NFTInfo, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NFTName, {\n                      children: nft.name\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NFTRarity, {\n                      rarity: nft.rarity,\n                      children: nft.rarity.toUpperCase()\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NFTPower, {\n                      children: [\"\\u26A1 \", nft.power]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NFTPrice, {\n                      children: [nft.price, \" ETH\"]\n                    })]\n                  }), nft.isStaked && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StakedBadge, {\n                    children: \"STAKED\"\n                  })]\n                }, nft.id))\n              })\n            }, \"nft\"), activeTab === 'defi' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DeFiGrid, {\n                children: defiPools.map(pool => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolHeader, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolName, {\n                      children: pool.name\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolAPR, {\n                      children: [pool.apr, \"% APR\"]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStats, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolStatLabel, {\n                        children: \"TVL\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStatValue, {\n                        children: [\"$\", (pool.tvl / 1000000).toFixed(1), \"M\"]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolStatLabel, {\n                        children: \"\\u0412\\u0430\\u0448 \\u0441\\u0442\\u0435\\u0439\\u043A\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStatValue, {\n                        children: [\"$\", pool.userStaked]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolStatLabel, {\n                        children: \"\\u041D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolStatValue, {\n                        children: [pool.rewards, \" KOZYR\"]\n                      })]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoolActions, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolButton, {\n                      whileHover: {\n                        scale: 1.02\n                      },\n                      whileTap: {\n                        scale: 0.98\n                      },\n                      children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoolButton, {\n                      secondary: true,\n                      whileHover: {\n                        scale: 1.02\n                      },\n                      whileTap: {\n                        scale: 0.98\n                      },\n                      children: \"\\u0417\\u0430\\u0431\\u0440\\u0430\\u0442\\u044C\"\n                    })]\n                  })]\n                }, pool.id))\n              })\n            }, \"defi\"), activeTab === 'dao' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DAOSection, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DAOStats, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatValue, {\n                      children: \"15,420\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatLabel, {\n                      children: \"\\u0412\\u0430\\u0448\\u0430 voting power\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatValue, {\n                      children: \"7\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatLabel, {\n                      children: \"\\u0410\\u043A\\u0442\\u0438\\u0432\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatValue, {\n                      children: \"89%\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DAOStatLabel, {\n                      children: \"\\u0423\\u0447\\u0430\\u0441\\u0442\\u0438\\u0435 \\u0432 \\u0433\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u043D\\u0438\\u0438\"\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProposalsList, {\n                  children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ProposalCard, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProposalTitle, {\n                      children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u043D\\u043E\\u0432\\u0443\\u044E \\u0438\\u0433\\u0440\\u0443: \\u0411\\u043B\\u044D\\u043A\\u0434\\u0436\\u0435\\u043A\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProposalDescription, {\n                      children: \"\\u041F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u043E \\u0434\\u043E\\u0431\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0438 \\u0431\\u043B\\u044D\\u043A\\u0434\\u0436\\u0435\\u043A\\u0430 \\u0432 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0443\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ProposalVotes, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(VoteOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteLabel, {\n                          children: \"\\u0417\\u0430: 85%\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteBar, {\n                          width: 85\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(VoteOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteLabel, {\n                          children: \"\\u041F\\u0440\\u043E\\u0442\\u0438\\u0432: 15%\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteBar, {\n                          width: 15\n                        })]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ProposalActions, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteButton, {\n                        whileHover: {\n                          scale: 1.02\n                        },\n                        whileTap: {\n                          scale: 0.98\n                        },\n                        children: \"\\u0413\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u0442\\u044C \\u0417\\u0410\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VoteButton, {\n                        secondary: true,\n                        whileHover: {\n                          scale: 1.02\n                        },\n                        whileTap: {\n                          scale: 0.98\n                        },\n                        children: \"\\u0413\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u0442\\u044C \\u041F\\u0420\\u041E\\u0422\\u0418\\u0412\"\n                      })]\n                    })]\n                  })\n                })]\n              })\n            }, \"dao\")]\n          })\n        })]\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты (первая часть)\n_s(Web3Dashboard, \"d3HFTTZRphU7pr3dylTnrNDO5lw=\");\n_c = Web3Dashboard;\nconst Web3Container = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #16213e 0%, #0f0f23 50%, #1a1a2e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c2 = Web3Container;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1400px;\n  width: 100%;\n`;\n_c3 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c5 = SectionSubtitle;\nconst WalletConnection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n`;\n_c6 = WalletConnection;\nconst ConnectionCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 3rem;\n  text-align: center;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;\n_c7 = ConnectionCard;\nconst ConnectionIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n`;\n_c8 = ConnectionIcon;\nconst ConnectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3`\n  font-size: 2rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 1rem;\n`;\n_c9 = ConnectionTitle;\nconst ConnectionDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\n_c10 = ConnectionDescription;\nconst WalletOptions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c11 = WalletOptions;\nconst WalletOption = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 15px;\n  padding: 1.5rem 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-color: #4a90e2;\n  }\n`;\n_c12 = WalletOption;\nconst WalletIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\n_c13 = WalletIcon;\nconst WalletName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\n\n// Стилизованные компоненты (продолжение)\n_c14 = WalletName;\nconst DashboardContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c15 = DashboardContent;\nconst DashboardHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c16 = DashboardHeader;\nconst UserInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n_c17 = UserInfo;\nconst UserAvatar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 50px;\n  height: 50px;\n  background: linear-gradient(135deg, #4a90e2, #7b68ee);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n`;\n_c18 = UserAvatar;\nconst UserDetails = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c19 = UserDetails;\nconst UserAddress = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n_c20 = UserAddress;\nconst PortfolioValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: #4ade80;\n  font-weight: 700;\n  font-size: 1.3rem;\n`;\n_c21 = PortfolioValue;\nconst NetworkInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c22 = NetworkInfo;\nconst NetworkIndicator = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 12px;\n  height: 12px;\n  background: #4ade80;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\n_c23 = NetworkIndicator;\nconst NetworkName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n_c24 = NetworkName;\nconst TabNavigation = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 0.5rem;\n`;\n_c25 = TabNavigation;\nconst TabButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  border: none;\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 600;\n\n  &:hover {\n    background: rgba(74, 144, 226, 0.1);\n    color: #4a90e2;\n  }\n`;\n_c26 = TabButton;\nconst TabIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-size: 1.2rem;\n`;\n_c27 = TabIcon;\nconst TabLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-size: 0.9rem;\n`;\n_c28 = TabLabel;\nconst TabContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 400px;\n`;\n_c29 = TabContent;\nconst TokenGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n`;\n_c30 = TokenGrid;\nconst TokenCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c31 = TokenCard;\nconst TokenHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;\n_c32 = TokenHeader;\nconst TokenSymbol = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 700;\n  font-size: 1.2rem;\n`;\n_c33 = TokenSymbol;\nconst TokenChange = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: ${props => props.positive ? '#4ade80' : '#ef4444'};\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\n_c34 = TokenChange;\nconst TokenBalance = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\n_c35 = TokenBalance;\nconst TokenValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 1rem;\n`;\n_c36 = TokenValue;\nconst NFTGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n`;\n_c37 = NFTGrid;\nconst NFTCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 2px solid ${props => getRarityColor(props.rarity)};\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, ${props => getRarityColor(props.rarity)}20, transparent);\n    pointer-events: none;\n  }\n`;\n_c38 = NFTCard;\nconst NFTImage = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 4rem;\n  text-align: center;\n  margin-bottom: 1rem;\n  filter: drop-shadow(0 0 20px currentColor);\n`;\n_c39 = NFTImage;\nconst NFTInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: relative;\n  z-index: 1;\n`;\n_c40 = NFTInfo;\nconst NFTName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 700;\n  font-size: 1.1rem;\n  margin-bottom: 0.5rem;\n`;\n_c41 = NFTName;\nconst NFTRarity = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: ${props => getRarityColor(props.rarity)};\n  font-weight: 600;\n  font-size: 0.8rem;\n  text-transform: uppercase;\n  margin-bottom: 0.5rem;\n`;\n_c42 = NFTRarity;\nconst NFTPower = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: #fbbf24;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n`;\n_c43 = NFTPower;\nconst NFTPrice = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n_c44 = NFTPrice;\nconst StakedBadge = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background: #4ade80;\n  color: black;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 700;\n`;\n_c45 = StakedBadge;\nconst DeFiGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n_c46 = DeFiGrid;\nconst PoolCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c47 = PoolCard;\nconst PoolHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n_c48 = PoolHeader;\nconst PoolName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 700;\n  font-size: 1.2rem;\n`;\n_c49 = PoolName;\nconst PoolAPR = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: #4ade80;\n  font-weight: 700;\n  font-size: 1.1rem;\n`;\n_c50 = PoolAPR;\nconst PoolStats = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n_c51 = PoolStats;\nconst PoolStat = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  text-align: center;\n`;\n_c52 = PoolStat;\nconst PoolStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 0.8rem;\n  margin-bottom: 0.25rem;\n`;\n_c53 = PoolStatLabel;\nconst PoolStatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-weight: 600;\n`;\n_c54 = PoolStatValue;\nconst PoolActions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 1rem;\n`;\n_c55 = PoolActions;\nconst PoolButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  background: ${props => props.secondary ? 'transparent' : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};\n  color: white;\n  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};\n  border-radius: 8px;\n  padding: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\n_c56 = PoolButton;\nconst DAOSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c57 = DAOSection;\nconst DAOStats = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n`;\n_c58 = DAOStats;\nconst DAOStat = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c59 = DAOStat;\nconst DAOStatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: #4a90e2;\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\n_c60 = DAOStatValue;\nconst DAOStatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\n_c61 = DAOStatLabel;\nconst ProposalsList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c62 = ProposalsList;\nconst ProposalCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c63 = ProposalCard;\nconst ProposalTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  color: white;\n  font-size: 1.2rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\n_c64 = ProposalTitle;\nconst ProposalDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 1.5rem;\n  line-height: 1.5;\n`;\n_c65 = ProposalDescription;\nconst ProposalVotes = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  margin-bottom: 1.5rem;\n`;\n_c66 = ProposalVotes;\nconst VoteOption = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  margin-bottom: 1rem;\n`;\n_c67 = VoteOption;\nconst VoteLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n`;\n_c68 = VoteLabel;\nconst VoteBar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 100%;\n  height: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  overflow: hidden;\n\n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.width}%;\n    height: 100%;\n    background: linear-gradient(90deg, #4a90e2, #7b68ee);\n    transition: width 0.8s ease;\n  }\n`;\n_c69 = VoteBar;\nconst ProposalActions = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 1rem;\n`;\n_c70 = ProposalActions;\nconst VoteButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  background: ${props => props.secondary ? 'transparent' : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};\n  color: white;\n  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};\n  border-radius: 8px;\n  padding: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\n\n// Вспомогательная функция\n_c71 = VoteButton;\nconst getRarityColor = rarity => {\n  switch (rarity) {\n    case 'legendary':\n      return '#ffd700';\n    case 'epic':\n      return '#9370db';\n    case 'rare':\n      return '#4a90e2';\n    default:\n      return '#6b7280';\n  }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (Web3Dashboard);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69, _c70, _c71;\n$RefreshReg$(_c, \"Web3Dashboard\");\n$RefreshReg$(_c2, \"Web3Container\");\n$RefreshReg$(_c3, \"ContentWrapper\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"SectionSubtitle\");\n$RefreshReg$(_c6, \"WalletConnection\");\n$RefreshReg$(_c7, \"ConnectionCard\");\n$RefreshReg$(_c8, \"ConnectionIcon\");\n$RefreshReg$(_c9, \"ConnectionTitle\");\n$RefreshReg$(_c10, \"ConnectionDescription\");\n$RefreshReg$(_c11, \"WalletOptions\");\n$RefreshReg$(_c12, \"WalletOption\");\n$RefreshReg$(_c13, \"WalletIcon\");\n$RefreshReg$(_c14, \"WalletName\");\n$RefreshReg$(_c15, \"DashboardContent\");\n$RefreshReg$(_c16, \"DashboardHeader\");\n$RefreshReg$(_c17, \"UserInfo\");\n$RefreshReg$(_c18, \"UserAvatar\");\n$RefreshReg$(_c19, \"UserDetails\");\n$RefreshReg$(_c20, \"UserAddress\");\n$RefreshReg$(_c21, \"PortfolioValue\");\n$RefreshReg$(_c22, \"NetworkInfo\");\n$RefreshReg$(_c23, \"NetworkIndicator\");\n$RefreshReg$(_c24, \"NetworkName\");\n$RefreshReg$(_c25, \"TabNavigation\");\n$RefreshReg$(_c26, \"TabButton\");\n$RefreshReg$(_c27, \"TabIcon\");\n$RefreshReg$(_c28, \"TabLabel\");\n$RefreshReg$(_c29, \"TabContent\");\n$RefreshReg$(_c30, \"TokenGrid\");\n$RefreshReg$(_c31, \"TokenCard\");\n$RefreshReg$(_c32, \"TokenHeader\");\n$RefreshReg$(_c33, \"TokenSymbol\");\n$RefreshReg$(_c34, \"TokenChange\");\n$RefreshReg$(_c35, \"TokenBalance\");\n$RefreshReg$(_c36, \"TokenValue\");\n$RefreshReg$(_c37, \"NFTGrid\");\n$RefreshReg$(_c38, \"NFTCard\");\n$RefreshReg$(_c39, \"NFTImage\");\n$RefreshReg$(_c40, \"NFTInfo\");\n$RefreshReg$(_c41, \"NFTName\");\n$RefreshReg$(_c42, \"NFTRarity\");\n$RefreshReg$(_c43, \"NFTPower\");\n$RefreshReg$(_c44, \"NFTPrice\");\n$RefreshReg$(_c45, \"StakedBadge\");\n$RefreshReg$(_c46, \"DeFiGrid\");\n$RefreshReg$(_c47, \"PoolCard\");\n$RefreshReg$(_c48, \"PoolHeader\");\n$RefreshReg$(_c49, \"PoolName\");\n$RefreshReg$(_c50, \"PoolAPR\");\n$RefreshReg$(_c51, \"PoolStats\");\n$RefreshReg$(_c52, \"PoolStat\");\n$RefreshReg$(_c53, \"PoolStatLabel\");\n$RefreshReg$(_c54, \"PoolStatValue\");\n$RefreshReg$(_c55, \"PoolActions\");\n$RefreshReg$(_c56, \"PoolButton\");\n$RefreshReg$(_c57, \"DAOSection\");\n$RefreshReg$(_c58, \"DAOStats\");\n$RefreshReg$(_c59, \"DAOStat\");\n$RefreshReg$(_c60, \"DAOStatValue\");\n$RefreshReg$(_c61, \"DAOStatLabel\");\n$RefreshReg$(_c62, \"ProposalsList\");\n$RefreshReg$(_c63, \"ProposalCard\");\n$RefreshReg$(_c64, \"ProposalTitle\");\n$RefreshReg$(_c65, \"ProposalDescription\");\n$RefreshReg$(_c66, \"ProposalVotes\");\n$RefreshReg$(_c67, \"VoteOption\");\n$RefreshReg$(_c68, \"VoteLabel\");\n$RefreshReg$(_c69, \"VoteBar\");\n$RefreshReg$(_c70, \"ProposalActions\");\n$RefreshReg$(_c71, \"VoteButton\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9XZWIzRGFzaGJvYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUNLO0FBQ2pCO0FBQUE7QUFpQ3ZDLE1BQU1VLGFBQTJDLEdBQUdBLENBQUM7RUFBRUMsVUFBVTtFQUFFQztBQUFnQixDQUFDLEtBQUs7RUFBQUMsRUFBQTtFQUN2RixNQUFNLENBQUNDLFNBQVMsRUFBRUMsWUFBWSxDQUFDLEdBQUdkLCtDQUFRLENBQW9DLFFBQVEsQ0FBQztFQUN2RixNQUFNLENBQUNlLGVBQWUsRUFBRUMsa0JBQWtCLENBQUMsR0FBR2hCLCtDQUFRLENBQUMsS0FBSyxDQUFDO0VBQzdELE1BQU0sQ0FBQ2lCLFdBQVcsRUFBRUMsY0FBYyxDQUFDLEdBQUdsQiwrQ0FBUSxDQUFDLEVBQUUsQ0FBQztFQUNsRCxNQUFNLENBQUNtQixhQUFhLEVBQUVDLGdCQUFnQixDQUFDLEdBQUdwQiwrQ0FBUSxDQUFpQixFQUFFLENBQUM7RUFDdEUsTUFBTSxDQUFDcUIsYUFBYSxFQUFFQyxnQkFBZ0IsQ0FBQyxHQUFHdEIsK0NBQVEsQ0FBWSxFQUFFLENBQUM7RUFDakUsTUFBTSxDQUFDdUIsU0FBUyxFQUFFQyxZQUFZLENBQUMsR0FBR3hCLCtDQUFRLENBQWEsRUFBRSxDQUFDO0VBQzFELE1BQU0sQ0FBQ3lCLG1CQUFtQixFQUFFQyxzQkFBc0IsQ0FBQyxHQUFHMUIsK0NBQVEsQ0FBQyxDQUFDLENBQUM7RUFFakVDLGdEQUFTLENBQUMsTUFBTTtJQUNkO0lBQ0EsSUFBSWMsZUFBZSxFQUFFO01BQ25CRyxjQUFjLENBQUMsNENBQTRDLENBQUM7TUFDNURFLGdCQUFnQixDQUFDLENBQ2Y7UUFBRU8sTUFBTSxFQUFFLE9BQU87UUFBRUMsT0FBTyxFQUFFLE9BQU87UUFBRUMsS0FBSyxFQUFFLE9BQU87UUFBRUMsU0FBUyxFQUFFO01BQUssQ0FBQyxFQUN0RTtRQUFFSCxNQUFNLEVBQUUsS0FBSztRQUFFQyxPQUFPLEVBQUUsSUFBSTtRQUFFQyxLQUFLLEVBQUUsSUFBSTtRQUFFQyxTQUFTLEVBQUUsQ0FBQztNQUFJLENBQUMsRUFDOUQ7UUFBRUgsTUFBTSxFQUFFLE1BQU07UUFBRUMsT0FBTyxFQUFFLElBQUk7UUFBRUMsS0FBSyxFQUFFLElBQUk7UUFBRUMsU0FBUyxFQUFFO01BQUksQ0FBQyxDQUMvRCxDQUFDO01BRUZSLGdCQUFnQixDQUFDLENBQ2Y7UUFDRVMsRUFBRSxFQUFFLEdBQUc7UUFDUEMsSUFBSSxFQUFFLGdCQUFnQjtRQUN0QkMsS0FBSyxFQUFFLElBQUk7UUFDWEMsTUFBTSxFQUFFLFdBQVc7UUFDbkJDLEtBQUssRUFBRSxFQUFFO1FBQ1RDLEtBQUssRUFBRSxHQUFHO1FBQ1ZDLFFBQVEsRUFBRTtNQUNaLENBQUMsRUFDRDtRQUNFTixFQUFFLEVBQUUsR0FBRztRQUNQQyxJQUFJLEVBQUUsWUFBWTtRQUNsQkMsS0FBSyxFQUFFLElBQUk7UUFDWEMsTUFBTSxFQUFFLE1BQU07UUFDZEMsS0FBSyxFQUFFLEVBQUU7UUFDVEMsS0FBSyxFQUFFLEdBQUc7UUFDVkMsUUFBUSxFQUFFO01BQ1osQ0FBQyxFQUNEO1FBQ0VOLEVBQUUsRUFBRSxHQUFHO1FBQ1BDLElBQUksRUFBRSxVQUFVO1FBQ2hCQyxLQUFLLEVBQUUsSUFBSTtRQUNYQyxNQUFNLEVBQUUsTUFBTTtRQUNkQyxLQUFLLEVBQUUsRUFBRTtRQUNUQyxLQUFLLEVBQUUsR0FBRztRQUNWQyxRQUFRLEVBQUU7TUFDWixDQUFDLENBQ0YsQ0FBQztNQUVGYixZQUFZLENBQUMsQ0FDWDtRQUNFTyxFQUFFLEVBQUUsR0FBRztRQUNQQyxJQUFJLEVBQUUsV0FBVztRQUNqQk0sR0FBRyxFQUFFLEtBQUs7UUFDVkMsR0FBRyxFQUFFLE9BQU87UUFDWkMsVUFBVSxFQUFFLElBQUk7UUFDaEJDLE9BQU8sRUFBRTtNQUNYLENBQUMsRUFDRDtRQUNFVixFQUFFLEVBQUUsR0FBRztRQUNQQyxJQUFJLEVBQUUsWUFBWTtRQUNsQk0sR0FBRyxFQUFFLElBQUk7UUFDVEMsR0FBRyxFQUFFLE9BQU87UUFDWkMsVUFBVSxFQUFFLEdBQUc7UUFDZkMsT0FBTyxFQUFFO01BQ1gsQ0FBQyxDQUNGLENBQUM7TUFFRmYsc0JBQXNCLENBQUMsUUFBUSxDQUFDO0lBQ2xDO0VBQ0YsQ0FBQyxFQUFFLENBQUNYLGVBQWUsQ0FBQyxDQUFDO0VBRXJCLE1BQU0yQixhQUFhLEdBQUcsTUFBQUEsQ0FBQSxLQUFZO0lBQ2hDO0lBQ0EsTUFBTSxJQUFJQyxPQUFPLENBQUNDLE9BQU8sSUFBSUMsVUFBVSxDQUFDRCxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUM7SUFDdkQ1QixrQkFBa0IsQ0FBQyxJQUFJLENBQUM7SUFDeEJMLGVBQWUsQ0FBQyxDQUFDO0VBQ25CLENBQUM7RUFFRCxNQUFNbUMsY0FBYyxHQUFJWixNQUFjLElBQUs7SUFDekMsUUFBUUEsTUFBTTtNQUNaLEtBQUssV0FBVztRQUFFLE9BQU8sU0FBUztNQUNsQyxLQUFLLE1BQU07UUFBRSxPQUFPLFNBQVM7TUFDN0IsS0FBSyxNQUFNO1FBQUUsT0FBTyxTQUFTO01BQzdCO1FBQVMsT0FBTyxTQUFTO0lBQzNCO0VBQ0YsQ0FBQztFQUVELG9CQUNFNUIsc0RBQUEsQ0FBQ3lDLGFBQWE7SUFBQUMsUUFBQSxlQUNaeEMsdURBQUEsQ0FBQ3lDLGNBQWM7TUFBQUQsUUFBQSxnQkFDYnhDLHVEQUFBLENBQUNOLGlEQUFNLENBQUNnRCxHQUFHO1FBQ1RDLE9BQU8sRUFBRTtVQUFFQyxPQUFPLEVBQUUsQ0FBQztVQUFFQyxDQUFDLEVBQUU7UUFBRyxDQUFFO1FBQy9CQyxPQUFPLEVBQUU7VUFBRUYsT0FBTyxFQUFFLENBQUM7VUFBRUMsQ0FBQyxFQUFFO1FBQUUsQ0FBRTtRQUM5QkUsVUFBVSxFQUFFO1VBQUVDLFFBQVEsRUFBRTtRQUFJLENBQUU7UUFBQVIsUUFBQSxnQkFFOUIxQyxzREFBQSxDQUFDbUQsWUFBWTtVQUFBVCxRQUFBLEVBQUM7UUFBZSxDQUFjLENBQUMsZUFDNUMxQyxzREFBQSxDQUFDb0QsZUFBZTtVQUFBVixRQUFBLEVBQUM7UUFFakIsQ0FBaUIsQ0FBQztNQUFBLENBQ1IsQ0FBQyxFQUVaLENBQUNqQyxlQUFlLGdCQUNmVCxzREFBQSxDQUFDcUQsZ0JBQWdCO1FBQUFYLFFBQUEsZUFDZnhDLHVEQUFBLENBQUNvRCxjQUFjO1VBQ2JULE9BQU8sRUFBRTtZQUFFQyxPQUFPLEVBQUUsQ0FBQztZQUFFUyxLQUFLLEVBQUU7VUFBSSxDQUFFO1VBQ3BDUCxPQUFPLEVBQUU7WUFBRUYsT0FBTyxFQUFFLENBQUM7WUFBRVMsS0FBSyxFQUFFO1VBQUUsQ0FBRTtVQUNsQ04sVUFBVSxFQUFFO1lBQUVDLFFBQVEsRUFBRTtVQUFJLENBQUU7VUFBQVIsUUFBQSxnQkFFOUIxQyxzREFBQSxDQUFDd0QsY0FBYztZQUFBZCxRQUFBLEVBQUM7VUFBRSxDQUFnQixDQUFDLGVBQ25DMUMsc0RBQUEsQ0FBQ3lELGVBQWU7WUFBQWYsUUFBQSxFQUFDO1VBQWtCLENBQWlCLENBQUMsZUFDckQxQyxzREFBQSxDQUFDMEQscUJBQXFCO1lBQUFoQixRQUFBLEVBQUM7VUFFdkIsQ0FBdUIsQ0FBQyxlQUV4QnhDLHVEQUFBLENBQUN5RCxhQUFhO1lBQUFqQixRQUFBLGdCQUNaeEMsdURBQUEsQ0FBQzBELFlBQVk7Y0FDWEMsT0FBTyxFQUFFekIsYUFBYztjQUN2QjBCLFVBQVUsRUFBRTtnQkFBRVAsS0FBSyxFQUFFO2NBQUssQ0FBRTtjQUM1QlEsUUFBUSxFQUFFO2dCQUFFUixLQUFLLEVBQUU7Y0FBSyxDQUFFO2NBQUFiLFFBQUEsZ0JBRTFCMUMsc0RBQUEsQ0FBQ2dFLFVBQVU7Z0JBQUF0QixRQUFBLEVBQUM7Y0FBRSxDQUFZLENBQUMsZUFDM0IxQyxzREFBQSxDQUFDaUUsVUFBVTtnQkFBQXZCLFFBQUEsRUFBQztjQUFRLENBQVksQ0FBQztZQUFBLENBQ3JCLENBQUMsZUFFZnhDLHVEQUFBLENBQUMwRCxZQUFZO2NBQ1hFLFVBQVUsRUFBRTtnQkFBRVAsS0FBSyxFQUFFO2NBQUssQ0FBRTtjQUM1QlEsUUFBUSxFQUFFO2dCQUFFUixLQUFLLEVBQUU7Y0FBSyxDQUFFO2NBQUFiLFFBQUEsZ0JBRTFCMUMsc0RBQUEsQ0FBQ2dFLFVBQVU7Z0JBQUF0QixRQUFBLEVBQUM7Y0FBRSxDQUFZLENBQUMsZUFDM0IxQyxzREFBQSxDQUFDaUUsVUFBVTtnQkFBQXZCLFFBQUEsRUFBQztjQUFPLENBQVksQ0FBQztZQUFBLENBQ3BCLENBQUMsZUFFZnhDLHVEQUFBLENBQUMwRCxZQUFZO2NBQ1hFLFVBQVUsRUFBRTtnQkFBRVAsS0FBSyxFQUFFO2NBQUssQ0FBRTtjQUM1QlEsUUFBUSxFQUFFO2dCQUFFUixLQUFLLEVBQUU7Y0FBSyxDQUFFO2NBQUFiLFFBQUEsZ0JBRTFCMUMsc0RBQUEsQ0FBQ2dFLFVBQVU7Z0JBQUF0QixRQUFBLEVBQUM7Y0FBRSxDQUFZLENBQUMsZUFDM0IxQyxzREFBQSxDQUFDaUUsVUFBVTtnQkFBQXZCLFFBQUEsRUFBQztjQUFRLENBQVksQ0FBQztZQUFBLENBQ3JCLENBQUM7VUFBQSxDQUNGLENBQUM7UUFBQSxDQUNGO01BQUMsQ0FDRCxDQUFDLGdCQUVuQnhDLHVEQUFBLENBQUNnRSxnQkFBZ0I7UUFBQXhCLFFBQUEsZ0JBRWZ4Qyx1REFBQSxDQUFDaUUsZUFBZTtVQUFBekIsUUFBQSxnQkFDZHhDLHVEQUFBLENBQUNrRSxRQUFRO1lBQUExQixRQUFBLGdCQUNQMUMsc0RBQUEsQ0FBQ3FFLFVBQVU7Y0FBQTNCLFFBQUEsRUFBQztZQUFFLENBQVksQ0FBQyxlQUMzQnhDLHVEQUFBLENBQUNvRSxXQUFXO2NBQUE1QixRQUFBLGdCQUNWeEMsdURBQUEsQ0FBQ3FFLFdBQVc7Z0JBQUE3QixRQUFBLEdBQUUvQixXQUFXLENBQUM2RCxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFDLEtBQUcsRUFBQzdELFdBQVcsQ0FBQzZELEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztjQUFBLENBQWMsQ0FBQyxlQUM5RXRFLHVEQUFBLENBQUN1RSxjQUFjO2dCQUFBL0IsUUFBQSxHQUFDLEdBQUMsRUFBQ3ZCLG1CQUFtQixDQUFDdUQsY0FBYyxDQUFDLENBQUM7Y0FBQSxDQUFpQixDQUFDO1lBQUEsQ0FDN0QsQ0FBQztVQUFBLENBQ04sQ0FBQyxlQUVYeEUsdURBQUEsQ0FBQ3lFLFdBQVc7WUFBQWpDLFFBQUEsZ0JBQ1YxQyxzREFBQSxDQUFDNEUsZ0JBQWdCLElBQUUsQ0FBQyxlQUNwQjVFLHNEQUFBLENBQUM2RSxXQUFXO2NBQUFuQyxRQUFBLEVBQUM7WUFBZ0IsQ0FBYSxDQUFDO1VBQUEsQ0FDaEMsQ0FBQztRQUFBLENBQ0MsQ0FBQyxlQUdsQjFDLHNEQUFBLENBQUM4RSxhQUFhO1VBQUFwQyxRQUFBLEVBQ1gsQ0FDQztZQUFFakIsRUFBRSxFQUFFLFFBQVE7WUFBRXNELEtBQUssRUFBRSxTQUFTO1lBQUVDLElBQUksRUFBRTtVQUFLLENBQUMsRUFDOUM7WUFBRXZELEVBQUUsRUFBRSxLQUFLO1lBQUVzRCxLQUFLLEVBQUUsS0FBSztZQUFFQyxJQUFJLEVBQUU7VUFBSyxDQUFDLEVBQ3ZDO1lBQUV2RCxFQUFFLEVBQUUsTUFBTTtZQUFFc0QsS0FBSyxFQUFFLE1BQU07WUFBRUMsSUFBSSxFQUFFO1VBQUssQ0FBQyxFQUN6QztZQUFFdkQsRUFBRSxFQUFFLEtBQUs7WUFBRXNELEtBQUssRUFBRSxLQUFLO1lBQUVDLElBQUksRUFBRTtVQUFNLENBQUMsQ0FDekMsQ0FBQ0MsR0FBRyxDQUFDQyxHQUFHLGlCQUNQaEYsdURBQUEsQ0FBQ2lGLFNBQVM7WUFFUkMsTUFBTSxFQUFFN0UsU0FBUyxLQUFLMkUsR0FBRyxDQUFDekQsRUFBRztZQUM3Qm9DLE9BQU8sRUFBRUEsQ0FBQSxLQUFNckQsWUFBWSxDQUFDMEUsR0FBRyxDQUFDekQsRUFBUyxDQUFFO1lBQzNDcUMsVUFBVSxFQUFFO2NBQUVQLEtBQUssRUFBRTtZQUFLLENBQUU7WUFDNUJRLFFBQVEsRUFBRTtjQUFFUixLQUFLLEVBQUU7WUFBSyxDQUFFO1lBQUFiLFFBQUEsZ0JBRTFCMUMsc0RBQUEsQ0FBQ3FGLE9BQU87Y0FBQTNDLFFBQUEsRUFBRXdDLEdBQUcsQ0FBQ0Y7WUFBSSxDQUFVLENBQUMsZUFDN0JoRixzREFBQSxDQUFDc0YsUUFBUTtjQUFBNUMsUUFBQSxFQUFFd0MsR0FBRyxDQUFDSDtZQUFLLENBQVcsQ0FBQztVQUFBLEdBUDNCRyxHQUFHLENBQUN6RCxFQVFBLENBQ1o7UUFBQyxDQUNXLENBQUMsZUFHaEJ6QixzREFBQSxDQUFDdUYsVUFBVTtVQUFBN0MsUUFBQSxlQUNUeEMsdURBQUEsQ0FBQ0wsMERBQWU7WUFBQzJGLElBQUksRUFBQyxNQUFNO1lBQUE5QyxRQUFBLEdBQ3pCbkMsU0FBUyxLQUFLLFFBQVEsaUJBQ3JCUCxzREFBQSxDQUFDSixpREFBTSxDQUFDZ0QsR0FBRztjQUVUQyxPQUFPLEVBQUU7Z0JBQUVDLE9BQU8sRUFBRSxDQUFDO2dCQUFFMkMsQ0FBQyxFQUFFO2NBQUcsQ0FBRTtjQUMvQnpDLE9BQU8sRUFBRTtnQkFBRUYsT0FBTyxFQUFFLENBQUM7Z0JBQUUyQyxDQUFDLEVBQUU7Y0FBRSxDQUFFO2NBQzlCQyxJQUFJLEVBQUU7Z0JBQUU1QyxPQUFPLEVBQUUsQ0FBQztnQkFBRTJDLENBQUMsRUFBRSxDQUFDO2NBQUcsQ0FBRTtjQUM3QnhDLFVBQVUsRUFBRTtnQkFBRUMsUUFBUSxFQUFFO2NBQUksQ0FBRTtjQUFBUixRQUFBLGVBRTlCMUMsc0RBQUEsQ0FBQzJGLFNBQVM7Z0JBQUFqRCxRQUFBLEVBQ1A3QixhQUFhLENBQUNvRSxHQUFHLENBQUNXLEtBQUssaUJBQ3RCMUYsdURBQUEsQ0FBQzJGLFNBQVM7a0JBQUFuRCxRQUFBLGdCQUNSeEMsdURBQUEsQ0FBQzRGLFdBQVc7b0JBQUFwRCxRQUFBLGdCQUNWMUMsc0RBQUEsQ0FBQytGLFdBQVc7c0JBQUFyRCxRQUFBLEVBQUVrRCxLQUFLLENBQUN2RTtvQkFBTSxDQUFjLENBQUMsZUFDekNuQix1REFBQSxDQUFDOEYsV0FBVztzQkFBQ0MsUUFBUSxFQUFFTCxLQUFLLENBQUNwRSxTQUFTLEdBQUcsQ0FBRTtzQkFBQWtCLFFBQUEsR0FDeENrRCxLQUFLLENBQUNwRSxTQUFTLEdBQUcsQ0FBQyxHQUFHLEdBQUcsR0FBRyxHQUFHLEVBQUMsR0FBQyxFQUFDMEUsSUFBSSxDQUFDQyxHQUFHLENBQUNQLEtBQUssQ0FBQ3BFLFNBQVMsQ0FBQyxFQUFDLEdBQy9EO29CQUFBLENBQWEsQ0FBQztrQkFBQSxDQUNILENBQUMsZUFDZHhCLHNEQUFBLENBQUNvRyxZQUFZO29CQUFBMUQsUUFBQSxFQUFFa0QsS0FBSyxDQUFDdEUsT0FBTyxDQUFDb0QsY0FBYyxDQUFDO2tCQUFDLENBQWUsQ0FBQyxlQUM3RHhFLHVEQUFBLENBQUNtRyxVQUFVO29CQUFBM0QsUUFBQSxHQUFDLEdBQUMsRUFBQ2tELEtBQUssQ0FBQ3JFLEtBQUssQ0FBQ21ELGNBQWMsQ0FBQyxDQUFDO2tCQUFBLENBQWEsQ0FBQztnQkFBQSxHQVIxQ2tCLEtBQUssQ0FBQ3ZFLE1BU1gsQ0FDWjtjQUFDLENBQ087WUFBQyxHQW5CUixRQW9CTSxDQUNiLEVBRUFkLFNBQVMsS0FBSyxLQUFLLGlCQUNsQlAsc0RBQUEsQ0FBQ0osaURBQU0sQ0FBQ2dELEdBQUc7Y0FFVEMsT0FBTyxFQUFFO2dCQUFFQyxPQUFPLEVBQUUsQ0FBQztnQkFBRTJDLENBQUMsRUFBRTtjQUFHLENBQUU7Y0FDL0J6QyxPQUFPLEVBQUU7Z0JBQUVGLE9BQU8sRUFBRSxDQUFDO2dCQUFFMkMsQ0FBQyxFQUFFO2NBQUUsQ0FBRTtjQUM5QkMsSUFBSSxFQUFFO2dCQUFFNUMsT0FBTyxFQUFFLENBQUM7Z0JBQUUyQyxDQUFDLEVBQUUsQ0FBQztjQUFHLENBQUU7Y0FDN0J4QyxVQUFVLEVBQUU7Z0JBQUVDLFFBQVEsRUFBRTtjQUFJLENBQUU7Y0FBQVIsUUFBQSxlQUU5QjFDLHNEQUFBLENBQUNzRyxPQUFPO2dCQUFBNUQsUUFBQSxFQUNMM0IsYUFBYSxDQUFDa0UsR0FBRyxDQUFDc0IsR0FBRyxpQkFDcEJyRyx1REFBQSxDQUFDc0csT0FBTztrQkFBYzVFLE1BQU0sRUFBRTJFLEdBQUcsQ0FBQzNFLE1BQU87a0JBQUFjLFFBQUEsZ0JBQ3ZDMUMsc0RBQUEsQ0FBQ3lHLFFBQVE7b0JBQUEvRCxRQUFBLEVBQUU2RCxHQUFHLENBQUM1RTtrQkFBSyxDQUFXLENBQUMsZUFDaEN6Qix1REFBQSxDQUFDd0csT0FBTztvQkFBQWhFLFFBQUEsZ0JBQ04xQyxzREFBQSxDQUFDMkcsT0FBTztzQkFBQWpFLFFBQUEsRUFBRTZELEdBQUcsQ0FBQzdFO29CQUFJLENBQVUsQ0FBQyxlQUM3QjFCLHNEQUFBLENBQUM0RyxTQUFTO3NCQUFDaEYsTUFBTSxFQUFFMkUsR0FBRyxDQUFDM0UsTUFBTztzQkFBQWMsUUFBQSxFQUMzQjZELEdBQUcsQ0FBQzNFLE1BQU0sQ0FBQ2lGLFdBQVcsQ0FBQztvQkFBQyxDQUNoQixDQUFDLGVBQ1ozRyx1REFBQSxDQUFDNEcsUUFBUTtzQkFBQXBFLFFBQUEsR0FBQyxTQUFFLEVBQUM2RCxHQUFHLENBQUMxRSxLQUFLO29CQUFBLENBQVcsQ0FBQyxlQUNsQzNCLHVEQUFBLENBQUM2RyxRQUFRO3NCQUFBckUsUUFBQSxHQUFFNkQsR0FBRyxDQUFDekUsS0FBSyxFQUFDLE1BQUk7b0JBQUEsQ0FBVSxDQUFDO2tCQUFBLENBQzdCLENBQUMsRUFDVHlFLEdBQUcsQ0FBQ3hFLFFBQVEsaUJBQUkvQixzREFBQSxDQUFDZ0gsV0FBVztvQkFBQXRFLFFBQUEsRUFBQztrQkFBTSxDQUFhLENBQUM7Z0JBQUEsR0FWdEM2RCxHQUFHLENBQUM5RSxFQVdULENBQ1Y7Y0FBQyxDQUNLO1lBQUMsR0FyQk4sS0FzQk0sQ0FDYixFQUVBbEIsU0FBUyxLQUFLLE1BQU0saUJBQ25CUCxzREFBQSxDQUFDSixpREFBTSxDQUFDZ0QsR0FBRztjQUVUQyxPQUFPLEVBQUU7Z0JBQUVDLE9BQU8sRUFBRSxDQUFDO2dCQUFFMkMsQ0FBQyxFQUFFO2NBQUcsQ0FBRTtjQUMvQnpDLE9BQU8sRUFBRTtnQkFBRUYsT0FBTyxFQUFFLENBQUM7Z0JBQUUyQyxDQUFDLEVBQUU7Y0FBRSxDQUFFO2NBQzlCQyxJQUFJLEVBQUU7Z0JBQUU1QyxPQUFPLEVBQUUsQ0FBQztnQkFBRTJDLENBQUMsRUFBRSxDQUFDO2NBQUcsQ0FBRTtjQUM3QnhDLFVBQVUsRUFBRTtnQkFBRUMsUUFBUSxFQUFFO2NBQUksQ0FBRTtjQUFBUixRQUFBLGVBRTlCMUMsc0RBQUEsQ0FBQ2lILFFBQVE7Z0JBQUF2RSxRQUFBLEVBQ056QixTQUFTLENBQUNnRSxHQUFHLENBQUNpQyxJQUFJLGlCQUNqQmhILHVEQUFBLENBQUNpSCxRQUFRO2tCQUFBekUsUUFBQSxnQkFDUHhDLHVEQUFBLENBQUNrSCxVQUFVO29CQUFBMUUsUUFBQSxnQkFDVDFDLHNEQUFBLENBQUNxSCxRQUFRO3NCQUFBM0UsUUFBQSxFQUFFd0UsSUFBSSxDQUFDeEY7b0JBQUksQ0FBVyxDQUFDLGVBQ2hDeEIsdURBQUEsQ0FBQ29ILE9BQU87c0JBQUE1RSxRQUFBLEdBQUV3RSxJQUFJLENBQUNsRixHQUFHLEVBQUMsT0FBSztvQkFBQSxDQUFTLENBQUM7a0JBQUEsQ0FDeEIsQ0FBQyxlQUNiOUIsdURBQUEsQ0FBQ3FILFNBQVM7b0JBQUE3RSxRQUFBLGdCQUNSeEMsdURBQUEsQ0FBQ3NILFFBQVE7c0JBQUE5RSxRQUFBLGdCQUNQMUMsc0RBQUEsQ0FBQ3lILGFBQWE7d0JBQUEvRSxRQUFBLEVBQUM7c0JBQUcsQ0FBZSxDQUFDLGVBQ2xDeEMsdURBQUEsQ0FBQ3dILGFBQWE7d0JBQUFoRixRQUFBLEdBQUMsR0FBQyxFQUFDLENBQUN3RSxJQUFJLENBQUNqRixHQUFHLEdBQUcsT0FBTyxFQUFFMEYsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFDLEdBQUM7c0JBQUEsQ0FBZSxDQUFDO29CQUFBLENBQzFELENBQUMsZUFDWHpILHVEQUFBLENBQUNzSCxRQUFRO3NCQUFBOUUsUUFBQSxnQkFDUDFDLHNEQUFBLENBQUN5SCxhQUFhO3dCQUFBL0UsUUFBQSxFQUFDO3NCQUFTLENBQWUsQ0FBQyxlQUN4Q3hDLHVEQUFBLENBQUN3SCxhQUFhO3dCQUFBaEYsUUFBQSxHQUFDLEdBQUMsRUFBQ3dFLElBQUksQ0FBQ2hGLFVBQVU7c0JBQUEsQ0FBZ0IsQ0FBQztvQkFBQSxDQUN6QyxDQUFDLGVBQ1hoQyx1REFBQSxDQUFDc0gsUUFBUTtzQkFBQTlFLFFBQUEsZ0JBQ1AxQyxzREFBQSxDQUFDeUgsYUFBYTt3QkFBQS9FLFFBQUEsRUFBQztzQkFBTyxDQUFlLENBQUMsZUFDdEN4Qyx1REFBQSxDQUFDd0gsYUFBYTt3QkFBQWhGLFFBQUEsR0FBRXdFLElBQUksQ0FBQy9FLE9BQU8sRUFBQyxRQUFNO3NCQUFBLENBQWUsQ0FBQztvQkFBQSxDQUMzQyxDQUFDO2tCQUFBLENBQ0YsQ0FBQyxlQUNaakMsdURBQUEsQ0FBQzBILFdBQVc7b0JBQUFsRixRQUFBLGdCQUNWMUMsc0RBQUEsQ0FBQzZILFVBQVU7c0JBQ1QvRCxVQUFVLEVBQUU7d0JBQUVQLEtBQUssRUFBRTtzQkFBSyxDQUFFO3NCQUM1QlEsUUFBUSxFQUFFO3dCQUFFUixLQUFLLEVBQUU7c0JBQUssQ0FBRTtzQkFBQWIsUUFBQSxFQUMzQjtvQkFFRCxDQUFZLENBQUMsZUFDYjFDLHNEQUFBLENBQUM2SCxVQUFVO3NCQUNUQyxTQUFTO3NCQUNUaEUsVUFBVSxFQUFFO3dCQUFFUCxLQUFLLEVBQUU7c0JBQUssQ0FBRTtzQkFDNUJRLFFBQVEsRUFBRTt3QkFBRVIsS0FBSyxFQUFFO3NCQUFLLENBQUU7c0JBQUFiLFFBQUEsRUFDM0I7b0JBRUQsQ0FBWSxDQUFDO2tCQUFBLENBQ0YsQ0FBQztnQkFBQSxHQWpDRHdFLElBQUksQ0FBQ3pGLEVBa0NWLENBQ1g7Y0FBQyxDQUNNO1lBQUMsR0E1Q1AsTUE2Q00sQ0FDYixFQUVBbEIsU0FBUyxLQUFLLEtBQUssaUJBQ2xCUCxzREFBQSxDQUFDSixpREFBTSxDQUFDZ0QsR0FBRztjQUVUQyxPQUFPLEVBQUU7Z0JBQUVDLE9BQU8sRUFBRSxDQUFDO2dCQUFFMkMsQ0FBQyxFQUFFO2NBQUcsQ0FBRTtjQUMvQnpDLE9BQU8sRUFBRTtnQkFBRUYsT0FBTyxFQUFFLENBQUM7Z0JBQUUyQyxDQUFDLEVBQUU7Y0FBRSxDQUFFO2NBQzlCQyxJQUFJLEVBQUU7Z0JBQUU1QyxPQUFPLEVBQUUsQ0FBQztnQkFBRTJDLENBQUMsRUFBRSxDQUFDO2NBQUcsQ0FBRTtjQUM3QnhDLFVBQVUsRUFBRTtnQkFBRUMsUUFBUSxFQUFFO2NBQUksQ0FBRTtjQUFBUixRQUFBLGVBRTlCeEMsdURBQUEsQ0FBQzZILFVBQVU7Z0JBQUFyRixRQUFBLGdCQUNUeEMsdURBQUEsQ0FBQzhILFFBQVE7a0JBQUF0RixRQUFBLGdCQUNQeEMsdURBQUEsQ0FBQytILE9BQU87b0JBQUF2RixRQUFBLGdCQUNOMUMsc0RBQUEsQ0FBQ2tJLFlBQVk7c0JBQUF4RixRQUFBLEVBQUM7b0JBQU0sQ0FBYyxDQUFDLGVBQ25DMUMsc0RBQUEsQ0FBQ21JLFlBQVk7c0JBQUF6RixRQUFBLEVBQUM7b0JBQWlCLENBQWMsQ0FBQztrQkFBQSxDQUN2QyxDQUFDLGVBQ1Z4Qyx1REFBQSxDQUFDK0gsT0FBTztvQkFBQXZGLFFBQUEsZ0JBQ04xQyxzREFBQSxDQUFDa0ksWUFBWTtzQkFBQXhGLFFBQUEsRUFBQztvQkFBQyxDQUFjLENBQUMsZUFDOUIxQyxzREFBQSxDQUFDbUksWUFBWTtzQkFBQXpGLFFBQUEsRUFBQztvQkFBb0IsQ0FBYyxDQUFDO2tCQUFBLENBQzFDLENBQUMsZUFDVnhDLHVEQUFBLENBQUMrSCxPQUFPO29CQUFBdkYsUUFBQSxnQkFDTjFDLHNEQUFBLENBQUNrSSxZQUFZO3NCQUFBeEYsUUFBQSxFQUFDO29CQUFHLENBQWMsQ0FBQyxlQUNoQzFDLHNEQUFBLENBQUNtSSxZQUFZO3NCQUFBekYsUUFBQSxFQUFDO29CQUFxQixDQUFjLENBQUM7a0JBQUEsQ0FDM0MsQ0FBQztnQkFBQSxDQUNGLENBQUMsZUFFWDFDLHNEQUFBLENBQUNvSSxhQUFhO2tCQUFBMUYsUUFBQSxlQUNaeEMsdURBQUEsQ0FBQ21JLFlBQVk7b0JBQUEzRixRQUFBLGdCQUNYMUMsc0RBQUEsQ0FBQ3NJLGFBQWE7c0JBQUE1RixRQUFBLEVBQUM7b0JBQTZCLENBQWUsQ0FBQyxlQUM1RDFDLHNEQUFBLENBQUN1SSxtQkFBbUI7c0JBQUE3RixRQUFBLEVBQUM7b0JBRXJCLENBQXFCLENBQUMsZUFDdEJ4Qyx1REFBQSxDQUFDc0ksYUFBYTtzQkFBQTlGLFFBQUEsZ0JBQ1p4Qyx1REFBQSxDQUFDdUksVUFBVTt3QkFBQS9GLFFBQUEsZ0JBQ1QxQyxzREFBQSxDQUFDMEksU0FBUzswQkFBQWhHLFFBQUEsRUFBQzt3QkFBTyxDQUFXLENBQUMsZUFDOUIxQyxzREFBQSxDQUFDMkksT0FBTzswQkFBQ0MsS0FBSyxFQUFFO3dCQUFHLENBQUUsQ0FBQztzQkFBQSxDQUNaLENBQUMsZUFDYjFJLHVEQUFBLENBQUN1SSxVQUFVO3dCQUFBL0YsUUFBQSxnQkFDVDFDLHNEQUFBLENBQUMwSSxTQUFTOzBCQUFBaEcsUUFBQSxFQUFDO3dCQUFXLENBQVcsQ0FBQyxlQUNsQzFDLHNEQUFBLENBQUMySSxPQUFPOzBCQUFDQyxLQUFLLEVBQUU7d0JBQUcsQ0FBRSxDQUFDO3NCQUFBLENBQ1osQ0FBQztvQkFBQSxDQUNBLENBQUMsZUFDaEIxSSx1REFBQSxDQUFDMkksZUFBZTtzQkFBQW5HLFFBQUEsZ0JBQ2QxQyxzREFBQSxDQUFDOEksVUFBVTt3QkFDVGhGLFVBQVUsRUFBRTswQkFBRVAsS0FBSyxFQUFFO3dCQUFLLENBQUU7d0JBQzVCUSxRQUFRLEVBQUU7MEJBQUVSLEtBQUssRUFBRTt3QkFBSyxDQUFFO3dCQUFBYixRQUFBLEVBQzNCO3NCQUVELENBQVksQ0FBQyxlQUNiMUMsc0RBQUEsQ0FBQzhJLFVBQVU7d0JBQ1RoQixTQUFTO3dCQUNUaEUsVUFBVSxFQUFFOzBCQUFFUCxLQUFLLEVBQUU7d0JBQUssQ0FBRTt3QkFDNUJRLFFBQVEsRUFBRTswQkFBRVIsS0FBSyxFQUFFO3dCQUFLLENBQUU7d0JBQUFiLFFBQUEsRUFDM0I7c0JBRUQsQ0FBWSxDQUFDO29CQUFBLENBQ0UsQ0FBQztrQkFBQSxDQUNOO2dCQUFDLENBQ0YsQ0FBQztjQUFBLENBQ047WUFBQyxHQXZEVCxLQXdETSxDQUNiO1VBQUEsQ0FDYztRQUFDLENBQ1IsQ0FBQztNQUFBLENBQ0csQ0FDbkI7SUFBQSxDQUNhO0VBQUMsQ0FDSixDQUFDO0FBRXBCLENBQUM7O0FBRUQ7QUFBQXBDLEVBQUEsQ0FwV01ILGFBQTJDO0FBQUE0SSxFQUFBLEdBQTNDNUksYUFBMkM7QUFxV2pELE1BQU1zQyxhQUFhLEdBQUczQyx5REFBTSxDQUFDOEMsR0FBSTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNvRyxHQUFBLEdBUEl2RyxhQUFhO0FBU25CLE1BQU1FLGNBQWMsR0FBRzdDLHlEQUFNLENBQUM4QyxHQUFJO0FBQ2xDO0FBQ0E7QUFDQSxDQUFDO0FBQUNxRyxHQUFBLEdBSEl0RyxjQUFjO0FBS3BCLE1BQU1RLFlBQVksR0FBR3JELHlEQUFNLENBQUNvSixFQUFHO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNDLEdBQUEsR0FaSWhHLFlBQVk7QUFjbEIsTUFBTUMsZUFBZSxHQUFHdEQseURBQU0sQ0FBQ3NKLENBQUU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNDLEdBQUEsR0FSSWpHLGVBQWU7QUFVckIsTUFBTUMsZ0JBQWdCLEdBQUd2RCx5REFBTSxDQUFDOEMsR0FBSTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzBHLEdBQUEsR0FMSWpHLGdCQUFnQjtBQU90QixNQUFNQyxjQUFjLEdBQUd4RCw2REFBTSxDQUFDRixpREFBTSxDQUFDZ0QsR0FBRyxDQUFFO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMyRyxHQUFBLEdBVElqRyxjQUFjO0FBV3BCLE1BQU1FLGNBQWMsR0FBRzFELHlEQUFNLENBQUM4QyxHQUFJO0FBQ2xDO0FBQ0E7QUFDQSxDQUFDO0FBQUM0RyxHQUFBLEdBSEloRyxjQUFjO0FBS3BCLE1BQU1DLGVBQWUsR0FBRzNELHlEQUFNLENBQUMySixFQUFHO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDQyxHQUFBLEdBTElqRyxlQUFlO0FBT3JCLE1BQU1DLHFCQUFxQixHQUFHNUQseURBQU0sQ0FBQ3NKLENBQUU7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDTyxJQUFBLEdBSklqRyxxQkFBcUI7QUFNM0IsTUFBTUMsYUFBYSxHQUFHN0QseURBQU0sQ0FBQzhDLEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNnSCxJQUFBLEdBUklqRyxhQUFhO0FBVW5CLE1BQU1DLFlBQVksR0FBRzlELDZEQUFNLENBQUNGLGlEQUFNLENBQUNpSyxNQUFNLENBQUU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ0MsSUFBQSxHQVpJbEcsWUFBWTtBQWNsQixNQUFNSSxVQUFVLEdBQUdsRSx5REFBTSxDQUFDOEMsR0FBSTtBQUM5QjtBQUNBO0FBQ0EsQ0FBQztBQUFDbUgsSUFBQSxHQUhJL0YsVUFBVTtBQUtoQixNQUFNQyxVQUFVLEdBQUduRSx5REFBTSxDQUFDOEMsR0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQUFvSCxJQUFBLEdBTk0vRixVQUFVO0FBT2hCLE1BQU1DLGdCQUFnQixHQUFHcEUseURBQU0sQ0FBQzhDLEdBQUksRUFBQztBQUFDcUgsSUFBQSxHQUFoQy9GLGdCQUFnQjtBQUV0QixNQUFNQyxlQUFlLEdBQUdyRSx5REFBTSxDQUFDOEMsR0FBSTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNzSCxJQUFBLEdBVkkvRixlQUFlO0FBWXJCLE1BQU1DLFFBQVEsR0FBR3RFLHlEQUFNLENBQUM4QyxHQUFJO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VILElBQUEsR0FKSS9GLFFBQVE7QUFNZCxNQUFNQyxVQUFVLEdBQUd2RSx5REFBTSxDQUFDOEMsR0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDd0gsSUFBQSxHQVRJL0YsVUFBVTtBQVdoQixNQUFNQyxXQUFXLEdBQUd4RSx5REFBTSxDQUFDOEMsR0FBSSxFQUFDO0FBQUN5SCxJQUFBLEdBQTNCL0YsV0FBVztBQUVqQixNQUFNQyxXQUFXLEdBQUd6RSx5REFBTSxDQUFDOEMsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMwSCxJQUFBLEdBSkkvRixXQUFXO0FBTWpCLE1BQU1FLGNBQWMsR0FBRzNFLHlEQUFNLENBQUM4QyxHQUFJO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzJILElBQUEsR0FKSTlGLGNBQWM7QUFNcEIsTUFBTUUsV0FBVyxHQUFHN0UseURBQU0sQ0FBQzhDLEdBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDNEgsSUFBQSxHQUpJN0YsV0FBVztBQU1qQixNQUFNQyxnQkFBZ0IsR0FBRzlFLHlEQUFNLENBQUM4QyxHQUFJO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM2SCxJQUFBLEdBTkk3RixnQkFBZ0I7QUFRdEIsTUFBTUMsV0FBVyxHQUFHL0UseURBQU0sQ0FBQzhDLEdBQUk7QUFDL0I7QUFDQTtBQUNBLENBQUM7QUFBQzhILElBQUEsR0FISTdGLFdBQVc7QUFLakIsTUFBTUMsYUFBYSxHQUFHaEYseURBQU0sQ0FBQzhDLEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDK0gsSUFBQSxHQVBJN0YsYUFBYTtBQVNuQixNQUFNSyxTQUFTLEdBQUdyRiw2REFBTSxDQUFDRixpREFBTSxDQUFDaUssTUFBTSxDQUF1QjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQmUsS0FBSyxJQUFJQSxLQUFLLENBQUN4RixNQUFNLEdBQUcseUJBQXlCLEdBQUcsYUFBYztBQUNsRixXQUFXd0YsS0FBSyxJQUFJQSxLQUFLLENBQUN4RixNQUFNLEdBQUcsU0FBUyxHQUFHLDBCQUEyQjtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUN5RixJQUFBLEdBbkJJMUYsU0FBUztBQXFCZixNQUFNRSxPQUFPLEdBQUd2Rix5REFBTSxDQUFDZ0wsSUFBSztBQUM1QjtBQUNBLENBQUM7QUFBQ0MsSUFBQSxHQUZJMUYsT0FBTztBQUliLE1BQU1DLFFBQVEsR0FBR3hGLHlEQUFNLENBQUNnTCxJQUFLO0FBQzdCO0FBQ0EsQ0FBQztBQUFDRSxJQUFBLEdBRkkxRixRQUFRO0FBSWQsTUFBTUMsVUFBVSxHQUFHekYseURBQU0sQ0FBQzhDLEdBQUk7QUFDOUI7QUFDQSxDQUFDO0FBQUNxSSxJQUFBLEdBRkkxRixVQUFVO0FBSWhCLE1BQU1JLFNBQVMsR0FBRzdGLHlEQUFNLENBQUM4QyxHQUFJO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3NJLElBQUEsR0FKSXZGLFNBQVM7QUFNZixNQUFNRSxTQUFTLEdBQUcvRix5REFBTSxDQUFDOEMsR0FBSTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VJLElBQUEsR0FMSXRGLFNBQVM7QUFPZixNQUFNQyxXQUFXLEdBQUdoRyx5REFBTSxDQUFDOEMsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3dJLElBQUEsR0FMSXRGLFdBQVc7QUFPakIsTUFBTUMsV0FBVyxHQUFHakcseURBQU0sQ0FBQzhDLEdBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDeUksSUFBQSxHQUpJdEYsV0FBVztBQU1qQixNQUFNQyxXQUFXLEdBQUdsRyx5REFBTSxDQUFDOEMsR0FBMkI7QUFDdEQsV0FBV2dJLEtBQUssSUFBSUEsS0FBSyxDQUFDM0UsUUFBUSxHQUFHLFNBQVMsR0FBRyxTQUFVO0FBQzNEO0FBQ0E7QUFDQSxDQUFDO0FBQUNxRixJQUFBLEdBSkl0RixXQUFXO0FBTWpCLE1BQU1JLFlBQVksR0FBR3RHLHlEQUFNLENBQUM4QyxHQUFJO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDMkksSUFBQSxHQUxJbkYsWUFBWTtBQU9sQixNQUFNQyxVQUFVLEdBQUd2Ryx5REFBTSxDQUFDOEMsR0FBSTtBQUM5QjtBQUNBO0FBQ0EsQ0FBQztBQUFDNEksSUFBQSxHQUhJbkYsVUFBVTtBQUtoQixNQUFNQyxPQUFPLEdBQUd4Ryx5REFBTSxDQUFDOEMsR0FBSTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM2SSxJQUFBLEdBSkluRixPQUFPO0FBTWIsTUFBTUUsT0FBTyxHQUFHMUcseURBQU0sQ0FBQzhDLEdBQXdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCZ0ksS0FBSyxJQUFJcEksY0FBYyxDQUFDb0ksS0FBSyxDQUFDaEosTUFBTSxDQUFFO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQ2dKLEtBQUssSUFBSXBJLGNBQWMsQ0FBQ29JLEtBQUssQ0FBQ2hKLE1BQU0sQ0FBRTtBQUNoRjtBQUNBO0FBQ0EsQ0FBQztBQUFDOEosSUFBQSxHQWxCSWxGLE9BQU87QUFvQmIsTUFBTUMsUUFBUSxHQUFHM0cseURBQU0sQ0FBQzhDLEdBQUk7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMrSSxJQUFBLEdBTElsRixRQUFRO0FBT2QsTUFBTUMsT0FBTyxHQUFHNUcseURBQU0sQ0FBQzhDLEdBQUk7QUFDM0I7QUFDQTtBQUNBLENBQUM7QUFBQ2dKLElBQUEsR0FISWxGLE9BQU87QUFLYixNQUFNQyxPQUFPLEdBQUc3Ryx5REFBTSxDQUFDOEMsR0FBSTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2lKLElBQUEsR0FMSWxGLE9BQU87QUFPYixNQUFNQyxTQUFTLEdBQUc5Ryx5REFBTSxDQUFDOEMsR0FBd0I7QUFDakQsV0FBV2dJLEtBQUssSUFBSXBJLGNBQWMsQ0FBQ29JLEtBQUssQ0FBQ2hKLE1BQU0sQ0FBRTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2tLLElBQUEsR0FOSWxGLFNBQVM7QUFRZixNQUFNRSxRQUFRLEdBQUdoSCx5REFBTSxDQUFDOEMsR0FBSTtBQUM1QjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNtSixJQUFBLEdBSklqRixRQUFRO0FBTWQsTUFBTUMsUUFBUSxHQUFHakgseURBQU0sQ0FBQzhDLEdBQUk7QUFDNUI7QUFDQTtBQUNBLENBQUM7QUFBQ29KLElBQUEsR0FISWpGLFFBQVE7QUFLZCxNQUFNQyxXQUFXLEdBQUdsSCx5REFBTSxDQUFDOEMsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNxSixJQUFBLEdBVklqRixXQUFXO0FBWWpCLE1BQU1DLFFBQVEsR0FBR25ILHlEQUFNLENBQUM4QyxHQUFJO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3NKLElBQUEsR0FKSWpGLFFBQVE7QUFNZCxNQUFNRSxRQUFRLEdBQUdySCx5REFBTSxDQUFDOEMsR0FBSTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VKLElBQUEsR0FMSWhGLFFBQVE7QUFPZCxNQUFNQyxVQUFVLEdBQUd0SCx5REFBTSxDQUFDOEMsR0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3dKLElBQUEsR0FMSWhGLFVBQVU7QUFPaEIsTUFBTUMsUUFBUSxHQUFHdkgseURBQU0sQ0FBQzhDLEdBQUk7QUFDNUI7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDeUosSUFBQSxHQUpJaEYsUUFBUTtBQU1kLE1BQU1DLE9BQU8sR0FBR3hILHlEQUFNLENBQUM4QyxHQUFJO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzBKLElBQUEsR0FKSWhGLE9BQU87QUFNYixNQUFNQyxTQUFTLEdBQUd6SCx5REFBTSxDQUFDOEMsR0FBSTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzJKLElBQUEsR0FMSWhGLFNBQVM7QUFPZixNQUFNQyxRQUFRLEdBQUcxSCx5REFBTSxDQUFDOEMsR0FBSTtBQUM1QjtBQUNBLENBQUM7QUFBQzRKLElBQUEsR0FGSWhGLFFBQVE7QUFJZCxNQUFNQyxhQUFhLEdBQUczSCx5REFBTSxDQUFDOEMsR0FBSTtBQUNqQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM2SixJQUFBLEdBSkloRixhQUFhO0FBTW5CLE1BQU1DLGFBQWEsR0FBRzVILHlEQUFNLENBQUM4QyxHQUFJO0FBQ2pDO0FBQ0E7QUFDQSxDQUFDO0FBQUM4SixJQUFBLEdBSEloRixhQUFhO0FBS25CLE1BQU1FLFdBQVcsR0FBRzlILHlEQUFNLENBQUM4QyxHQUFJO0FBQy9CO0FBQ0E7QUFDQSxDQUFDO0FBQUMrSixJQUFBLEdBSEkvRSxXQUFXO0FBS2pCLE1BQU1DLFVBQVUsR0FBRy9ILDZEQUFNLENBQUNGLGlEQUFNLENBQUNpSyxNQUFNLENBQTJCO0FBQ2xFO0FBQ0EsZ0JBQWdCZSxLQUFLLElBQUlBLEtBQUssQ0FBQzlDLFNBQVMsR0FDbEMsYUFBYSxHQUNiLDJDQUE0QztBQUNsRDtBQUNBLFlBQVk4QyxLQUFLLElBQUlBLEtBQUssQ0FBQzlDLFNBQVMsR0FBRyxvQ0FBb0MsR0FBRyxNQUFPO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDOEUsSUFBQSxHQVhJL0UsVUFBVTtBQWFoQixNQUFNRSxVQUFVLEdBQUdqSSx5REFBTSxDQUFDOEMsR0FBSSxFQUFDO0FBQUNpSyxJQUFBLEdBQTFCOUUsVUFBVTtBQUVoQixNQUFNQyxRQUFRLEdBQUdsSSx5REFBTSxDQUFDOEMsR0FBSTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2tLLElBQUEsR0FMSTlFLFFBQVE7QUFPZCxNQUFNQyxPQUFPLEdBQUduSSx5REFBTSxDQUFDOEMsR0FBSTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDbUssSUFBQSxHQU5JOUUsT0FBTztBQVFiLE1BQU1DLFlBQVksR0FBR3BJLHlEQUFNLENBQUM4QyxHQUFJO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDb0ssSUFBQSxHQUxJOUUsWUFBWTtBQU9sQixNQUFNQyxZQUFZLEdBQUdySSx5REFBTSxDQUFDOEMsR0FBSTtBQUNoQztBQUNBO0FBQ0EsQ0FBQztBQUFDcUssSUFBQSxHQUhJOUUsWUFBWTtBQUtsQixNQUFNQyxhQUFhLEdBQUd0SSx5REFBTSxDQUFDOEMsR0FBSSxFQUFDO0FBQUNzSyxJQUFBLEdBQTdCOUUsYUFBYTtBQUVuQixNQUFNQyxZQUFZLEdBQUd2SSx5REFBTSxDQUFDOEMsR0FBSTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VLLElBQUEsR0FMSTlFLFlBQVk7QUFPbEIsTUFBTUMsYUFBYSxHQUFHeEkseURBQU0sQ0FBQ3NOLEVBQUc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNDLElBQUEsR0FMSS9FLGFBQWE7QUFPbkIsTUFBTUMsbUJBQW1CLEdBQUd6SSx5REFBTSxDQUFDc0osQ0FBRTtBQUNyQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNrRSxJQUFBLEdBSkkvRSxtQkFBbUI7QUFNekIsTUFBTUMsYUFBYSxHQUFHMUkseURBQU0sQ0FBQzhDLEdBQUk7QUFDakM7QUFDQSxDQUFDO0FBQUMySyxJQUFBLEdBRkkvRSxhQUFhO0FBSW5CLE1BQU1DLFVBQVUsR0FBRzNJLHlEQUFNLENBQUM4QyxHQUFJO0FBQzlCO0FBQ0EsQ0FBQztBQUFDNEssSUFBQSxHQUZJL0UsVUFBVTtBQUloQixNQUFNQyxTQUFTLEdBQUc1SSx5REFBTSxDQUFDOEMsR0FBSTtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM2SyxJQUFBLEdBSkkvRSxTQUFTO0FBTWYsTUFBTUMsT0FBTyxHQUFHN0kseURBQU0sQ0FBQzhDLEdBQXVCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWFnSSxLQUFLLElBQUlBLEtBQUssQ0FBQ2hDLEtBQU07QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM4RSxJQUFBLEdBZkkvRSxPQUFPO0FBaUJiLE1BQU1FLGVBQWUsR0FBRy9JLHlEQUFNLENBQUM4QyxHQUFJO0FBQ25DO0FBQ0E7QUFDQSxDQUFDO0FBQUMrSyxJQUFBLEdBSEk5RSxlQUFlO0FBS3JCLE1BQU1DLFVBQVUsR0FBR2hKLDZEQUFNLENBQUNGLGlEQUFNLENBQUNpSyxNQUFNLENBQTJCO0FBQ2xFO0FBQ0EsZ0JBQWdCZSxLQUFLLElBQUlBLEtBQUssQ0FBQzlDLFNBQVMsR0FDbEMsYUFBYSxHQUNiLDJDQUE0QztBQUNsRDtBQUNBLFlBQVk4QyxLQUFLLElBQUlBLEtBQUssQ0FBQzlDLFNBQVMsR0FBRyxvQ0FBb0MsR0FBRyxNQUFPO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUFBOEYsSUFBQSxHQWJNOUUsVUFBVTtBQWNoQixNQUFNdEcsY0FBYyxHQUFJWixNQUFjLElBQUs7RUFDekMsUUFBUUEsTUFBTTtJQUNaLEtBQUssV0FBVztNQUFFLE9BQU8sU0FBUztJQUNsQyxLQUFLLE1BQU07TUFBRSxPQUFPLFNBQVM7SUFDN0IsS0FBSyxNQUFNO01BQUUsT0FBTyxTQUFTO0lBQzdCO01BQVMsT0FBTyxTQUFTO0VBQzNCO0FBQ0YsQ0FBQztBQUVELCtEQUFlekIsYUFBYSxFQUFDO0FBQUEsSUFBQTRJLEVBQUEsRUFBQUMsR0FBQSxFQUFBQyxHQUFBLEVBQUFFLEdBQUEsRUFBQUUsR0FBQSxFQUFBQyxHQUFBLEVBQUFDLEdBQUEsRUFBQUMsR0FBQSxFQUFBRSxHQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBRSxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUUsSUFBQSxFQUFBRSxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFFLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQTtBQUFBQyxZQUFBLENBQUE5RSxFQUFBO0FBQUE4RSxZQUFBLENBQUE3RSxHQUFBO0FBQUE2RSxZQUFBLENBQUE1RSxHQUFBO0FBQUE0RSxZQUFBLENBQUExRSxHQUFBO0FBQUEwRSxZQUFBLENBQUF4RSxHQUFBO0FBQUF3RSxZQUFBLENBQUF2RSxHQUFBO0FBQUF1RSxZQUFBLENBQUF0RSxHQUFBO0FBQUFzRSxZQUFBLENBQUFyRSxHQUFBO0FBQUFxRSxZQUFBLENBQUFuRSxHQUFBO0FBQUFtRSxZQUFBLENBQUFsRSxJQUFBO0FBQUFrRSxZQUFBLENBQUFqRSxJQUFBO0FBQUFpRSxZQUFBLENBQUEvRCxJQUFBO0FBQUErRCxZQUFBLENBQUE5RCxJQUFBO0FBQUE4RCxZQUFBLENBQUE3RCxJQUFBO0FBQUE2RCxZQUFBLENBQUE1RCxJQUFBO0FBQUE0RCxZQUFBLENBQUEzRCxJQUFBO0FBQUEyRCxZQUFBLENBQUExRCxJQUFBO0FBQUEwRCxZQUFBLENBQUF6RCxJQUFBO0FBQUF5RCxZQUFBLENBQUF4RCxJQUFBO0FBQUF3RCxZQUFBLENBQUF2RCxJQUFBO0FBQUF1RCxZQUFBLENBQUF0RCxJQUFBO0FBQUFzRCxZQUFBLENBQUFyRCxJQUFBO0FBQUFxRCxZQUFBLENBQUFwRCxJQUFBO0FBQUFvRCxZQUFBLENBQUFuRCxJQUFBO0FBQUFtRCxZQUFBLENBQUFsRCxJQUFBO0FBQUFrRCxZQUFBLENBQUFoRCxJQUFBO0FBQUFnRCxZQUFBLENBQUE5QyxJQUFBO0FBQUE4QyxZQUFBLENBQUE3QyxJQUFBO0FBQUE2QyxZQUFBLENBQUE1QyxJQUFBO0FBQUE0QyxZQUFBLENBQUEzQyxJQUFBO0FBQUEyQyxZQUFBLENBQUExQyxJQUFBO0FBQUEwQyxZQUFBLENBQUF6QyxJQUFBO0FBQUF5QyxZQUFBLENBQUF4QyxJQUFBO0FBQUF3QyxZQUFBLENBQUF2QyxJQUFBO0FBQUF1QyxZQUFBLENBQUF0QyxJQUFBO0FBQUFzQyxZQUFBLENBQUFyQyxJQUFBO0FBQUFxQyxZQUFBLENBQUFwQyxJQUFBO0FBQUFvQyxZQUFBLENBQUFuQyxJQUFBO0FBQUFtQyxZQUFBLENBQUFsQyxJQUFBO0FBQUFrQyxZQUFBLENBQUFqQyxJQUFBO0FBQUFpQyxZQUFBLENBQUFoQyxJQUFBO0FBQUFnQyxZQUFBLENBQUEvQixJQUFBO0FBQUErQixZQUFBLENBQUE5QixJQUFBO0FBQUE4QixZQUFBLENBQUE3QixJQUFBO0FBQUE2QixZQUFBLENBQUE1QixJQUFBO0FBQUE0QixZQUFBLENBQUEzQixJQUFBO0FBQUEyQixZQUFBLENBQUExQixJQUFBO0FBQUEwQixZQUFBLENBQUF6QixJQUFBO0FBQUF5QixZQUFBLENBQUF4QixJQUFBO0FBQUF3QixZQUFBLENBQUF2QixJQUFBO0FBQUF1QixZQUFBLENBQUF0QixJQUFBO0FBQUFzQixZQUFBLENBQUFyQixJQUFBO0FBQUFxQixZQUFBLENBQUFwQixJQUFBO0FBQUFvQixZQUFBLENBQUFuQixJQUFBO0FBQUFtQixZQUFBLENBQUFsQixJQUFBO0FBQUFrQixZQUFBLENBQUFqQixJQUFBO0FBQUFpQixZQUFBLENBQUFoQixJQUFBO0FBQUFnQixZQUFBLENBQUFmLElBQUE7QUFBQWUsWUFBQSxDQUFBZCxJQUFBO0FBQUFjLFlBQUEsQ0FBQWIsSUFBQTtBQUFBYSxZQUFBLENBQUFaLElBQUE7QUFBQVksWUFBQSxDQUFBWCxJQUFBO0FBQUFXLFlBQUEsQ0FBQVYsSUFBQTtBQUFBVSxZQUFBLENBQUFSLElBQUE7QUFBQVEsWUFBQSxDQUFBUCxJQUFBO0FBQUFPLFlBQUEsQ0FBQU4sSUFBQTtBQUFBTSxZQUFBLENBQUFMLElBQUE7QUFBQUssWUFBQSxDQUFBSixJQUFBO0FBQUFJLFlBQUEsQ0FBQUgsSUFBQTtBQUFBRyxZQUFBLENBQUFGLElBQUE7QUFBQUUsWUFBQSxDQUFBRCxJQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL1dlYjNEYXNoYm9hcmQudHN4P2ZhMzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdzdHlsZWQtY29tcG9uZW50cyc7XG5cbmludGVyZmFjZSBXZWIzRGFzaGJvYXJkUHJvcHMge1xuICB3ZWIzU3RhdHVzOiBhbnk7XG4gIG9uQ29ubmVjdFdhbGxldDogKCkgPT4gdm9pZDtcbn1cblxuaW50ZXJmYWNlIE5GVENhcmQge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGltYWdlOiBzdHJpbmc7XG4gIHJhcml0eTogJ2NvbW1vbicgfCAncmFyZScgfCAnZXBpYycgfCAnbGVnZW5kYXJ5JztcbiAgcG93ZXI6IG51bWJlcjtcbiAgcHJpY2U6IG51bWJlcjtcbiAgaXNTdGFrZWQ6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBEZUZpUG9vbCB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgYXByOiBudW1iZXI7XG4gIHR2bDogbnVtYmVyO1xuICB1c2VyU3Rha2VkOiBudW1iZXI7XG4gIHJld2FyZHM6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFRva2VuQmFsYW5jZSB7XG4gIHN5bWJvbDogc3RyaW5nO1xuICBiYWxhbmNlOiBudW1iZXI7XG4gIHZhbHVlOiBudW1iZXI7XG4gIGNoYW5nZTI0aDogbnVtYmVyO1xufVxuXG5jb25zdCBXZWIzRGFzaGJvYXJkOiBSZWFjdC5GQzxXZWIzRGFzaGJvYXJkUHJvcHM+ID0gKHsgd2ViM1N0YXR1cywgb25Db25uZWN0V2FsbGV0IH0pID0+IHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCd3YWxsZXQnIHwgJ25mdCcgfCAnZGVmaScgfCAnZGFvJz4oJ3dhbGxldCcpO1xuICBjb25zdCBbd2FsbGV0Q29ubmVjdGVkLCBzZXRXYWxsZXRDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdXNlckFkZHJlc3MsIHNldFVzZXJBZGRyZXNzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Rva2VuQmFsYW5jZXMsIHNldFRva2VuQmFsYW5jZXNdID0gdXNlU3RhdGU8VG9rZW5CYWxhbmNlW10+KFtdKTtcbiAgY29uc3QgW25mdENvbGxlY3Rpb24sIHNldE5mdENvbGxlY3Rpb25dID0gdXNlU3RhdGU8TkZUQ2FyZFtdPihbXSk7XG4gIGNvbnN0IFtkZWZpUG9vbHMsIHNldERlZmlQb29sc10gPSB1c2VTdGF0ZTxEZUZpUG9vbFtdPihbXSk7XG4gIGNvbnN0IFt0b3RhbFBvcnRmb2xpb1ZhbHVlLCBzZXRUb3RhbFBvcnRmb2xpb1ZhbHVlXSA9IHVzZVN0YXRlKDApO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g0KHQuNC80YPQu9GP0YbQuNGPINC00LDQvdC90YvRhSBXZWIzXG4gICAgaWYgKHdhbGxldENvbm5lY3RlZCkge1xuICAgICAgc2V0VXNlckFkZHJlc3MoJzB4NzQyZDM1Q2M2NjM0QzA1MzI5MjVhM2I4RDRDOWRiOTY1OTBjNEM1ZCcpO1xuICAgICAgc2V0VG9rZW5CYWxhbmNlcyhbXG4gICAgICAgIHsgc3ltYm9sOiAnS09aWVInLCBiYWxhbmNlOiAxNTQyMC41LCB2YWx1ZTogNzcxMC4yNSwgY2hhbmdlMjRoOiAxMi41IH0sXG4gICAgICAgIHsgc3ltYm9sOiAnRVRIJywgYmFsYW5jZTogMi40NSwgdmFsdWU6IDQ5MDAsIGNoYW5nZTI0aDogLTMuMiB9LFxuICAgICAgICB7IHN5bWJvbDogJ1VTREMnLCBiYWxhbmNlOiAxMjUwLCB2YWx1ZTogMTI1MCwgY2hhbmdlMjRoOiAwLjEgfVxuICAgICAgXSk7XG4gICAgICBcbiAgICAgIHNldE5mdENvbGxlY3Rpb24oW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgICBuYW1lOiAnTGVnZW5kYXJ5IEtpbmcnLFxuICAgICAgICAgIGltYWdlOiAn8J+RkScsXG4gICAgICAgICAgcmFyaXR5OiAnbGVnZW5kYXJ5JyxcbiAgICAgICAgICBwb3dlcjogOTUsXG4gICAgICAgICAgcHJpY2U6IDIuNSxcbiAgICAgICAgICBpc1N0YWtlZDogdHJ1ZVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICBuYW1lOiAnRXBpYyBRdWVlbicsXG4gICAgICAgICAgaW1hZ2U6ICfwn5G4JyxcbiAgICAgICAgICByYXJpdHk6ICdlcGljJyxcbiAgICAgICAgICBwb3dlcjogODUsXG4gICAgICAgICAgcHJpY2U6IDEuMixcbiAgICAgICAgICBpc1N0YWtlZDogZmFsc2VcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMycsXG4gICAgICAgICAgbmFtZTogJ1JhcmUgQWNlJyxcbiAgICAgICAgICBpbWFnZTogJ/Cfg48nLFxuICAgICAgICAgIHJhcml0eTogJ3JhcmUnLFxuICAgICAgICAgIHBvd2VyOiA3NSxcbiAgICAgICAgICBwcmljZTogMC44LFxuICAgICAgICAgIGlzU3Rha2VkOiB0cnVlXG4gICAgICAgIH1cbiAgICAgIF0pO1xuXG4gICAgICBzZXREZWZpUG9vbHMoW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgICBuYW1lOiAnS09aWVIvRVRIJyxcbiAgICAgICAgICBhcHI6IDE0NS4yLFxuICAgICAgICAgIHR2bDogMjUwMDAwMCxcbiAgICAgICAgICB1c2VyU3Rha2VkOiAxNTAwLFxuICAgICAgICAgIHJld2FyZHM6IDI1LjRcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnMicsXG4gICAgICAgICAgbmFtZTogJ0tPWllSL1VTREMnLFxuICAgICAgICAgIGFwcjogODkuNyxcbiAgICAgICAgICB0dmw6IDE4MDAwMDAsXG4gICAgICAgICAgdXNlclN0YWtlZDogODAwLFxuICAgICAgICAgIHJld2FyZHM6IDEyLjFcbiAgICAgICAgfVxuICAgICAgXSk7XG5cbiAgICAgIHNldFRvdGFsUG9ydGZvbGlvVmFsdWUoMTM4NjAuMjUpO1xuICAgIH1cbiAgfSwgW3dhbGxldENvbm5lY3RlZF0pO1xuXG4gIGNvbnN0IGNvbm5lY3RXYWxsZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgLy8g0KHQuNC80YPQu9GP0YbQuNGPINC/0L7QtNC60LvRjtGH0LXQvdC40Y8g0LrQvtGI0LXQu9GM0LrQsFxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxNTAwKSk7XG4gICAgc2V0V2FsbGV0Q29ubmVjdGVkKHRydWUpO1xuICAgIG9uQ29ubmVjdFdhbGxldCgpO1xuICB9O1xuXG4gIGNvbnN0IGdldFJhcml0eUNvbG9yID0gKHJhcml0eTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChyYXJpdHkpIHtcbiAgICAgIGNhc2UgJ2xlZ2VuZGFyeSc6IHJldHVybiAnI2ZmZDcwMCc7XG4gICAgICBjYXNlICdlcGljJzogcmV0dXJuICcjOTM3MGRiJztcbiAgICAgIGNhc2UgJ3JhcmUnOiByZXR1cm4gJyM0YTkwZTInO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICcjNmI3MjgwJztcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8V2ViM0NvbnRhaW5lcj5cbiAgICAgIDxDb250ZW50V3JhcHBlcj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDUwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgID5cbiAgICAgICAgICA8U2VjdGlvblRpdGxlPldlYjMg0K3QutC+0YHQuNGB0YLQtdC80LA8L1NlY3Rpb25UaXRsZT5cbiAgICAgICAgICA8U2VjdGlvblN1YnRpdGxlPlxuICAgICAgICAgICAg0JTQtdGG0LXQvdGC0YDQsNC70LjQt9C+0LLQsNC90L3QvtC1INCx0YPQtNGD0YnQtdC1INC60LDRgNGC0L7Rh9C90YvRhSDQuNCz0YBcbiAgICAgICAgICA8L1NlY3Rpb25TdWJ0aXRsZT5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHshd2FsbGV0Q29ubmVjdGVkID8gKFxuICAgICAgICAgIDxXYWxsZXRDb25uZWN0aW9uPlxuICAgICAgICAgICAgPENvbm5lY3Rpb25DYXJkXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q29ubmVjdGlvbkljb24+8J+UlzwvQ29ubmVjdGlvbkljb24+XG4gICAgICAgICAgICAgIDxDb25uZWN0aW9uVGl0bGU+0J/QvtC00LrQu9GO0YfQuNGC0LUg0LrQvtGI0LXQu9GR0Lo8L0Nvbm5lY3Rpb25UaXRsZT5cbiAgICAgICAgICAgICAgPENvbm5lY3Rpb25EZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICDQn9C+0LTQutC70Y7Rh9C40YLQtSBXZWIzINC60L7RiNC10LvRkdC6INC00LvRjyDQtNC+0YHRgtGD0L/QsCDQuiBORlQsIERlRmkg0LggREFPINGE0YPQvdC60YbQuNGP0LxcbiAgICAgICAgICAgICAgPC9Db25uZWN0aW9uRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8V2FsbGV0T3B0aW9ucz5cbiAgICAgICAgICAgICAgICA8V2FsbGV0T3B0aW9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb25uZWN0V2FsbGV0fVxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8V2FsbGV0SWNvbj7wn6aKPC9XYWxsZXRJY29uPlxuICAgICAgICAgICAgICAgICAgPFdhbGxldE5hbWU+TWV0YU1hc2s8L1dhbGxldE5hbWU+XG4gICAgICAgICAgICAgICAgPC9XYWxsZXRPcHRpb24+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPFdhbGxldE9wdGlvblxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wNSB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8V2FsbGV0SWNvbj7wn4yIPC9XYWxsZXRJY29uPlxuICAgICAgICAgICAgICAgICAgPFdhbGxldE5hbWU+UmFpbmJvdzwvV2FsbGV0TmFtZT5cbiAgICAgICAgICAgICAgICA8L1dhbGxldE9wdGlvbj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8V2FsbGV0T3B0aW9uXG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxXYWxsZXRJY29uPvCfkpk8L1dhbGxldEljb24+XG4gICAgICAgICAgICAgICAgICA8V2FsbGV0TmFtZT5Db2luYmFzZTwvV2FsbGV0TmFtZT5cbiAgICAgICAgICAgICAgICA8L1dhbGxldE9wdGlvbj5cbiAgICAgICAgICAgICAgPC9XYWxsZXRPcHRpb25zPlxuICAgICAgICAgICAgPC9Db25uZWN0aW9uQ2FyZD5cbiAgICAgICAgICA8L1dhbGxldENvbm5lY3Rpb24+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPERhc2hib2FyZENvbnRlbnQ+XG4gICAgICAgICAgICB7Lyog0JfQsNCz0L7Qu9C+0LLQvtC6INGBINCw0LTRgNC10YHQvtC8ICovfVxuICAgICAgICAgICAgPERhc2hib2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPFVzZXJJbmZvPlxuICAgICAgICAgICAgICAgIDxVc2VyQXZhdGFyPvCfkaQ8L1VzZXJBdmF0YXI+XG4gICAgICAgICAgICAgICAgPFVzZXJEZXRhaWxzPlxuICAgICAgICAgICAgICAgICAgPFVzZXJBZGRyZXNzPnt1c2VyQWRkcmVzcy5zbGljZSgwLCA2KX0uLi57dXNlckFkZHJlc3Muc2xpY2UoLTQpfTwvVXNlckFkZHJlc3M+XG4gICAgICAgICAgICAgICAgICA8UG9ydGZvbGlvVmFsdWU+JHt0b3RhbFBvcnRmb2xpb1ZhbHVlLnRvTG9jYWxlU3RyaW5nKCl9PC9Qb3J0Zm9saW9WYWx1ZT5cbiAgICAgICAgICAgICAgICA8L1VzZXJEZXRhaWxzPlxuICAgICAgICAgICAgICA8L1VzZXJJbmZvPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPE5ldHdvcmtJbmZvPlxuICAgICAgICAgICAgICAgIDxOZXR3b3JrSW5kaWNhdG9yIC8+XG4gICAgICAgICAgICAgICAgPE5ldHdvcmtOYW1lPkV0aGVyZXVtIE1haW5uZXQ8L05ldHdvcmtOYW1lPlxuICAgICAgICAgICAgICA8L05ldHdvcmtJbmZvPlxuICAgICAgICAgICAgPC9EYXNoYm9hcmRIZWFkZXI+XG5cbiAgICAgICAgICAgIHsvKiDQndCw0LLQuNCz0LDRhtC40Y8g0L/QviDRgtCw0LHQsNC8ICovfVxuICAgICAgICAgICAgPFRhYk5hdmlnYXRpb24+XG4gICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgeyBpZDogJ3dhbGxldCcsIGxhYmVsOiAn0JrQvtGI0LXQu9GR0LonLCBpY29uOiAn8J+SsCcgfSxcbiAgICAgICAgICAgICAgICB7IGlkOiAnbmZ0JywgbGFiZWw6ICdORlQnLCBpY29uOiAn8J+OtCcgfSxcbiAgICAgICAgICAgICAgICB7IGlkOiAnZGVmaScsIGxhYmVsOiAnRGVGaScsIGljb246ICfwn4+mJyB9LFxuICAgICAgICAgICAgICAgIHsgaWQ6ICdkYW8nLCBsYWJlbDogJ0RBTycsIGljb246ICfwn5ez77iPJyB9XG4gICAgICAgICAgICAgIF0ubWFwKHRhYiA9PiAoXG4gICAgICAgICAgICAgICAgPFRhYkJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgICBhY3RpdmU9e2FjdGl2ZVRhYiA9PT0gdGFiLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCBhcyBhbnkpfVxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VGFiSWNvbj57dGFiLmljb259PC9UYWJJY29uPlxuICAgICAgICAgICAgICAgICAgPFRhYkxhYmVsPnt0YWIubGFiZWx9PC9UYWJMYWJlbD5cbiAgICAgICAgICAgICAgICA8L1RhYkJ1dHRvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1RhYk5hdmlnYXRpb24+XG5cbiAgICAgICAgICAgIHsvKiDQmtC+0L3RgtC10L3RgiDRgtCw0LHQvtCyICovfVxuICAgICAgICAgICAgPFRhYkNvbnRlbnQ+XG4gICAgICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIj5cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnd2FsbGV0JyAmJiAoXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9XCJ3YWxsZXRcIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFRva2VuR3JpZD5cbiAgICAgICAgICAgICAgICAgICAgICB7dG9rZW5CYWxhbmNlcy5tYXAodG9rZW4gPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFRva2VuQ2FyZCBrZXk9e3Rva2VuLnN5bWJvbH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2tlbkhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9rZW5TeW1ib2w+e3Rva2VuLnN5bWJvbH08L1Rva2VuU3ltYm9sPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2tlbkNoYW5nZSBwb3NpdGl2ZT17dG9rZW4uY2hhbmdlMjRoID4gMH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dG9rZW4uY2hhbmdlMjRoID4gMCA/ICfihpcnIDogJ+KGmCd9IHtNYXRoLmFicyh0b2tlbi5jaGFuZ2UyNGgpfSVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rva2VuQ2hhbmdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rva2VuSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9rZW5CYWxhbmNlPnt0b2tlbi5iYWxhbmNlLnRvTG9jYWxlU3RyaW5nKCl9PC9Ub2tlbkJhbGFuY2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2tlblZhbHVlPiR7dG9rZW4udmFsdWUudG9Mb2NhbGVTdHJpbmcoKX08L1Rva2VuVmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1Rva2VuQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9Ub2tlbkdyaWQ+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICduZnQnICYmIChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT1cIm5mdFwiXG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogNTAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeDogLTUwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8TkZUR3JpZD5cbiAgICAgICAgICAgICAgICAgICAgICB7bmZ0Q29sbGVjdGlvbi5tYXAobmZ0ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxORlRDYXJkIGtleT17bmZ0LmlkfSByYXJpdHk9e25mdC5yYXJpdHl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TkZUSW1hZ2U+e25mdC5pbWFnZX08L05GVEltYWdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TkZUSW5mbz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TkZUTmFtZT57bmZ0Lm5hbWV9PC9ORlROYW1lPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxORlRSYXJpdHkgcmFyaXR5PXtuZnQucmFyaXR5fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtuZnQucmFyaXR5LnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9ORlRSYXJpdHk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE5GVFBvd2VyPuKaoSB7bmZ0LnBvd2VyfTwvTkZUUG93ZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE5GVFByaWNlPntuZnQucHJpY2V9IEVUSDwvTkZUUHJpY2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTkZUSW5mbz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge25mdC5pc1N0YWtlZCAmJiA8U3Rha2VkQmFkZ2U+U1RBS0VEPC9TdGFrZWRCYWRnZT59XG4gICAgICAgICAgICAgICAgICAgICAgICA8L05GVENhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvTkZUR3JpZD5cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2RlZmknICYmIChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT1cImRlZmlcIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPERlRmlHcmlkPlxuICAgICAgICAgICAgICAgICAgICAgIHtkZWZpUG9vbHMubWFwKHBvb2wgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xDYXJkIGtleT17cG9vbC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sTmFtZT57cG9vbC5uYW1lfTwvUG9vbE5hbWU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xBUFI+e3Bvb2wuYXByfSUgQVBSPC9Qb29sQVBSPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Bvb2xIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sU3RhdHM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xTdGF0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xTdGF0TGFiZWw+VFZMPC9Qb29sU3RhdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xTdGF0VmFsdWU+JHsocG9vbC50dmwgLyAxMDAwMDAwKS50b0ZpeGVkKDEpfU08L1Bvb2xTdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb29sU3RhdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9vbFN0YXQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9vbFN0YXRMYWJlbD7QktCw0Ygg0YHRgtC10LnQujwvUG9vbFN0YXRMYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sU3RhdFZhbHVlPiR7cG9vbC51c2VyU3Rha2VkfTwvUG9vbFN0YXRWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Bvb2xTdGF0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sU3RhdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sU3RhdExhYmVsPtCd0LDQs9GA0LDQtNGLPC9Qb29sU3RhdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvb2xTdGF0VmFsdWU+e3Bvb2wucmV3YXJkc30gS09aWVI8L1Bvb2xTdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb29sU3RhdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb29sU3RhdHM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sQWN0aW9ucz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9vbEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDQlNC+0LHQsNCy0LjRgtGMXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb29sQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb29sQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRhcnlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg0JfQsNCx0YDQsNGC0YxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Bvb2xCdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvUG9vbEFjdGlvbnM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1Bvb2xDYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L0RlRmlHcmlkPlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnZGFvJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9XCJkYW9cIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPERBT1NlY3Rpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPERBT1N0YXRzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERBT1N0YXQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEQU9TdGF0VmFsdWU+MTUsNDIwPC9EQU9TdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEQU9TdGF0TGFiZWw+0JLQsNGI0LAgdm90aW5nIHBvd2VyPC9EQU9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RBT1N0YXQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8REFPU3RhdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERBT1N0YXRWYWx1ZT43PC9EQU9TdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEQU9TdGF0TGFiZWw+0JDQutGC0LjQstC90YvRhSDQv9GA0LXQtNC70L7QttC10L3QuNC5PC9EQU9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RBT1N0YXQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8REFPU3RhdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERBT1N0YXRWYWx1ZT44OSU8L0RBT1N0YXRWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERBT1N0YXRMYWJlbD7Qo9GH0LDRgdGC0LjQtSDQsiDQs9C+0LvQvtGB0L7QstCw0L3QuNC4PC9EQU9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RBT1N0YXQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9EQU9TdGF0cz5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8UHJvcG9zYWxzTGlzdD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQcm9wb3NhbENhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxQcm9wb3NhbFRpdGxlPtCU0L7QsdCw0LLQuNGC0Ywg0L3QvtCy0YPRjiDQuNCz0YDRgzog0JHQu9GN0LrQtNC20LXQujwvUHJvcG9zYWxUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFByb3Bvc2FsRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg0J/RgNC10LTQu9C+0LbQtdC90LjQtSDQviDQtNC+0LHQsNCy0LvQtdC90LjQuCDQsdC70Y3QutC00LbQtdC60LAg0LIg0L/Qu9Cw0YLRhNC+0YDQvNGDXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvUHJvcG9zYWxEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFByb3Bvc2FsVm90ZXM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZvdGVPcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Vm90ZUxhYmVsPtCX0LA6IDg1JTwvVm90ZUxhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZvdGVCYXIgd2lkdGg9ezg1fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVm90ZU9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Vm90ZU9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWb3RlTGFiZWw+0J/RgNC+0YLQuNCyOiAxNSU8L1ZvdGVMYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWb3RlQmFyIHdpZHRoPXsxNX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1ZvdGVPcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvUHJvcG9zYWxWb3Rlcz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFByb3Bvc2FsQWN0aW9ucz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Vm90ZUJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDQk9C+0LvQvtGB0L7QstCw0YLRjCDQl9CQXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Wb3RlQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWb3RlQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRhcnlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk4IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg0JPQvtC70L7RgdC+0LLQsNGC0Ywg0J/QoNCe0KLQmNCSXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Wb3RlQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Byb3Bvc2FsQWN0aW9ucz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvUHJvcG9zYWxDYXJkPlxuICAgICAgICAgICAgICAgICAgICAgIDwvUHJvcG9zYWxzTGlzdD5cbiAgICAgICAgICAgICAgICAgICAgPC9EQU9TZWN0aW9uPlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAgPC9UYWJDb250ZW50PlxuICAgICAgICAgIDwvRGFzaGJvYXJkQ29udGVudD5cbiAgICAgICAgKX1cbiAgICAgIDwvQ29udGVudFdyYXBwZXI+XG4gICAgPC9XZWIzQ29udGFpbmVyPlxuICApO1xufTtcblxuLy8g0KHRgtC40LvQuNC30L7QstCw0L3QvdGL0LUg0LrQvtC80L/QvtC90LXQvdGC0YsgKNC/0LXRgNCy0LDRjyDRh9Cw0YHRgtGMKVxuY29uc3QgV2ViM0NvbnRhaW5lciA9IHN0eWxlZC5kaXZgXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTYyMTNlIDAlLCAjMGYwZjIzIDUwJSwgIzFhMWEyZSAxMDAlKTtcbiAgcGFkZGluZzogNHJlbSAycmVtO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbmA7XG5cbmNvbnN0IENvbnRlbnRXcmFwcGVyID0gc3R5bGVkLmRpdmBcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIHdpZHRoOiAxMDAlO1xuYDtcblxuY29uc3QgU2VjdGlvblRpdGxlID0gc3R5bGVkLmgyYFxuICBmb250LXNpemU6IDMuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDkwMDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM0YTkwZTIsICM3YjY4ZWUsICM5MzcwZGIpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBcbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgZm9udC1zaXplOiAyLjVyZW07XG4gIH1cbmA7XG5cbmNvbnN0IFNlY3Rpb25TdWJ0aXRsZSA9IHN0eWxlZC5wYFxuICBmb250LXNpemU6IDEuM3JlbTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiA0cmVtO1xuICBtYXgtd2lkdGg6IDgwMHB4O1xuICBtYXJnaW4tbGVmdDogYXV0bztcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xuYDtcblxuY29uc3QgV2FsbGV0Q29ubmVjdGlvbiA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtaW4taGVpZ2h0OiA0MDBweDtcbmA7XG5cbmNvbnN0IENvbm5lY3Rpb25DYXJkID0gc3R5bGVkKG1vdGlvbi5kaXYpYFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBwYWRkaW5nOiAzcmVtO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBtYXgtd2lkdGg6IDUwMHB4O1xuICB3aWR0aDogMTAwJTtcbmA7XG5cbmNvbnN0IENvbm5lY3Rpb25JY29uID0gc3R5bGVkLmRpdmBcbiAgZm9udC1zaXplOiA0cmVtO1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5gO1xuXG5jb25zdCBDb25uZWN0aW9uVGl0bGUgPSBzdHlsZWQuaDNgXG4gIGZvbnQtc2l6ZTogMnJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6IHdoaXRlO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuYDtcblxuY29uc3QgQ29ubmVjdGlvbkRlc2NyaXB0aW9uID0gc3R5bGVkLnBgXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjY7XG5gO1xuXG5jb25zdCBXYWxsZXRPcHRpb25zID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTtcbiAgZ2FwOiAxcmVtO1xuICBcbiAgQG1lZGlhIChtYXgtd2lkdGg6IDY0MHB4KSB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIH1cbmA7XG5cbmNvbnN0IFdhbGxldE9wdGlvbiA9IHN0eWxlZChtb3Rpb24uYnV0dG9uKWBcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBwYWRkaW5nOiAxLjVyZW0gMXJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBcbiAgJjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICAgIGJvcmRlci1jb2xvcjogIzRhOTBlMjtcbiAgfVxuYDtcblxuY29uc3QgV2FsbGV0SWNvbiA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogMnJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgV2FsbGV0TmFtZSA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG5gO1xuXG4vLyDQodGC0LjQu9C40LfQvtCy0LDQvdC90YvQtSDQutC+0LzQv9C+0L3QtdC90YLRiyAo0L/RgNC+0LTQvtC70LbQtdC90LjQtSlcbmNvbnN0IERhc2hib2FyZENvbnRlbnQgPSBzdHlsZWQuZGl2YGA7XG5cbmNvbnN0IERhc2hib2FyZEhlYWRlciA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgcGFkZGluZzogMS41cmVtO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbmA7XG5cbmNvbnN0IFVzZXJJbmZvID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxcmVtO1xuYDtcblxuY29uc3QgVXNlckF2YXRhciA9IHN0eWxlZC5kaXZgXG4gIHdpZHRoOiA1MHB4O1xuICBoZWlnaHQ6IDUwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0YTkwZTIsICM3YjY4ZWUpO1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmb250LXNpemU6IDEuNXJlbTtcbmA7XG5cbmNvbnN0IFVzZXJEZXRhaWxzID0gc3R5bGVkLmRpdmBgO1xuXG5jb25zdCBVc2VyQWRkcmVzcyA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAxLjFyZW07XG5gO1xuXG5jb25zdCBQb3J0Zm9saW9WYWx1ZSA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiAjNGFkZTgwO1xuICBmb250LXdlaWdodDogNzAwO1xuICBmb250LXNpemU6IDEuM3JlbTtcbmA7XG5cbmNvbnN0IE5ldHdvcmtJbmZvID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG5gO1xuXG5jb25zdCBOZXR3b3JrSW5kaWNhdG9yID0gc3R5bGVkLmRpdmBcbiAgd2lkdGg6IDEycHg7XG4gIGhlaWdodDogMTJweDtcbiAgYmFja2dyb3VuZDogIzRhZGU4MDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuYDtcblxuY29uc3QgTmV0d29ya05hbWUgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IFRhYk5hdmlnYXRpb24gPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgcGFkZGluZzogMC41cmVtO1xuYDtcblxuY29uc3QgVGFiQnV0dG9uID0gc3R5bGVkKG1vdGlvbi5idXR0b24pPHsgYWN0aXZlOiBib29sZWFuIH0+YFxuICBmbGV4OiAxO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG4gIHBhZGRpbmc6IDFyZW07XG4gIGJvcmRlcjogbm9uZTtcbiAgYmFja2dyb3VuZDogJHtwcm9wcyA9PiBwcm9wcy5hY3RpdmUgPyAncmdiYSg3NCwgMTQ0LCAyMjYsIDAuMyknIDogJ3RyYW5zcGFyZW50J307XG4gIGNvbG9yOiAke3Byb3BzID0+IHByb3BzLmFjdGl2ZSA/ICcjNGE5MGUyJyA6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyknfTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBmb250LXdlaWdodDogNjAwO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoNzQsIDE0NCwgMjI2LCAwLjEpO1xuICAgIGNvbG9yOiAjNGE5MGUyO1xuICB9XG5gO1xuXG5jb25zdCBUYWJJY29uID0gc3R5bGVkLnNwYW5gXG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuYDtcblxuY29uc3QgVGFiTGFiZWwgPSBzdHlsZWQuc3BhbmBcbiAgZm9udC1zaXplOiAwLjlyZW07XG5gO1xuXG5jb25zdCBUYWJDb250ZW50ID0gc3R5bGVkLmRpdmBcbiAgbWluLWhlaWdodDogNDAwcHg7XG5gO1xuXG5jb25zdCBUb2tlbkdyaWQgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgZ2FwOiAxLjVyZW07XG5gO1xuXG5jb25zdCBUb2tlbkNhcmQgPSBzdHlsZWQuZGl2YFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbmA7XG5cbmNvbnN0IFRva2VuSGVhZGVyID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuYDtcblxuY29uc3QgVG9rZW5TeW1ib2wgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuYDtcblxuY29uc3QgVG9rZW5DaGFuZ2UgPSBzdHlsZWQuZGl2PHsgcG9zaXRpdmU6IGJvb2xlYW4gfT5gXG4gIGNvbG9yOiAke3Byb3BzID0+IHByb3BzLnBvc2l0aXZlID8gJyM0YWRlODAnIDogJyNlZjQ0NDQnfTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG5gO1xuXG5jb25zdCBUb2tlbkJhbGFuY2UgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5gO1xuXG5jb25zdCBUb2tlblZhbHVlID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgZm9udC1zaXplOiAxcmVtO1xuYDtcblxuY29uc3QgTkZUR3JpZCA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpO1xuICBnYXA6IDEuNXJlbTtcbmA7XG5cbmNvbnN0IE5GVENhcmQgPSBzdHlsZWQuZGl2PHsgcmFyaXR5OiBzdHJpbmcgfT5gXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgcGFkZGluZzogMS41cmVtO1xuICBib3JkZXI6IDJweCBzb2xpZCAke3Byb3BzID0+IGdldFJhcml0eUNvbG9yKHByb3BzLnJhcml0eSl9O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gICY6OmJlZm9yZSB7XG4gICAgY29udGVudDogJyc7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogMDtcbiAgICBsZWZ0OiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGJvdHRvbTogMDtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke3Byb3BzID0+IGdldFJhcml0eUNvbG9yKHByb3BzLnJhcml0eSl9MjAsIHRyYW5zcGFyZW50KTtcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcbiAgfVxuYDtcblxuY29uc3QgTkZUSW1hZ2UgPSBzdHlsZWQuZGl2YFxuICBmb250LXNpemU6IDRyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDAgMjBweCBjdXJyZW50Q29sb3IpO1xuYDtcblxuY29uc3QgTkZUSW5mbyA9IHN0eWxlZC5kaXZgXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbmA7XG5cbmNvbnN0IE5GVE5hbWUgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5gO1xuXG5jb25zdCBORlRSYXJpdHkgPSBzdHlsZWQuZGl2PHsgcmFyaXR5OiBzdHJpbmcgfT5gXG4gIGNvbG9yOiAke3Byb3BzID0+IGdldFJhcml0eUNvbG9yKHByb3BzLnJhcml0eSl9O1xuICBmb250LXdlaWdodDogNjAwO1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgTkZUUG93ZXIgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogI2ZiYmYyNDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgTkZUUHJpY2UgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IFN0YWtlZEJhZGdlID0gc3R5bGVkLmRpdmBcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDAuNXJlbTtcbiAgcmlnaHQ6IDAuNXJlbTtcbiAgYmFja2dyb3VuZDogIzRhZGU4MDtcbiAgY29sb3I6IGJsYWNrO1xuICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcbiAgYm9yZGVyLXJhZGl1czogNXB4O1xuICBmb250LXNpemU6IDAuN3JlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbmA7XG5cbmNvbnN0IERlRmlHcmlkID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gIGdhcDogMS41cmVtO1xuYDtcblxuY29uc3QgUG9vbENhcmQgPSBzdHlsZWQuZGl2YFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBwYWRkaW5nOiAxLjVyZW07XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbmA7XG5cbmNvbnN0IFBvb2xIZWFkZXIgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbmA7XG5cbmNvbnN0IFBvb2xOYW1lID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXdlaWdodDogNzAwO1xuICBmb250LXNpemU6IDEuMnJlbTtcbmA7XG5cbmNvbnN0IFBvb2xBUFIgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogIzRhZGU4MDtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgZm9udC1zaXplOiAxLjFyZW07XG5gO1xuXG5jb25zdCBQb29sU3RhdHMgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCAxZnIpO1xuICBnYXA6IDFyZW07XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbmA7XG5cbmNvbnN0IFBvb2xTdGF0ID0gc3R5bGVkLmRpdmBcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuYDtcblxuY29uc3QgUG9vbFN0YXRMYWJlbCA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNik7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuYDtcblxuY29uc3QgUG9vbFN0YXRWYWx1ZSA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbmA7XG5cbmNvbnN0IFBvb2xBY3Rpb25zID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxcmVtO1xuYDtcblxuY29uc3QgUG9vbEJ1dHRvbiA9IHN0eWxlZChtb3Rpb24uYnV0dG9uKTx7IHNlY29uZGFyeT86IGJvb2xlYW4gfT5gXG4gIGZsZXg6IDE7XG4gIGJhY2tncm91bmQ6ICR7cHJvcHMgPT4gcHJvcHMuc2Vjb25kYXJ5XG4gICAgPyAndHJhbnNwYXJlbnQnXG4gICAgOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRhOTBlMiwgIzdiNjhlZSknfTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXI6ICR7cHJvcHMgPT4gcHJvcHMuc2Vjb25kYXJ5ID8gJzFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyknIDogJ25vbmUnfTtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBwYWRkaW5nOiAwLjc1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjdXJzb3I6IHBvaW50ZXI7XG5gO1xuXG5jb25zdCBEQU9TZWN0aW9uID0gc3R5bGVkLmRpdmBgO1xuXG5jb25zdCBEQU9TdGF0cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7XG4gIGdhcDogMS41cmVtO1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xuYDtcblxuY29uc3QgREFPU3RhdCA9IHN0eWxlZC5kaXZgXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG5gO1xuXG5jb25zdCBEQU9TdGF0VmFsdWUgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogIzRhOTBlMjtcbiAgZm9udC1zaXplOiAycmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5gO1xuXG5jb25zdCBEQU9TdGF0TGFiZWwgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IFByb3Bvc2Fsc0xpc3QgPSBzdHlsZWQuZGl2YGA7XG5cbmNvbnN0IFByb3Bvc2FsQ2FyZCA9IHN0eWxlZC5kaXZgXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuYDtcblxuY29uc3QgUHJvcG9zYWxUaXRsZSA9IHN0eWxlZC5oNGBcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgUHJvcG9zYWxEZXNjcmlwdGlvbiA9IHN0eWxlZC5wYFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjU7XG5gO1xuXG5jb25zdCBQcm9wb3NhbFZvdGVzID0gc3R5bGVkLmRpdmBcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuYDtcblxuY29uc3QgVm90ZU9wdGlvbiA9IHN0eWxlZC5kaXZgXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG5gO1xuXG5jb25zdCBWb3RlTGFiZWwgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5gO1xuXG5jb25zdCBWb3RlQmFyID0gc3R5bGVkLmRpdjx7IHdpZHRoOiBudW1iZXIgfT5gXG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDhweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgJjo6YWZ0ZXIge1xuICAgIGNvbnRlbnQ6ICcnO1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIHdpZHRoOiAke3Byb3BzID0+IHByb3BzLndpZHRofSU7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzRhOTBlMiwgIzdiNjhlZSk7XG4gICAgdHJhbnNpdGlvbjogd2lkdGggMC44cyBlYXNlO1xuICB9XG5gO1xuXG5jb25zdCBQcm9wb3NhbEFjdGlvbnMgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDFyZW07XG5gO1xuXG5jb25zdCBWb3RlQnV0dG9uID0gc3R5bGVkKG1vdGlvbi5idXR0b24pPHsgc2Vjb25kYXJ5PzogYm9vbGVhbiB9PmBcbiAgZmxleDogMTtcbiAgYmFja2dyb3VuZDogJHtwcm9wcyA9PiBwcm9wcy5zZWNvbmRhcnlcbiAgICA/ICd0cmFuc3BhcmVudCdcbiAgICA6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNGE5MGUyLCAjN2I2OGVlKSd9O1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogJHtwcm9wcyA9PiBwcm9wcy5zZWNvbmRhcnkgPyAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKScgOiAnbm9uZSd9O1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHBhZGRpbmc6IDAuNzVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGN1cnNvcjogcG9pbnRlcjtcbmA7XG5cbi8vINCS0YHQv9C+0LzQvtCz0LDRgtC10LvRjNC90LDRjyDRhNGD0L3QutGG0LjRj1xuY29uc3QgZ2V0UmFyaXR5Q29sb3IgPSAocmFyaXR5OiBzdHJpbmcpID0+IHtcbiAgc3dpdGNoIChyYXJpdHkpIHtcbiAgICBjYXNlICdsZWdlbmRhcnknOiByZXR1cm4gJyNmZmQ3MDAnO1xuICAgIGNhc2UgJ2VwaWMnOiByZXR1cm4gJyM5MzcwZGInO1xuICAgIGNhc2UgJ3JhcmUnOiByZXR1cm4gJyM0YTkwZTInO1xuICAgIGRlZmF1bHQ6IHJldHVybiAnIzZiNzI4MCc7XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IFdlYjNEYXNoYm9hcmQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsInN0eWxlZCIsImpzeCIsIl9qc3giLCJqc3hzIiwiX2pzeHMiLCJXZWIzRGFzaGJvYXJkIiwid2ViM1N0YXR1cyIsIm9uQ29ubmVjdFdhbGxldCIsIl9zIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwid2FsbGV0Q29ubmVjdGVkIiwic2V0V2FsbGV0Q29ubmVjdGVkIiwidXNlckFkZHJlc3MiLCJzZXRVc2VyQWRkcmVzcyIsInRva2VuQmFsYW5jZXMiLCJzZXRUb2tlbkJhbGFuY2VzIiwibmZ0Q29sbGVjdGlvbiIsInNldE5mdENvbGxlY3Rpb24iLCJkZWZpUG9vbHMiLCJzZXREZWZpUG9vbHMiLCJ0b3RhbFBvcnRmb2xpb1ZhbHVlIiwic2V0VG90YWxQb3J0Zm9saW9WYWx1ZSIsInN5bWJvbCIsImJhbGFuY2UiLCJ2YWx1ZSIsImNoYW5nZTI0aCIsImlkIiwibmFtZSIsImltYWdlIiwicmFyaXR5IiwicG93ZXIiLCJwcmljZSIsImlzU3Rha2VkIiwiYXByIiwidHZsIiwidXNlclN0YWtlZCIsInJld2FyZHMiLCJjb25uZWN0V2FsbGV0IiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZ2V0UmFyaXR5Q29sb3IiLCJXZWIzQ29udGFpbmVyIiwiY2hpbGRyZW4iLCJDb250ZW50V3JhcHBlciIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJTZWN0aW9uVGl0bGUiLCJTZWN0aW9uU3VidGl0bGUiLCJXYWxsZXRDb25uZWN0aW9uIiwiQ29ubmVjdGlvbkNhcmQiLCJzY2FsZSIsIkNvbm5lY3Rpb25JY29uIiwiQ29ubmVjdGlvblRpdGxlIiwiQ29ubmVjdGlvbkRlc2NyaXB0aW9uIiwiV2FsbGV0T3B0aW9ucyIsIldhbGxldE9wdGlvbiIsIm9uQ2xpY2siLCJ3aGlsZUhvdmVyIiwid2hpbGVUYXAiLCJXYWxsZXRJY29uIiwiV2FsbGV0TmFtZSIsIkRhc2hib2FyZENvbnRlbnQiLCJEYXNoYm9hcmRIZWFkZXIiLCJVc2VySW5mbyIsIlVzZXJBdmF0YXIiLCJVc2VyRGV0YWlscyIsIlVzZXJBZGRyZXNzIiwic2xpY2UiLCJQb3J0Zm9saW9WYWx1ZSIsInRvTG9jYWxlU3RyaW5nIiwiTmV0d29ya0luZm8iLCJOZXR3b3JrSW5kaWNhdG9yIiwiTmV0d29ya05hbWUiLCJUYWJOYXZpZ2F0aW9uIiwibGFiZWwiLCJpY29uIiwibWFwIiwidGFiIiwiVGFiQnV0dG9uIiwiYWN0aXZlIiwiVGFiSWNvbiIsIlRhYkxhYmVsIiwiVGFiQ29udGVudCIsIm1vZGUiLCJ4IiwiZXhpdCIsIlRva2VuR3JpZCIsInRva2VuIiwiVG9rZW5DYXJkIiwiVG9rZW5IZWFkZXIiLCJUb2tlblN5bWJvbCIsIlRva2VuQ2hhbmdlIiwicG9zaXRpdmUiLCJNYXRoIiwiYWJzIiwiVG9rZW5CYWxhbmNlIiwiVG9rZW5WYWx1ZSIsIk5GVEdyaWQiLCJuZnQiLCJORlRDYXJkIiwiTkZUSW1hZ2UiLCJORlRJbmZvIiwiTkZUTmFtZSIsIk5GVFJhcml0eSIsInRvVXBwZXJDYXNlIiwiTkZUUG93ZXIiLCJORlRQcmljZSIsIlN0YWtlZEJhZGdlIiwiRGVGaUdyaWQiLCJwb29sIiwiUG9vbENhcmQiLCJQb29sSGVhZGVyIiwiUG9vbE5hbWUiLCJQb29sQVBSIiwiUG9vbFN0YXRzIiwiUG9vbFN0YXQiLCJQb29sU3RhdExhYmVsIiwiUG9vbFN0YXRWYWx1ZSIsInRvRml4ZWQiLCJQb29sQWN0aW9ucyIsIlBvb2xCdXR0b24iLCJzZWNvbmRhcnkiLCJEQU9TZWN0aW9uIiwiREFPU3RhdHMiLCJEQU9TdGF0IiwiREFPU3RhdFZhbHVlIiwiREFPU3RhdExhYmVsIiwiUHJvcG9zYWxzTGlzdCIsIlByb3Bvc2FsQ2FyZCIsIlByb3Bvc2FsVGl0bGUiLCJQcm9wb3NhbERlc2NyaXB0aW9uIiwiUHJvcG9zYWxWb3RlcyIsIlZvdGVPcHRpb24iLCJWb3RlTGFiZWwiLCJWb3RlQmFyIiwid2lkdGgiLCJQcm9wb3NhbEFjdGlvbnMiLCJWb3RlQnV0dG9uIiwiX2MiLCJfYzIiLCJfYzMiLCJoMiIsIl9jNCIsInAiLCJfYzUiLCJfYzYiLCJfYzciLCJfYzgiLCJoMyIsIl9jOSIsIl9jMTAiLCJfYzExIiwiYnV0dG9uIiwiX2MxMiIsIl9jMTMiLCJfYzE0IiwiX2MxNSIsIl9jMTYiLCJfYzE3IiwiX2MxOCIsIl9jMTkiLCJfYzIwIiwiX2MyMSIsIl9jMjIiLCJfYzIzIiwiX2MyNCIsIl9jMjUiLCJwcm9wcyIsIl9jMjYiLCJzcGFuIiwiX2MyNyIsIl9jMjgiLCJfYzI5IiwiX2MzMCIsIl9jMzEiLCJfYzMyIiwiX2MzMyIsIl9jMzQiLCJfYzM1IiwiX2MzNiIsIl9jMzciLCJfYzM4IiwiX2MzOSIsIl9jNDAiLCJfYzQxIiwiX2M0MiIsIl9jNDMiLCJfYzQ0IiwiX2M0NSIsIl9jNDYiLCJfYzQ3IiwiX2M0OCIsIl9jNDkiLCJfYzUwIiwiX2M1MSIsIl9jNTIiLCJfYzUzIiwiX2M1NCIsIl9jNTUiLCJfYzU2IiwiX2M1NyIsIl9jNTgiLCJfYzU5IiwiX2M2MCIsIl9jNjEiLCJfYzYyIiwiX2M2MyIsImg0IiwiX2M2NCIsIl9jNjUiLCJfYzY2IiwiX2M2NyIsIl9jNjgiLCJfYzY5IiwiX2M3MCIsIl9jNzEiLCIkUmVmcmVzaFJlZyQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/Web3Dashboard.tsx\n"));

/***/ })

}]);