"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_RevolutionaryFeatures_tsx"],{

/***/ "./src/components/RevolutionaryFeatures.tsx":
/*!**************************************************!*\
  !*** ./src/components/RevolutionaryFeatures.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst features = [{\n  id: 'quantum',\n  title: 'Квантовый игровой движок',\n  subtitle: 'Истинная случайность из квантовых источников',\n  description: 'Первая в мире игровая платформа, использующая реальные квантовые источники случайности для абсолютной честности игр.',\n  icon: '⚛️',\n  color: '#4a90e2',\n  stats: [{\n    label: 'Энтропия',\n    value: '99.99%'\n  }, {\n    label: 'Квантовых источников',\n    value: '5'\n  }, {\n    label: 'Тестов качества',\n    value: '15+'\n  }],\n  technologies: ['ANU Quantum RNG', 'ID Quantique', 'PicoQuant', 'Quantum Dice', 'NIST Tests'],\n  benefits: ['Абсолютная честность', 'Невозможность предсказания', 'Научная валидация']\n}, {\n  id: 'ai',\n  title: 'Эмоциональный ИИ',\n  subtitle: 'Глубокое понимание каждого игрока',\n  description: 'Революционная система искусственного интеллекта, анализирующая эмоциональное состояние и персонализирующая игровой опыт.',\n  icon: '🧠',\n  color: '#7b68ee',\n  stats: [{\n    label: 'Эмоциональных состояний',\n    value: '8'\n  }, {\n    label: 'Точность анализа',\n    value: '95%'\n  }, {\n    label: 'ML моделей',\n    value: '6'\n  }],\n  technologies: ['TensorFlow', 'OpenAI GPT-4', 'Computer Vision', 'NLP', 'Behavioral Analysis'],\n  benefits: ['Персонализация', 'Предотвращение тильта', 'Адаптивное обучение']\n}, {\n  id: 'metaverse',\n  title: '3D Метавселенная',\n  subtitle: 'Иммерсивные игровые миры',\n  description: 'Полноценная метавселенная с 3D мирами, VR/AR поддержкой и социальными пространствами для революционного игрового опыта.',\n  icon: '🌍',\n  color: '#9370db',\n  stats: [{\n    label: 'Тематических миров',\n    value: '6'\n  }, {\n    label: 'Одновременных игроков',\n    value: '10K+'\n  }, {\n    label: 'VR/AR поддержка',\n    value: '100%'\n  }],\n  technologies: ['Three.js', 'WebXR', 'WebGL', 'Physics Engine', 'Spatial Audio'],\n  benefits: ['Иммерсивность', 'Социальное взаимодействие', 'Новый опыт']\n}, {\n  id: 'security',\n  title: 'Квантовая безопасность',\n  subtitle: 'Непробиваемая защита',\n  description: 'Передовые системы безопасности с квантовым шифрованием, биометрией и ИИ-детекцией угроз.',\n  icon: '🛡️',\n  color: '#ff6b6b',\n  stats: [{\n    label: 'Точность детекции',\n    value: '99.9%'\n  }, {\n    label: 'Типов угроз',\n    value: '6'\n  }, {\n    label: 'Время реакции',\n    value: '<1с'\n  }],\n  technologies: ['Post-Quantum Crypto', 'Zero-Knowledge Proofs', 'Biometrics', 'AI Detection'],\n  benefits: ['Абсолютная защита', 'Приватность', 'Доверие']\n}, {\n  id: 'analytics',\n  title: 'Предиктивная аналитика',\n  subtitle: 'ИИ предсказывает будущее',\n  description: 'Мощная система машинного обучения, предсказывающая игровые события и персонализирующая опыт.',\n  icon: '📊',\n  color: '#4ecdc4',\n  stats: [{\n    label: 'Точность предсказаний',\n    value: '85%'\n  }, {\n    label: 'Анализируемых метрик',\n    value: '100+'\n  }, {\n    label: 'Обновлений в секунду',\n    value: '1000+'\n  }],\n  technologies: ['Machine Learning', 'Real-time Analytics', 'Predictive Models', 'Big Data'],\n  benefits: ['Персонализация', 'Оптимизация', 'Инсайты']\n}, {\n  id: 'web3',\n  title: 'Web3 экосистема',\n  subtitle: 'Децентрализованное будущее',\n  description: 'Полная интеграция с блокчейном, NFT картами, DeFi протоколами и DAO управлением.',\n  icon: '⛓️',\n  color: '#feca57',\n  stats: [{\n    label: 'Смарт-контрактов',\n    value: '6'\n  }, {\n    label: 'Поддерживаемых сетей',\n    value: '5+'\n  }, {\n    label: 'NFT коллекций',\n    value: '10+'\n  }],\n  technologies: ['Ethereum', 'Polygon', 'IPFS', 'Smart Contracts', 'DeFi'],\n  benefits: ['Владение активами', 'Децентрализация', 'Новая экономика']\n}];\nconst RevolutionaryFeatures = ({\n  currentSection\n}) => {\n  _s();\n  const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [hoveredFeature, setHoveredFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const interval = setInterval(() => {\n      setActiveFeature(prev => (prev + 1) % features.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeaturesContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n          children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionSubtitle, {\n          children: \"\\u041C\\u044B \\u043E\\u0431\\u044A\\u0435\\u0434\\u0438\\u043D\\u0438\\u043B\\u0438 \\u043F\\u0435\\u0440\\u0435\\u0434\\u043E\\u0432\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438 \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0433\\u043E \\u0432 \\u043E\\u0434\\u043D\\u043E\\u0439 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0435\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeaturesGrid, {\n        children: features.map((feature, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeatureCard, {\n          active: index === activeFeature,\n          hovered: hoveredFeature === feature.id,\n          color: feature.color,\n          onMouseEnter: () => setHoveredFeature(feature.id),\n          onMouseLeave: () => setHoveredFeature(null),\n          onClick: () => setActiveFeature(index),\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5,\n            delay: index * 0.1\n          },\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureIcon, {\n            active: index === activeFeature,\n            children: feature.icon\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureTitle, {\n            children: feature.title\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureSubtitle, {\n            children: feature.subtitle\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n            children: (index === activeFeature || hoveredFeature === feature.id) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FeatureDetails, {\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              exit: {\n                opacity: 0,\n                height: 0\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FeatureDescription, {\n                children: feature.description\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatsGrid, {\n                children: feature.stats.map((stat, statIndex) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatItem, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                    children: stat.value\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                    children: stat.label\n                  })]\n                }, statIndex))\n              })]\n            })\n          })]\n        }, feature.id))\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DetailedView, {\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -50\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DetailedContent, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(DetailedHeader, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DetailedIcon, {\n                color: features[activeFeature].color,\n                children: features[activeFeature].icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DetailedTitle, {\n                  children: features[activeFeature].title\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DetailedSubtitle, {\n                  children: features[activeFeature].subtitle\n                })]\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DetailedDescription, {\n              children: features[activeFeature].description\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TechnologiesSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionLabel, {\n                children: \"\\u0422\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438:\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechnologiesList, {\n                children: features[activeFeature].technologies.map((tech, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechTag, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  children: tech\n                }, index))\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(BenefitsSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionLabel, {\n                children: \"\\u041F\\u0440\\u0435\\u0438\\u043C\\u0443\\u0449\\u0435\\u0441\\u0442\\u0432\\u0430:\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BenefitsList, {\n                children: features[activeFeature].benefits.map((benefit, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(BenefitItem, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  children: [\"\\u2728 \", benefit]\n                }, index))\n              })]\n            })]\n          })\n        }, activeFeature)\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProgressIndicators, {\n        children: features.map((_, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProgressDot, {\n          active: index === activeFeature,\n          onClick: () => setActiveFeature(index),\n          whileHover: {\n            scale: 1.2\n          },\n          whileTap: {\n            scale: 0.9\n          }\n        }, index))\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\n_s(RevolutionaryFeatures, \"oJYL2UD5jAPoC9xZxSqVom7Ddwc=\");\n_c = RevolutionaryFeatures;\nconst FeaturesContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c2 = FeaturesContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1400px;\n  width: 100%;\n`;\n_c3 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c5 = SectionSubtitle;\nconst FeaturesGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 2rem;\n  margin-bottom: 4rem;\n`;\n_c6 = FeaturesGrid;\nconst FeatureCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: ${props => props.active || props.hovered ? `linear-gradient(135deg, ${props.color}20, ${props.color}10)` : 'rgba(255, 255, 255, 0.05)'};\n  border: 2px solid ${props => props.active || props.hovered ? props.color : 'rgba(255, 255, 255, 0.1)'};\n  border-radius: 20px;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: ${props => `linear-gradient(135deg, ${props.color}10, transparent)`};\n    opacity: ${props => props.active || props.hovered ? 1 : 0};\n    transition: opacity 0.3s ease;\n  }\n`;\n_c7 = FeatureCard;\nconst FeatureIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  filter: ${props => props.active ? 'drop-shadow(0 0 20px currentColor)' : 'none'};\n  transition: all 0.3s ease;\n`;\n_c8 = FeatureIcon;\nconst FeatureTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n_c9 = FeatureTitle;\nconst FeatureSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 1rem;\n`;\n_c10 = FeatureSubtitle;\nconst FeatureDetails = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  position: relative;\n  z-index: 1;\n`;\n_c11 = FeatureDetails;\nconst FeatureDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n`;\n_c12 = FeatureDescription;\nconst StatsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n`;\n_c13 = StatsGrid;\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  text-align: center;\n`;\n_c14 = StatItem;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n`;\n_c15 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n`;\n_c16 = StatLabel;\nconst DetailedView = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 3rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  margin-bottom: 3rem;\n`;\n_c17 = DetailedView;\nconst DetailedContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c18 = DetailedContent;\nconst DetailedHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n`;\n_c19 = DetailedHeader;\nconst DetailedIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 4rem;\n  color: ${props => props.color};\n  filter: drop-shadow(0 0 20px ${props => props.color}50);\n`;\n_c20 = DetailedIcon;\nconst DetailedTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h3`\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\n_c21 = DetailedTitle;\nconst DetailedSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.2rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c22 = DetailedSubtitle;\nconst DetailedDescription = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.8;\n  margin-bottom: 2rem;\n`;\n_c23 = DetailedDescription;\nconst TechnologiesSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  margin-bottom: 2rem;\n`;\n_c24 = TechnologiesSection;\nconst BenefitsSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c25 = BenefitsSection;\nconst SectionLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 1rem;\n`;\n_c26 = SectionLabel;\nconst TechnologiesList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n`;\n_c27 = TechnologiesList;\nconst TechTag = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span)`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n`;\n_c28 = TechTag;\nconst BenefitsList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\n_c29 = BenefitsList;\nconst BenefitItem = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 1rem;\n`;\n_c30 = BenefitItem;\nconst ProgressIndicators = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n`;\n_c31 = ProgressIndicators;\nconst ProgressDot = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n`;\n_c32 = ProgressDot;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RevolutionaryFeatures);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32;\n$RefreshReg$(_c, \"RevolutionaryFeatures\");\n$RefreshReg$(_c2, \"FeaturesContainer\");\n$RefreshReg$(_c3, \"ContentWrapper\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"SectionSubtitle\");\n$RefreshReg$(_c6, \"FeaturesGrid\");\n$RefreshReg$(_c7, \"FeatureCard\");\n$RefreshReg$(_c8, \"FeatureIcon\");\n$RefreshReg$(_c9, \"FeatureTitle\");\n$RefreshReg$(_c10, \"FeatureSubtitle\");\n$RefreshReg$(_c11, \"FeatureDetails\");\n$RefreshReg$(_c12, \"FeatureDescription\");\n$RefreshReg$(_c13, \"StatsGrid\");\n$RefreshReg$(_c14, \"StatItem\");\n$RefreshReg$(_c15, \"StatValue\");\n$RefreshReg$(_c16, \"StatLabel\");\n$RefreshReg$(_c17, \"DetailedView\");\n$RefreshReg$(_c18, \"DetailedContent\");\n$RefreshReg$(_c19, \"DetailedHeader\");\n$RefreshReg$(_c20, \"DetailedIcon\");\n$RefreshReg$(_c21, \"DetailedTitle\");\n$RefreshReg$(_c22, \"DetailedSubtitle\");\n$RefreshReg$(_c23, \"DetailedDescription\");\n$RefreshReg$(_c24, \"TechnologiesSection\");\n$RefreshReg$(_c25, \"BenefitsSection\");\n$RefreshReg$(_c26, \"SectionLabel\");\n$RefreshReg$(_c27, \"TechnologiesList\");\n$RefreshReg$(_c28, \"TechTag\");\n$RefreshReg$(_c29, \"BenefitsList\");\n$RefreshReg$(_c30, \"BenefitItem\");\n$RefreshReg$(_c31, \"ProgressIndicators\");\n$RefreshReg$(_c32, \"ProgressDot\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/RevolutionaryFeatures.tsx\n"));

/***/ })

}]);