"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_Footer_tsx"],{

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst Footer = ({\n  onSubscribe\n}) => {\n  _s();\n  const [email, setEmail] = react__WEBPACK_IMPORTED_MODULE_0___default().useState('');\n  const handleSubscribe = e => {\n    e.preventDefault();\n    if (email && onSubscribe) {\n      onSubscribe(email);\n      setEmail('');\n    }\n  };\n  const socialLinks = [{\n    name: 'Discord',\n    icon: '💬',\n    url: 'https://discord.gg/kozyrmasterr',\n    color: '#5865F2'\n  }, {\n    name: 'Telegram',\n    icon: '📱',\n    url: 'https://t.me/kozyrmasterr',\n    color: '#0088cc'\n  }, {\n    name: 'Twitter',\n    icon: '🐦',\n    url: 'https://twitter.com/kozyrmasterr',\n    color: '#1DA1F2'\n  }, {\n    name: 'YouTube',\n    icon: '📺',\n    url: 'https://youtube.com/@kozyrmasterr',\n    color: '#FF0000'\n  }, {\n    name: 'Twitch',\n    icon: '🎮',\n    url: 'https://twitch.tv/kozyrmasterr',\n    color: '#9146FF'\n  }, {\n    name: 'GitHub',\n    icon: '💻',\n    url: 'https://github.com/kozyrmasterr',\n    color: '#333'\n  }];\n  const quickLinks = [{\n    name: 'Игры',\n    url: '/games'\n  }, {\n    name: 'Турниры',\n    url: '/tournaments'\n  }, {\n    name: 'Обучение',\n    url: '/tutorials'\n  }, {\n    name: 'Рейтинг',\n    url: '/leaderboard'\n  }, {\n    name: 'Профиль',\n    url: '/profile'\n  }, {\n    name: 'Поддержка',\n    url: '/support'\n  }];\n  const legalLinks = [{\n    name: 'Пользовательское соглашение',\n    url: '/terms'\n  }, {\n    name: 'Политика конфиденциальности',\n    url: '/privacy'\n  }, {\n    name: 'Правила игры',\n    url: '/rules'\n  }, {\n    name: 'FAQ',\n    url: '/faq'\n  }];\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FooterContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(FooterContent, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(BrandSection, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(Logo, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LogoIcon, {\n                children: \"\\uD83C\\uDFAE\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(LogoText, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LogoTitle, {\n                  children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LogoVersion, {\n                  children: \"4.0\"\n                })]\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BrandDescription, {\n              children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u0430\\u044F \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440 \\u0441 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E, \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u0418\\u0418 \\u0438 3D \\u043C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0439. \\u0411\\u0443\\u0434\\u0443\\u0449\\u0435\\u0435 \\u0438\\u0433\\u0440 \\u0443\\u0436\\u0435 \\u0437\\u0434\\u0435\\u0441\\u044C!\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TechBadges, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechBadge, {\n                children: \"\\u269B\\uFE0F \\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechBadge, {\n                children: \"\\uD83E\\uDDE0 \\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechBadge, {\n                children: \"\\uD83C\\uDF0D 3D \\u041C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u0430\\u044F\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechBadge, {\n                children: \"\\u26D3\\uFE0F Web3 & NFT\"\n              })]\n            })]\n          })\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.1\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n            children: \"\\u0411\\u044B\\u0441\\u0442\\u0440\\u044B\\u0435 \\u0441\\u0441\\u044B\\u043B\\u043A\\u0438\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LinksList, {\n            children: quickLinks.map((link, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LinkItem, {\n              href: link.url,\n              whileHover: {\n                x: 5,\n                color: '#4a90e2'\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: link.name\n            }, link.name))\n          })]\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n            children: \"\\u0421\\u043E\\u043E\\u0431\\u0449\\u0435\\u0441\\u0442\\u0432\\u043E\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SocialLinks, {\n            children: socialLinks.map((social, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SocialLink, {\n              href: social.url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              color: social.color,\n              whileHover: {\n                scale: 1.1,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              initial: {\n                opacity: 0,\n                scale: 0\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SocialIcon, {\n                children: social.icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SocialName, {\n                children: social.name\n              })]\n            }, social.name))\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(CommunityStats, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                children: \"50K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                children: \"15K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                children: \"Discord\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                children: \"25K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                children: \"\\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432\"\n              })]\n            })]\n          })]\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n            children: \"\\u041D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(NewsletterDescription, {\n            children: \"\\u0411\\u0443\\u0434\\u044C\\u0442\\u0435 \\u0432 \\u043A\\u0443\\u0440\\u0441\\u0435 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u043D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0435\\u0439, \\u0442\\u0443\\u0440\\u043D\\u0438\\u0440\\u043E\\u0432 \\u0438 \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u0439 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u044B\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NewsletterForm, {\n            onSubmit: handleSubscribe,\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(EmailInput, {\n              type: \"email\",\n              placeholder: \"\\u0412\\u0430\\u0448 email \\u0430\\u0434\\u0440\\u0435\\u0441\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              required: true\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SubscribeButton, {\n              type: \"submit\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\uD83D\\uDE80 \\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0430\\u0442\\u044C\\u0441\\u044F\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(NewsletterBenefits, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BenefitItem, {\n              children: \"\\u2728 \\u042D\\u043A\\u0441\\u043A\\u043B\\u044E\\u0437\\u0438\\u0432\\u043D\\u044B\\u0435 \\u0442\\u0443\\u0440\\u043D\\u0438\\u0440\\u044B\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BenefitItem, {\n              children: \"\\uD83C\\uDF81 \\u0411\\u043E\\u043D\\u0443\\u0441\\u044B \\u0438 \\u043F\\u0440\\u043E\\u043C\\u043E\\u043A\\u043E\\u0434\\u044B\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BenefitItem, {\n              children: \"\\uD83D\\uDCF0 \\u041F\\u0435\\u0440\\u0432\\u044B\\u043C\\u0438 \\u0443\\u0437\\u043D\\u0430\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0438\"\n            })]\n          })]\n        })\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FooterBottom, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(BottomContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LegalLinks, {\n          children: legalLinks.map((link, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LegalLink, {\n            href: link.url,\n            whileHover: {\n              color: '#4a90e2'\n            },\n            children: link.name\n          }, link.name))\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(Copyright, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CopyrightText, {\n            children: \"\\xA9 2024 \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0. \\u0412\\u0441\\u0435 \\u043F\\u0440\\u0430\\u0432\\u0430 \\u0437\\u0430\\u0449\\u0438\\u0449\\u0435\\u043D\\u044B.\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechInfo, {\n            children: \"\\u0420\\u0430\\u0431\\u043E\\u0442\\u0430\\u0435\\u0442 \\u043D\\u0430 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0445 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u0445 \\u0438 \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0418\\u0418\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PoweredBy, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PoweredText, {\n            children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u043D\\u043E \\u0441 \\u2764\\uFE0F \\u043A\\u043E\\u043C\\u0430\\u043D\\u0434\\u043E\\u0439 \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TechStack, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechItem, {\n              children: \"React\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechItem, {\n              children: \"Three.js\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechItem, {\n              children: \"Web3\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TechItem, {\n              children: \"AI/ML\"\n            })]\n          })]\n        })]\n      })\n    })]\n  });\n};\n\n// Стилизованные компоненты\n_s(Footer, \"qu4bovk5U4+JuhY7vxbmswqixrc=\");\n_c = Footer;\nconst FooterContainer = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].footer`\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, #4a90e2, transparent);\n  }\n`;\n_c2 = FooterContainer;\nconst FooterContent = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 4rem 2rem 2rem;\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1.5fr;\n  gap: 3rem;\n  \n  @media (max-width: 1024px) {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 2rem;\n  }\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n    padding: 2rem 1rem 1rem;\n  }\n`;\n_c3 = FooterContent;\nconst FooterSection = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div``;\n_c4 = FooterSection;\nconst BrandSection = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div``;\n_c5 = BrandSection;\nconst Logo = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n_c6 = Logo;\nconst LogoIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  font-size: 3rem;\n  filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.5));\n`;\n_c7 = LogoIcon;\nconst LogoText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div``;\n_c8 = LogoText;\nconst LogoTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  font-size: 1.8rem;\n  font-weight: 900;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  line-height: 1;\n`;\n_c9 = LogoTitle;\nconst LogoVersion = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  font-size: 1rem;\n  color: #4a90e2;\n  font-weight: 700;\n`;\n_c10 = LogoVersion;\nconst BrandDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n`;\n_c11 = BrandDescription;\nconst TechBadges = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n`;\n_c12 = TechBadges;\nconst TechBadge = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.3rem 0.8rem;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n`;\n_c13 = TechBadge;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].h4`\n  font-size: 1.3rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: white;\n`;\n_c14 = SectionTitle;\nconst LinksList = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\n_c15 = LinksList;\nconst LinkItem = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a)`\n  color: rgba(255, 255, 255, 0.7);\n  text-decoration: none;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    color: #4a90e2;\n  }\n`;\n_c16 = LinkItem;\nconst SocialLinks = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n`;\n_c17 = SocialLinks;\nconst SocialLink = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  text-decoration: none;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => `${props.color}20`};\n    border-color: ${props => props.color};\n  }\n`;\n_c18 = SocialLink;\nconst SocialIcon = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span`\n  font-size: 1.2rem;\n`;\n_c19 = SocialIcon;\nconst SocialName = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span`\n  font-size: 0.85rem;\n  font-weight: 600;\n`;\n_c20 = SocialName;\nconst CommunityStats = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n`;\n_c21 = CommunityStats;\nconst StatItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 1rem 0.5rem;\n`;\n_c22 = StatItem;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #4a90e2;\n  margin-bottom: 0.25rem;\n`;\n_c23 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c24 = StatLabel;\nconst NewsletterDescription = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].p`\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 1.5rem;\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\n_c25 = NewsletterDescription;\nconst NewsletterForm = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].form`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n`;\n_c26 = NewsletterForm;\nconst EmailInput = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].input`\n  flex: 1;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n    background: rgba(255, 255, 255, 0.15);\n  }\n`;\n_c27 = EmailInput;\nconst SubscribeButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  background: linear-gradient(135deg, #4a90e2, #7b68ee);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  cursor: pointer;\n  white-space: nowrap;\n`;\n_c28 = SubscribeButton;\nconst NewsletterBenefits = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\n_c29 = NewsletterBenefits;\nconst BenefitItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.85rem;\n`;\n_c30 = BenefitItem;\nconst FooterBottom = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 2rem 0;\n`;\n_c31 = FooterBottom;\nconst BottomContent = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: grid;\n  grid-template-columns: 1fr auto 1fr;\n  gap: 2rem;\n  align-items: center;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    text-align: center;\n    gap: 1rem;\n    padding: 0 1rem;\n  }\n`;\n_c32 = BottomContent;\nconst LegalLinks = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1.5rem;\n  \n  @media (max-width: 768px) {\n    justify-content: center;\n  }\n`;\n_c33 = LegalLinks;\nconst LegalLink = (0,styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a)`\n  color: rgba(255, 255, 255, 0.6);\n  text-decoration: none;\n  font-size: 0.8rem;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: #4a90e2;\n  }\n`;\n_c34 = LegalLink;\nconst Copyright = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  text-align: center;\n`;\n_c35 = Copyright;\nconst CopyrightText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  margin-bottom: 0.25rem;\n`;\n_c36 = CopyrightText;\nconst TechInfo = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.75rem;\n`;\n_c37 = TechInfo;\nconst PoweredBy = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  text-align: right;\n  \n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\n_c38 = PoweredBy;\nconst PoweredText = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.85rem;\n  margin-bottom: 0.5rem;\n`;\n_c39 = PoweredText;\nconst TechStack = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].div`\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n  \n  @media (max-width: 768px) {\n    justify-content: center;\n  }\n`;\n_c40 = TechStack;\nconst TechItem = styled_components__WEBPACK_IMPORTED_MODULE_3__[\"default\"].span`\n  background: rgba(74, 144, 226, 0.1);\n  color: #4a90e2;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n_c41 = TechItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Footer);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c2, \"FooterContainer\");\n$RefreshReg$(_c3, \"FooterContent\");\n$RefreshReg$(_c4, \"FooterSection\");\n$RefreshReg$(_c5, \"BrandSection\");\n$RefreshReg$(_c6, \"Logo\");\n$RefreshReg$(_c7, \"LogoIcon\");\n$RefreshReg$(_c8, \"LogoText\");\n$RefreshReg$(_c9, \"LogoTitle\");\n$RefreshReg$(_c10, \"LogoVersion\");\n$RefreshReg$(_c11, \"BrandDescription\");\n$RefreshReg$(_c12, \"TechBadges\");\n$RefreshReg$(_c13, \"TechBadge\");\n$RefreshReg$(_c14, \"SectionTitle\");\n$RefreshReg$(_c15, \"LinksList\");\n$RefreshReg$(_c16, \"LinkItem\");\n$RefreshReg$(_c17, \"SocialLinks\");\n$RefreshReg$(_c18, \"SocialLink\");\n$RefreshReg$(_c19, \"SocialIcon\");\n$RefreshReg$(_c20, \"SocialName\");\n$RefreshReg$(_c21, \"CommunityStats\");\n$RefreshReg$(_c22, \"StatItem\");\n$RefreshReg$(_c23, \"StatValue\");\n$RefreshReg$(_c24, \"StatLabel\");\n$RefreshReg$(_c25, \"NewsletterDescription\");\n$RefreshReg$(_c26, \"NewsletterForm\");\n$RefreshReg$(_c27, \"EmailInput\");\n$RefreshReg$(_c28, \"SubscribeButton\");\n$RefreshReg$(_c29, \"NewsletterBenefits\");\n$RefreshReg$(_c30, \"BenefitItem\");\n$RefreshReg$(_c31, \"FooterBottom\");\n$RefreshReg$(_c32, \"BottomContent\");\n$RefreshReg$(_c33, \"LegalLinks\");\n$RefreshReg$(_c34, \"LegalLink\");\n$RefreshReg$(_c35, \"Copyright\");\n$RefreshReg$(_c36, \"CopyrightText\");\n$RefreshReg$(_c37, \"TechInfo\");\n$RefreshReg$(_c38, \"PoweredBy\");\n$RefreshReg$(_c39, \"PoweredText\");\n$RefreshReg$(_c40, \"TechStack\");\n$RefreshReg$(_c41, \"TechItem\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n"));

/***/ })

}]);