"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src_components_StreamingPlatform_tsx"],{

/***/ "./src/components/StreamingPlatform.tsx":
/*!**********************************************!*\
  !*** ./src/components/StreamingPlatform.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"../../node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"../../node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"../../node_modules/react/jsx-runtime.js\");\nvar _s = $RefreshSig$();\n\n\n\n\nconst StreamingPlatform = ({\n  onStartStreaming\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('browse');\n  const [featuredStreams, setFeaturedStreams] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [streamStats, setStreamStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    viewers: 0,\n    followers: 1247,\n    donations: 156.50,\n    chatMessages: 0\n  });\n  const [streamSettings, setStreamSettings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    title: 'Козырь Мастер - Эпические игры!',\n    quality: '1080p',\n    enableChat: true,\n    enableDonations: true,\n    enableAI: true\n  });\n  const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция популярных стримов\n    setFeaturedStreams([{\n      id: '1',\n      title: 'Турнир по Дураку - Финал!',\n      streamer: 'ProGamer2024',\n      viewers: 2847,\n      game: 'Дурак',\n      thumbnail: '🃏',\n      isLive: true,\n      quality: '1080p',\n      language: 'RU',\n      tags: ['Турнир', 'Финал', 'Профи']\n    }, {\n      id: '2',\n      title: 'Покер с ИИ - Кто умнее?',\n      streamer: 'AIChallenger',\n      viewers: 1523,\n      game: 'Покер',\n      thumbnail: '♠️',\n      isLive: true,\n      quality: '4K',\n      language: 'EN',\n      tags: ['ИИ', 'Покер', 'Вызов']\n    }, {\n      id: '3',\n      title: 'Обучение новичков',\n      streamer: 'CardMaster',\n      viewers: 892,\n      game: 'Дурак',\n      thumbnail: '🎓',\n      isLive: true,\n      quality: '720p',\n      language: 'RU',\n      tags: ['Обучение', 'Новички']\n    }]);\n\n    // Симуляция обновления статистики стрима\n    if (isStreaming) {\n      const interval = setInterval(() => {\n        setStreamStats(prev => ({\n          ...prev,\n          viewers: prev.viewers + Math.floor(Math.random() * 10 - 3),\n          chatMessages: prev.chatMessages + Math.floor(Math.random() * 5),\n          donations: prev.donations + (Math.random() > 0.9 ? Math.random() * 20 : 0)\n        }));\n      }, 3000);\n      return () => clearInterval(interval);\n    }\n  }, [isStreaming]);\n  const startStream = async () => {\n    try {\n      // Запрос доступа к камере и микрофону\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n      setIsStreaming(true);\n      setStreamStats(prev => ({\n        ...prev,\n        viewers: 1\n      }));\n    } catch (error) {\n      console.error('Ошибка доступа к медиа:', error);\n      // Симуляция стрима без реального видео\n      setIsStreaming(true);\n      setStreamStats(prev => ({\n        ...prev,\n        viewers: 1\n      }));\n    }\n  };\n  const stopStream = () => {\n    if (videoRef.current?.srcObject) {\n      const stream = videoRef.current.srcObject;\n      stream.getTracks().forEach(track => track.stop());\n    }\n    setIsStreaming(false);\n    setStreamStats(prev => ({\n      ...prev,\n      viewers: 0\n    }));\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamingContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionTitle, {\n          children: \"\\u0421\\u0442\\u0440\\u0438\\u043C\\u0438\\u043D\\u0433 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionSubtitle, {\n          children: \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u044B \\u0434\\u043B\\u044F \\u0441\\u043E\\u0437\\u0434\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439 \\u043A\\u043E\\u043D\\u0442\\u0435\\u043D\\u0442\\u0430\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabNavigation, {\n        children: [{\n          id: 'browse',\n          label: 'Обзор стримов',\n          icon: '📺'\n        }, {\n          id: 'stream',\n          label: 'Мой стрим',\n          icon: '🎥'\n        }, {\n          id: 'analytics',\n          label: 'Аналитика',\n          icon: '📊'\n        }].map(tab => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(TabButton, {\n          active: activeTab === tab.id,\n          onClick: () => setActiveTab(tab.id),\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabIcon, {\n            children: tab.icon\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabLabel, {\n            children: tab.label\n          })]\n        }, tab.id))\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TabContent, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n          mode: \"wait\",\n          children: [activeTab === 'browse' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(BrowseSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SectionHeader, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SectionSubtitle, {\n                  children: \"\\u041F\\u043E\\u043F\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u044B\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(LiveIndicator, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LiveDot, {}), featuredStreams.length, \" \\u0441\\u0442\\u0440\\u0438\\u043C\\u043E\\u0432 \\u0432 \\u044D\\u0444\\u0438\\u0440\\u0435\"]\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamsGrid, {\n                children: featuredStreams.map(stream => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamCard, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamThumbnail, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ThumbnailIcon, {\n                      children: stream.thumbnail\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamOverlay, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QualityBadge, {\n                        children: stream.quality\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ViewerCount, {\n                        children: [\"\\uD83D\\uDC41\\uFE0F \", stream.viewers.toLocaleString()]\n                      })]\n                    }), stream.isLive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LiveBadge, {\n                      children: \"LIVE\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamInfo, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamTitle, {\n                      children: stream.title\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamMeta, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamerName, {\n                        children: stream.streamer\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamGame, {\n                        children: stream.game\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamTags, {\n                      children: stream.tags.map(tag => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StreamTag, {\n                        children: tag\n                      }, tag))\n                    })]\n                  })]\n                }, stream.id))\n              })]\n            })\n          }, \"browse\"), activeTab === 'stream' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamContainer, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamPreview, {\n                  children: [isStreaming ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VideoPreview, {\n                    ref: videoRef,\n                    autoPlay: true,\n                    muted: true,\n                    playsInline: true\n                  }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(PreviewPlaceholder, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PlaceholderIcon, {\n                      children: \"\\uD83C\\uDFA5\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PlaceholderText, {\n                      children: \"\\u041F\\u0440\\u0435\\u0434\\u043F\\u0440\\u043E\\u0441\\u043C\\u043E\\u0442\\u0440 \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430\"\n                    })]\n                  }), isStreaming && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamOverlays, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamStatus, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatusDot, {}), \"\\u0412 \\u042D\\u0424\\u0418\\u0420\\u0415\"]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ViewerCounter, {\n                      children: [\"\\uD83D\\uDC41\\uFE0F \", streamStats.viewers]\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamControls, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ControlsRow, {\n                    children: [!isStreaming ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StartStreamButton, {\n                      onClick: startStream,\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\uD83D\\uDE80 \\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0441\\u0442\\u0440\\u0438\\u043C\"\n                    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StopStreamButton, {\n                      onClick: stopStream,\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\u23F9\\uFE0F \\u041E\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0438\\u0442\\u044C \\u0441\\u0442\\u0440\\u0438\\u043C\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ControlButton, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\u2699\\uFE0F \\u041D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0439\\u043A\\u0438\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StreamSettings, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SettingGroup, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SettingLabel, {\n                        children: \"\\u041D\\u0430\\u0437\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SettingInput, {\n                        value: streamSettings.title,\n                        onChange: e => setStreamSettings(prev => ({\n                          ...prev,\n                          title: e.target.value\n                        }))\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SettingGroup, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SettingLabel, {\n                        children: \"\\u041A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SettingSelect, {\n                        value: streamSettings.quality,\n                        onChange: e => setStreamSettings(prev => ({\n                          ...prev,\n                          quality: e.target.value\n                        })),\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"option\", {\n                          value: \"720p\",\n                          children: \"720p\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"option\", {\n                          value: \"1080p\",\n                          children: \"1080p\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"option\", {\n                          value: \"4K\",\n                          children: \"4K\"\n                        })]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SettingToggles, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableChat,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableChat: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleLabel, {\n                          children: \"\\u0412\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u044C \\u0447\\u0430\\u0442\"\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableDonations,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableDonations: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleLabel, {\n                          children: \"\\u041F\\u0440\\u0438\\u043D\\u0438\\u043C\\u0430\\u0442\\u044C \\u0434\\u043E\\u043D\\u0430\\u0442\\u044B\"\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableAI,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableAI: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ToggleLabel, {\n                          children: \"\\u0418\\u0418-\\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\"\n                        })]\n                      })]\n                    })]\n                  })]\n                })]\n              }), isStreaming && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(LiveStats, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDC41\\uFE0F\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                    children: streamStats.viewers\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                    children: \"\\u0417\\u0440\\u0438\\u0442\\u0435\\u043B\\u0435\\u0439\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDC65\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                    children: streamStats.followers\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                    children: \"\\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDCB0\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatValue, {\n                    children: [\"$\", streamStats.donations.toFixed(2)]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                    children: \"\\u0414\\u043E\\u043D\\u0430\\u0442\\u043E\\u0432\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDCAC\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatValue, {\n                    children: streamStats.chatMessages\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(StatLabel, {\n                    children: \"\\u0421\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0439\"\n                  })]\n                })]\n              })]\n            })\n          }, \"stream\"), activeTab === 'analytics' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AnalyticsSection, {\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(AnalyticsGrid, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CardTitle, {\n                    children: \"\\u0421\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u043A\\u0430 \\u0437\\u0430 \\u043C\\u0435\\u0441\\u044F\\u0446\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricsList, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n                        children: \"\\u041E\\u0431\\u0449\\u0435\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F \\u0441\\u0442\\u0440\\u0438\\u043C\\u043E\\u0432:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricValue, {\n                        children: \"47\\u0447 23\\u043C\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n                        children: \"\\u0421\\u0440\\u0435\\u0434\\u043D\\u0438\\u0435 \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0438:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricValue, {\n                        children: \"234\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n                        children: \"\\u041F\\u0438\\u043A\\u043E\\u0432\\u044B\\u0435 \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0438:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricValue, {\n                        children: \"1,847\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricLabel, {\n                        children: \"\\u041D\\u043E\\u0432\\u044B\\u0445 \\u043F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MetricValue, {\n                        children: \"+156\"\n                      })]\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CardTitle, {\n                    children: \"\\u0414\\u043E\\u0445\\u043E\\u0434\\u044B\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(RevenueChart, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartBar, {\n                      height: 60,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartValue, {\n                        children: \"$245\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartBar, {\n                      height: 80,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartValue, {\n                        children: \"$320\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartBar, {\n                      height: 45,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartValue, {\n                        children: \"$180\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartBar, {\n                      height: 95,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChartValue, {\n                        children: \"$425\"\n                      })\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CardTitle, {\n                    children: \"\\u0418\\u0418 \\u0418\\u043D\\u0441\\u0430\\u0439\\u0442\\u044B\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(AIInsights, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightIcon, {\n                        children: \"\\uD83C\\uDFAF\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightText, {\n                        children: \"\\u041B\\u0443\\u0447\\u0448\\u0435\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F \\u0434\\u043B\\u044F \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430: 19:00-22:00\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightIcon, {\n                        children: \"\\uD83D\\uDCC8\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightText, {\n                        children: \"\\u0422\\u0443\\u0440\\u043D\\u0438\\u0440\\u043D\\u044B\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u044B \\u043F\\u0440\\u0438\\u0432\\u043B\\u0435\\u043A\\u0430\\u044E\\u0442 +40% \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0435\\u0439\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightIcon, {\n                        children: \"\\uD83D\\uDCA1\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(InsightText, {\n                        children: \"\\u0420\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0443\\u0435\\u043C \\u0434\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435 \\u0438\\u043D\\u0442\\u0435\\u0440\\u0430\\u043A\\u0442\\u0438\\u0432\\u0430\"\n                      })]\n                    })]\n                  })]\n                })]\n              })\n            })\n          }, \"analytics\")]\n        })\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\n_s(StreamingPlatform, \"MaZbxbDmYbq2vf+Yw6hxqgEDZIk=\");\n_c = StreamingPlatform;\nconst StreamingContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c2 = StreamingContainer;\nconst ContentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  max-width: 1400px;\n  width: 100%;\n`;\n_c3 = ContentWrapper;\nconst SectionTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h2`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = SectionTitle;\nconst SectionSubtitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].p`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c5 = SectionSubtitle;\nconst TabNavigation = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 0.5rem;\n`;\n_c6 = TabNavigation;\nconst TabButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  border: none;\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 600;\n\n  &:hover {\n    background: rgba(74, 144, 226, 0.1);\n    color: #4a90e2;\n  }\n`;\n_c7 = TabButton;\nconst TabIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-size: 1.2rem;\n`;\n_c8 = TabIcon;\nconst TabLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  font-size: 0.9rem;\n`;\n_c9 = TabLabel;\nconst TabContent = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  min-height: 500px;\n`;\n_c10 = TabContent;\nconst BrowseSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c11 = BrowseSection;\nconst SectionHeader = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;\n_c12 = SectionHeader;\nconst LiveIndicator = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n_c13 = LiveIndicator;\nconst LiveDot = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 8px;\n  height: 8px;\n  background: #ef4444;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\n_c14 = LiveDot;\nconst StreamsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n_c15 = StreamsGrid;\nconst StreamCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c16 = StreamCard;\nconst StreamThumbnail = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: relative;\n  height: 180px;\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c17 = StreamThumbnail;\nconst ThumbnailIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 4rem;\n  filter: drop-shadow(0 0 20px currentColor);\n`;\n_c18 = ThumbnailIcon;\nconst StreamOverlay = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: absolute;\n  bottom: 0.5rem;\n  left: 0.5rem;\n  right: 0.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c19 = StreamOverlay;\nconst QualityBadge = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n_c20 = QualityBadge;\nconst ViewerCount = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n_c21 = ViewerCount;\nconst LiveBadge = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background: #ef4444;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 700;\n  animation: pulse 2s infinite;\n`;\n_c22 = LiveBadge;\nconst StreamInfo = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  padding: 1rem;\n`;\n_c23 = StreamInfo;\nconst StreamTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  color: white;\n  font-size: 1rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  line-height: 1.3;\n`;\n_c24 = StreamTitle;\nconst StreamMeta = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n`;\n_c25 = StreamMeta;\nconst StreamerName = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  color: #4a90e2;\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\n_c26 = StreamerName;\nconst StreamGame = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.8rem;\n`;\n_c27 = StreamGame;\nconst StreamTags = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n`;\n_c28 = StreamTags;\nconst StreamTag = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-size: 0.7rem;\n  font-weight: 500;\n`;\n_c29 = StreamTag;\nconst StreamSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c30 = StreamSection;\nconst StreamContainer = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c31 = StreamContainer;\nconst StreamPreview = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  overflow: hidden;\n  aspect-ratio: 16/9;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c32 = StreamPreview;\nconst VideoPreview = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].video`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c33 = VideoPreview;\nconst PreviewPlaceholder = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\n`;\n_c34 = PreviewPlaceholder;\nconst PlaceholderIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n`;\n_c35 = PlaceholderIcon;\nconst PlaceholderText = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 1.1rem;\n`;\n_c36 = PlaceholderText;\nconst StreamOverlays = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c37 = StreamOverlays;\nconst StreamStatus = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: rgba(239, 68, 68, 0.9);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-weight: 700;\n  font-size: 0.9rem;\n`;\n_c38 = StreamStatus;\nconst StatusDot = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  width: 8px;\n  height: 8px;\n  background: white;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\n_c39 = StatusDot;\nconst ViewerCounter = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-weight: 600;\n`;\n_c40 = ViewerCounter;\nconst StreamControls = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c41 = StreamControls;\nconst ControlsRow = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n_c42 = ControlsRow;\nconst StartStreamButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  background: linear-gradient(135deg, #4ade80, #22c55e);\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 1rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\n_c43 = StartStreamButton;\nconst StopStreamButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  flex: 1;\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 1rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\n_c44 = StopStreamButton;\nconst ControlButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button)`\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  padding: 1rem;\n  cursor: pointer;\n  font-weight: 600;\n`;\n_c45 = ControlButton;\nconst StreamSettings = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c46 = StreamSettings;\nconst SettingGroup = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c47 = SettingGroup;\nconst SettingLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].label`\n  display: block;\n  color: white;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n`;\n_c48 = SettingLabel;\nconst SettingInput = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].input`\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\n_c49 = SettingInput;\nconst SettingSelect = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].select`\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\n_c50 = SettingSelect;\nconst SettingToggles = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\n_c51 = SettingToggles;\nconst ToggleOption = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c52 = ToggleOption;\nconst ToggleInput = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].input`\n  width: 18px;\n  height: 18px;\n  accent-color: #4a90e2;\n`;\n_c53 = ToggleInput;\nconst ToggleLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].label`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  cursor: pointer;\n`;\n_c54 = ToggleLabel;\nconst LiveStats = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 1rem;\n\n  @media (max-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n`;\n_c55 = LiveStats;\nconst StatCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c56 = StatCard;\nconst StatIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\n_c57 = StatIcon;\nconst StatValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: #4a90e2;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n`;\n_c58 = StatValue;\nconst StatLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.8rem;\n`;\n_c59 = StatLabel;\nconst AnalyticsSection = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div``;\n_c60 = AnalyticsSection;\nconst AnalyticsGrid = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n_c61 = AnalyticsGrid;\nconst AnalyticsCard = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c62 = AnalyticsCard;\nconst CardTitle = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].h4`\n  color: white;\n  font-size: 1.2rem;\n  font-weight: 700;\n  margin-bottom: 1rem;\n`;\n_c63 = CardTitle;\nconst MetricsList = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\n_c64 = MetricsList;\nconst MetricItem = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c65 = MetricItem;\nconst MetricLabel = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\n_c66 = MetricLabel;\nconst MetricValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].span`\n  color: #4a90e2;\n  font-weight: 600;\n`;\n_c67 = MetricValue;\nconst RevenueChart = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: end;\n  gap: 1rem;\n  height: 120px;\n`;\n_c68 = RevenueChart;\nconst ChartBar = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  flex: 1;\n  height: ${props => props.height}%;\n  background: linear-gradient(to top, #4a90e2, #7b68ee);\n  border-radius: 5px 5px 0 0;\n  position: relative;\n  display: flex;\n  align-items: end;\n  justify-content: center;\n  padding-bottom: 0.5rem;\n`;\n_c69 = ChartBar;\nconst ChartValue = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: white;\n  font-size: 0.8rem;\n  font-weight: 600;\n`;\n_c70 = ChartValue;\nconst AIInsights = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c71 = AIInsights;\nconst InsightItem = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n_c72 = InsightItem;\nconst InsightIcon = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  font-size: 1.5rem;\n`;\n_c73 = InsightIcon;\nconst InsightText = styled_components__WEBPACK_IMPORTED_MODULE_4__[\"default\"].div`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  line-height: 1.4;\n`;\n_c74 = InsightText;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StreamingPlatform);\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69, _c70, _c71, _c72, _c73, _c74;\n$RefreshReg$(_c, \"StreamingPlatform\");\n$RefreshReg$(_c2, \"StreamingContainer\");\n$RefreshReg$(_c3, \"ContentWrapper\");\n$RefreshReg$(_c4, \"SectionTitle\");\n$RefreshReg$(_c5, \"SectionSubtitle\");\n$RefreshReg$(_c6, \"TabNavigation\");\n$RefreshReg$(_c7, \"TabButton\");\n$RefreshReg$(_c8, \"TabIcon\");\n$RefreshReg$(_c9, \"TabLabel\");\n$RefreshReg$(_c10, \"TabContent\");\n$RefreshReg$(_c11, \"BrowseSection\");\n$RefreshReg$(_c12, \"SectionHeader\");\n$RefreshReg$(_c13, \"LiveIndicator\");\n$RefreshReg$(_c14, \"LiveDot\");\n$RefreshReg$(_c15, \"StreamsGrid\");\n$RefreshReg$(_c16, \"StreamCard\");\n$RefreshReg$(_c17, \"StreamThumbnail\");\n$RefreshReg$(_c18, \"ThumbnailIcon\");\n$RefreshReg$(_c19, \"StreamOverlay\");\n$RefreshReg$(_c20, \"QualityBadge\");\n$RefreshReg$(_c21, \"ViewerCount\");\n$RefreshReg$(_c22, \"LiveBadge\");\n$RefreshReg$(_c23, \"StreamInfo\");\n$RefreshReg$(_c24, \"StreamTitle\");\n$RefreshReg$(_c25, \"StreamMeta\");\n$RefreshReg$(_c26, \"StreamerName\");\n$RefreshReg$(_c27, \"StreamGame\");\n$RefreshReg$(_c28, \"StreamTags\");\n$RefreshReg$(_c29, \"StreamTag\");\n$RefreshReg$(_c30, \"StreamSection\");\n$RefreshReg$(_c31, \"StreamContainer\");\n$RefreshReg$(_c32, \"StreamPreview\");\n$RefreshReg$(_c33, \"VideoPreview\");\n$RefreshReg$(_c34, \"PreviewPlaceholder\");\n$RefreshReg$(_c35, \"PlaceholderIcon\");\n$RefreshReg$(_c36, \"PlaceholderText\");\n$RefreshReg$(_c37, \"StreamOverlays\");\n$RefreshReg$(_c38, \"StreamStatus\");\n$RefreshReg$(_c39, \"StatusDot\");\n$RefreshReg$(_c40, \"ViewerCounter\");\n$RefreshReg$(_c41, \"StreamControls\");\n$RefreshReg$(_c42, \"ControlsRow\");\n$RefreshReg$(_c43, \"StartStreamButton\");\n$RefreshReg$(_c44, \"StopStreamButton\");\n$RefreshReg$(_c45, \"ControlButton\");\n$RefreshReg$(_c46, \"StreamSettings\");\n$RefreshReg$(_c47, \"SettingGroup\");\n$RefreshReg$(_c48, \"SettingLabel\");\n$RefreshReg$(_c49, \"SettingInput\");\n$RefreshReg$(_c50, \"SettingSelect\");\n$RefreshReg$(_c51, \"SettingToggles\");\n$RefreshReg$(_c52, \"ToggleOption\");\n$RefreshReg$(_c53, \"ToggleInput\");\n$RefreshReg$(_c54, \"ToggleLabel\");\n$RefreshReg$(_c55, \"LiveStats\");\n$RefreshReg$(_c56, \"StatCard\");\n$RefreshReg$(_c57, \"StatIcon\");\n$RefreshReg$(_c58, \"StatValue\");\n$RefreshReg$(_c59, \"StatLabel\");\n$RefreshReg$(_c60, \"AnalyticsSection\");\n$RefreshReg$(_c61, \"AnalyticsGrid\");\n$RefreshReg$(_c62, \"AnalyticsCard\");\n$RefreshReg$(_c63, \"CardTitle\");\n$RefreshReg$(_c64, \"MetricsList\");\n$RefreshReg$(_c65, \"MetricItem\");\n$RefreshReg$(_c66, \"MetricLabel\");\n$RefreshReg$(_c67, \"MetricValue\");\n$RefreshReg$(_c68, \"RevenueChart\");\n$RefreshReg$(_c69, \"ChartBar\");\n$RefreshReg$(_c70, \"ChartValue\");\n$RefreshReg$(_c71, \"AIInsights\");\n$RefreshReg$(_c72, \"InsightItem\");\n$RefreshReg$(_c73, \"InsightIcon\");\n$RefreshReg$(_c74, \"InsightText\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9TdHJlYW1pbmdQbGF0Zm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMkQ7QUFDSDtBQUNqQjtBQUFBO0FBbUJ2QyxNQUFNVyxpQkFBbUQsR0FBR0EsQ0FBQztFQUFFQztBQUFpQixDQUFDLEtBQUs7RUFBQUMsRUFBQTtFQUNwRixNQUFNLENBQUNDLFNBQVMsRUFBRUMsWUFBWSxDQUFDLEdBQUdkLCtDQUFRLENBQW9DLFFBQVEsQ0FBQztFQUN2RixNQUFNLENBQUNlLGVBQWUsRUFBRUMsa0JBQWtCLENBQUMsR0FBR2hCLCtDQUFRLENBQVcsRUFBRSxDQUFDO0VBQ3BFLE1BQU0sQ0FBQ2lCLFdBQVcsRUFBRUMsY0FBYyxDQUFDLEdBQUdsQiwrQ0FBUSxDQUFDLEtBQUssQ0FBQztFQUNyRCxNQUFNLENBQUNtQixXQUFXLEVBQUVDLGNBQWMsQ0FBQyxHQUFHcEIsK0NBQVEsQ0FBQztJQUM3Q3FCLE9BQU8sRUFBRSxDQUFDO0lBQ1ZDLFNBQVMsRUFBRSxJQUFJO0lBQ2ZDLFNBQVMsRUFBRSxNQUFNO0lBQ2pCQyxZQUFZLEVBQUU7RUFDaEIsQ0FBQyxDQUFDO0VBQ0YsTUFBTSxDQUFDQyxjQUFjLEVBQUVDLGlCQUFpQixDQUFDLEdBQUcxQiwrQ0FBUSxDQUFDO0lBQ25EMkIsS0FBSyxFQUFFLGlDQUFpQztJQUN4Q0MsT0FBTyxFQUFFLE9BQWdCO0lBQ3pCQyxVQUFVLEVBQUUsSUFBSTtJQUNoQkMsZUFBZSxFQUFFLElBQUk7SUFDckJDLFFBQVEsRUFBRTtFQUNaLENBQUMsQ0FBQztFQUVGLE1BQU1DLFFBQVEsR0FBRzlCLDZDQUFNLENBQW1CLElBQUksQ0FBQztFQUUvQ0QsZ0RBQVMsQ0FBQyxNQUFNO0lBQ2Q7SUFDQWUsa0JBQWtCLENBQUMsQ0FDakI7TUFDRWlCLEVBQUUsRUFBRSxHQUFHO01BQ1BOLEtBQUssRUFBRSwyQkFBMkI7TUFDbENPLFFBQVEsRUFBRSxjQUFjO01BQ3hCYixPQUFPLEVBQUUsSUFBSTtNQUNiYyxJQUFJLEVBQUUsT0FBTztNQUNiQyxTQUFTLEVBQUUsSUFBSTtNQUNmQyxNQUFNLEVBQUUsSUFBSTtNQUNaVCxPQUFPLEVBQUUsT0FBTztNQUNoQlUsUUFBUSxFQUFFLElBQUk7TUFDZEMsSUFBSSxFQUFFLENBQUMsUUFBUSxFQUFFLE9BQU8sRUFBRSxPQUFPO0lBQ25DLENBQUMsRUFDRDtNQUNFTixFQUFFLEVBQUUsR0FBRztNQUNQTixLQUFLLEVBQUUseUJBQXlCO01BQ2hDTyxRQUFRLEVBQUUsY0FBYztNQUN4QmIsT0FBTyxFQUFFLElBQUk7TUFDYmMsSUFBSSxFQUFFLE9BQU87TUFDYkMsU0FBUyxFQUFFLElBQUk7TUFDZkMsTUFBTSxFQUFFLElBQUk7TUFDWlQsT0FBTyxFQUFFLElBQUk7TUFDYlUsUUFBUSxFQUFFLElBQUk7TUFDZEMsSUFBSSxFQUFFLENBQUMsSUFBSSxFQUFFLE9BQU8sRUFBRSxPQUFPO0lBQy9CLENBQUMsRUFDRDtNQUNFTixFQUFFLEVBQUUsR0FBRztNQUNQTixLQUFLLEVBQUUsbUJBQW1CO01BQzFCTyxRQUFRLEVBQUUsWUFBWTtNQUN0QmIsT0FBTyxFQUFFLEdBQUc7TUFDWmMsSUFBSSxFQUFFLE9BQU87TUFDYkMsU0FBUyxFQUFFLElBQUk7TUFDZkMsTUFBTSxFQUFFLElBQUk7TUFDWlQsT0FBTyxFQUFFLE1BQU07TUFDZlUsUUFBUSxFQUFFLElBQUk7TUFDZEMsSUFBSSxFQUFFLENBQUMsVUFBVSxFQUFFLFNBQVM7SUFDOUIsQ0FBQyxDQUNGLENBQUM7O0lBRUY7SUFDQSxJQUFJdEIsV0FBVyxFQUFFO01BQ2YsTUFBTXVCLFFBQVEsR0FBR0MsV0FBVyxDQUFDLE1BQU07UUFDakNyQixjQUFjLENBQUNzQixJQUFJLEtBQUs7VUFDdEIsR0FBR0EsSUFBSTtVQUNQckIsT0FBTyxFQUFFcUIsSUFBSSxDQUFDckIsT0FBTyxHQUFHc0IsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1VBQzFEckIsWUFBWSxFQUFFa0IsSUFBSSxDQUFDbEIsWUFBWSxHQUFHbUIsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7VUFDL0R0QixTQUFTLEVBQUVtQixJQUFJLENBQUNuQixTQUFTLElBQUlvQixJQUFJLENBQUNFLE1BQU0sQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHRixJQUFJLENBQUNFLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxHQUFHLENBQUM7UUFDM0UsQ0FBQyxDQUFDLENBQUM7TUFDTCxDQUFDLEVBQUUsSUFBSSxDQUFDO01BRVIsT0FBTyxNQUFNQyxhQUFhLENBQUNOLFFBQVEsQ0FBQztJQUN0QztFQUNGLENBQUMsRUFBRSxDQUFDdkIsV0FBVyxDQUFDLENBQUM7RUFFakIsTUFBTThCLFdBQVcsR0FBRyxNQUFBQSxDQUFBLEtBQVk7SUFDOUIsSUFBSTtNQUNGO01BQ0EsTUFBTUMsTUFBTSxHQUFHLE1BQU1DLFNBQVMsQ0FBQ0MsWUFBWSxDQUFDQyxZQUFZLENBQUM7UUFDdkRDLEtBQUssRUFBRSxJQUFJO1FBQ1hDLEtBQUssRUFBRTtNQUNULENBQUMsQ0FBQztNQUVGLElBQUlyQixRQUFRLENBQUNzQixPQUFPLEVBQUU7UUFDcEJ0QixRQUFRLENBQUNzQixPQUFPLENBQUNDLFNBQVMsR0FBR1AsTUFBTTtNQUNyQztNQUVBOUIsY0FBYyxDQUFDLElBQUksQ0FBQztNQUNwQkUsY0FBYyxDQUFDc0IsSUFBSSxLQUFLO1FBQUUsR0FBR0EsSUFBSTtRQUFFckIsT0FBTyxFQUFFO01BQUUsQ0FBQyxDQUFDLENBQUM7SUFDbkQsQ0FBQyxDQUFDLE9BQU9tQyxLQUFLLEVBQUU7TUFDZEMsT0FBTyxDQUFDRCxLQUFLLENBQUMseUJBQXlCLEVBQUVBLEtBQUssQ0FBQztNQUMvQztNQUNBdEMsY0FBYyxDQUFDLElBQUksQ0FBQztNQUNwQkUsY0FBYyxDQUFDc0IsSUFBSSxLQUFLO1FBQUUsR0FBR0EsSUFBSTtRQUFFckIsT0FBTyxFQUFFO01BQUUsQ0FBQyxDQUFDLENBQUM7SUFDbkQ7RUFDRixDQUFDO0VBRUQsTUFBTXFDLFVBQVUsR0FBR0EsQ0FBQSxLQUFNO0lBQ3ZCLElBQUkxQixRQUFRLENBQUNzQixPQUFPLEVBQUVDLFNBQVMsRUFBRTtNQUMvQixNQUFNUCxNQUFNLEdBQUdoQixRQUFRLENBQUNzQixPQUFPLENBQUNDLFNBQXdCO01BQ3hEUCxNQUFNLENBQUNXLFNBQVMsQ0FBQyxDQUFDLENBQUNDLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJQSxLQUFLLENBQUNDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDbkQ7SUFDQTVDLGNBQWMsQ0FBQyxLQUFLLENBQUM7SUFDckJFLGNBQWMsQ0FBQ3NCLElBQUksS0FBSztNQUFFLEdBQUdBLElBQUk7TUFBRXJCLE9BQU8sRUFBRTtJQUFFLENBQUMsQ0FBQyxDQUFDO0VBQ25ELENBQUM7RUFFRCxvQkFDRWQsc0RBQUEsQ0FBQ3dELGtCQUFrQjtJQUFBQyxRQUFBLGVBQ2pCdkQsdURBQUEsQ0FBQ3dELGNBQWM7TUFBQUQsUUFBQSxnQkFDYnZELHVEQUFBLENBQUNOLGlEQUFNLENBQUMrRCxHQUFHO1FBQ1RDLE9BQU8sRUFBRTtVQUFFQyxPQUFPLEVBQUUsQ0FBQztVQUFFQyxDQUFDLEVBQUU7UUFBRyxDQUFFO1FBQy9CQyxPQUFPLEVBQUU7VUFBRUYsT0FBTyxFQUFFLENBQUM7VUFBRUMsQ0FBQyxFQUFFO1FBQUUsQ0FBRTtRQUM5QkUsVUFBVSxFQUFFO1VBQUVDLFFBQVEsRUFBRTtRQUFJLENBQUU7UUFBQVIsUUFBQSxnQkFFOUJ6RCxzREFBQSxDQUFDa0UsWUFBWTtVQUFBVCxRQUFBLEVBQUM7UUFBa0IsQ0FBYyxDQUFDLGVBQy9DekQsc0RBQUEsQ0FBQ21FLGVBQWU7VUFBQVYsUUFBQSxFQUFDO1FBRWpCLENBQWlCLENBQUM7TUFBQSxDQUNSLENBQUMsZUFHYnpELHNEQUFBLENBQUNvRSxhQUFhO1FBQUFYLFFBQUEsRUFDWCxDQUNDO1VBQUUvQixFQUFFLEVBQUUsUUFBUTtVQUFFMkMsS0FBSyxFQUFFLGVBQWU7VUFBRUMsSUFBSSxFQUFFO1FBQUssQ0FBQyxFQUNwRDtVQUFFNUMsRUFBRSxFQUFFLFFBQVE7VUFBRTJDLEtBQUssRUFBRSxXQUFXO1VBQUVDLElBQUksRUFBRTtRQUFLLENBQUMsRUFDaEQ7VUFBRTVDLEVBQUUsRUFBRSxXQUFXO1VBQUUyQyxLQUFLLEVBQUUsV0FBVztVQUFFQyxJQUFJLEVBQUU7UUFBSyxDQUFDLENBQ3BELENBQUNDLEdBQUcsQ0FBQ0MsR0FBRyxpQkFDUHRFLHVEQUFBLENBQUN1RSxTQUFTO1VBRVJDLE1BQU0sRUFBRXBFLFNBQVMsS0FBS2tFLEdBQUcsQ0FBQzlDLEVBQUc7VUFDN0JpRCxPQUFPLEVBQUVBLENBQUEsS0FBTXBFLFlBQVksQ0FBQ2lFLEdBQUcsQ0FBQzlDLEVBQVMsQ0FBRTtVQUMzQ2tELFVBQVUsRUFBRTtZQUFFQyxLQUFLLEVBQUU7VUFBSyxDQUFFO1VBQzVCQyxRQUFRLEVBQUU7WUFBRUQsS0FBSyxFQUFFO1VBQUssQ0FBRTtVQUFBcEIsUUFBQSxnQkFFMUJ6RCxzREFBQSxDQUFDK0UsT0FBTztZQUFBdEIsUUFBQSxFQUFFZSxHQUFHLENBQUNGO1VBQUksQ0FBVSxDQUFDLGVBQzdCdEUsc0RBQUEsQ0FBQ2dGLFFBQVE7WUFBQXZCLFFBQUEsRUFBRWUsR0FBRyxDQUFDSDtVQUFLLENBQVcsQ0FBQztRQUFBLEdBUDNCRyxHQUFHLENBQUM5QyxFQVFBLENBQ1o7TUFBQyxDQUNXLENBQUMsZUFHaEIxQixzREFBQSxDQUFDaUYsVUFBVTtRQUFBeEIsUUFBQSxlQUNUdkQsdURBQUEsQ0FBQ0wsMERBQWU7VUFBQ3FGLElBQUksRUFBQyxNQUFNO1VBQUF6QixRQUFBLEdBQ3pCbkQsU0FBUyxLQUFLLFFBQVEsaUJBQ3JCTixzREFBQSxDQUFDSixpREFBTSxDQUFDK0QsR0FBRztZQUVUQyxPQUFPLEVBQUU7Y0FBRUMsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRTtZQUFHLENBQUU7WUFDL0JwQixPQUFPLEVBQUU7Y0FBRUYsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRTtZQUFFLENBQUU7WUFDOUJDLElBQUksRUFBRTtjQUFFdkIsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRSxDQUFDO1lBQUcsQ0FBRTtZQUM3Qm5CLFVBQVUsRUFBRTtjQUFFQyxRQUFRLEVBQUU7WUFBSSxDQUFFO1lBQUFSLFFBQUEsZUFFOUJ2RCx1REFBQSxDQUFDbUYsYUFBYTtjQUFBNUIsUUFBQSxnQkFDWnZELHVEQUFBLENBQUNvRixhQUFhO2dCQUFBN0IsUUFBQSxnQkFDWnpELHNEQUFBLENBQUNtRSxlQUFlO2tCQUFBVixRQUFBLEVBQUM7Z0JBQWlCLENBQWlCLENBQUMsZUFDcER2RCx1REFBQSxDQUFDcUYsYUFBYTtrQkFBQTlCLFFBQUEsZ0JBQ1p6RCxzREFBQSxDQUFDd0YsT0FBTyxJQUFFLENBQUMsRUFDVmhGLGVBQWUsQ0FBQ2lGLE1BQU0sRUFBQyxtRkFDMUI7Z0JBQUEsQ0FBZSxDQUFDO2NBQUEsQ0FDSCxDQUFDLGVBRWhCekYsc0RBQUEsQ0FBQzBGLFdBQVc7Z0JBQUFqQyxRQUFBLEVBQ1RqRCxlQUFlLENBQUMrRCxHQUFHLENBQUM5QixNQUFNLGlCQUN6QnZDLHVEQUFBLENBQUN5RixVQUFVO2tCQUVUZixVQUFVLEVBQUU7b0JBQUVDLEtBQUssRUFBRTtrQkFBSyxDQUFFO2tCQUM1QkMsUUFBUSxFQUFFO29CQUFFRCxLQUFLLEVBQUU7a0JBQUssQ0FBRTtrQkFBQXBCLFFBQUEsZ0JBRTFCdkQsdURBQUEsQ0FBQzBGLGVBQWU7b0JBQUFuQyxRQUFBLGdCQUNkekQsc0RBQUEsQ0FBQzZGLGFBQWE7c0JBQUFwQyxRQUFBLEVBQUVoQixNQUFNLENBQUNaO29CQUFTLENBQWdCLENBQUMsZUFDakQzQix1REFBQSxDQUFDNEYsYUFBYTtzQkFBQXJDLFFBQUEsZ0JBQ1p6RCxzREFBQSxDQUFDK0YsWUFBWTt3QkFBQXRDLFFBQUEsRUFBRWhCLE1BQU0sQ0FBQ3BCO3NCQUFPLENBQWUsQ0FBQyxlQUM3Q25CLHVEQUFBLENBQUM4RixXQUFXO3dCQUFBdkMsUUFBQSxHQUFDLHFCQUFJLEVBQUNoQixNQUFNLENBQUMzQixPQUFPLENBQUNtRixjQUFjLENBQUMsQ0FBQztzQkFBQSxDQUFjLENBQUM7b0JBQUEsQ0FDbkQsQ0FBQyxFQUNmeEQsTUFBTSxDQUFDWCxNQUFNLGlCQUFJOUIsc0RBQUEsQ0FBQ2tHLFNBQVM7c0JBQUF6QyxRQUFBLEVBQUM7b0JBQUksQ0FBVyxDQUFDO2tCQUFBLENBQzlCLENBQUMsZUFFbEJ2RCx1REFBQSxDQUFDaUcsVUFBVTtvQkFBQTFDLFFBQUEsZ0JBQ1R6RCxzREFBQSxDQUFDb0csV0FBVztzQkFBQTNDLFFBQUEsRUFBRWhCLE1BQU0sQ0FBQ3JCO29CQUFLLENBQWMsQ0FBQyxlQUN6Q2xCLHVEQUFBLENBQUNtRyxVQUFVO3NCQUFBNUMsUUFBQSxnQkFDVHpELHNEQUFBLENBQUNzRyxZQUFZO3dCQUFBN0MsUUFBQSxFQUFFaEIsTUFBTSxDQUFDZDtzQkFBUSxDQUFlLENBQUMsZUFDOUMzQixzREFBQSxDQUFDdUcsVUFBVTt3QkFBQTlDLFFBQUEsRUFBRWhCLE1BQU0sQ0FBQ2I7c0JBQUksQ0FBYSxDQUFDO29CQUFBLENBQzVCLENBQUMsZUFDYjVCLHNEQUFBLENBQUN3RyxVQUFVO3NCQUFBL0MsUUFBQSxFQUNSaEIsTUFBTSxDQUFDVCxJQUFJLENBQUN1QyxHQUFHLENBQUNrQyxHQUFHLGlCQUNsQnpHLHNEQUFBLENBQUMwRyxTQUFTO3dCQUFBakQsUUFBQSxFQUFZZ0Q7c0JBQUcsR0FBVEEsR0FBcUIsQ0FDdEM7b0JBQUMsQ0FDUSxDQUFDO2tCQUFBLENBQ0gsQ0FBQztnQkFBQSxHQXhCUmhFLE1BQU0sQ0FBQ2YsRUF5QkYsQ0FDYjtjQUFDLENBQ1MsQ0FBQztZQUFBLENBQ0Q7VUFBQyxHQTlDWixRQStDTSxDQUNiLEVBRUFwQixTQUFTLEtBQUssUUFBUSxpQkFDckJOLHNEQUFBLENBQUNKLGlEQUFNLENBQUMrRCxHQUFHO1lBRVRDLE9BQU8sRUFBRTtjQUFFQyxPQUFPLEVBQUUsQ0FBQztjQUFFc0IsQ0FBQyxFQUFFO1lBQUcsQ0FBRTtZQUMvQnBCLE9BQU8sRUFBRTtjQUFFRixPQUFPLEVBQUUsQ0FBQztjQUFFc0IsQ0FBQyxFQUFFO1lBQUUsQ0FBRTtZQUM5QkMsSUFBSSxFQUFFO2NBQUV2QixPQUFPLEVBQUUsQ0FBQztjQUFFc0IsQ0FBQyxFQUFFLENBQUM7WUFBRyxDQUFFO1lBQzdCbkIsVUFBVSxFQUFFO2NBQUVDLFFBQVEsRUFBRTtZQUFJLENBQUU7WUFBQVIsUUFBQSxlQUU5QnZELHVEQUFBLENBQUN5RyxhQUFhO2NBQUFsRCxRQUFBLGdCQUNadkQsdURBQUEsQ0FBQzBHLGVBQWU7Z0JBQUFuRCxRQUFBLGdCQUNkdkQsdURBQUEsQ0FBQzJHLGFBQWE7a0JBQUFwRCxRQUFBLEdBQ1gvQyxXQUFXLGdCQUNWVixzREFBQSxDQUFDOEcsWUFBWTtvQkFDWEMsR0FBRyxFQUFFdEYsUUFBUztvQkFDZHVGLFFBQVE7b0JBQ1JDLEtBQUs7b0JBQ0xDLFdBQVc7a0JBQUEsQ0FDWixDQUFDLGdCQUVGaEgsdURBQUEsQ0FBQ2lILGtCQUFrQjtvQkFBQTFELFFBQUEsZ0JBQ2pCekQsc0RBQUEsQ0FBQ29ILGVBQWU7c0JBQUEzRCxRQUFBLEVBQUM7b0JBQUUsQ0FBaUIsQ0FBQyxlQUNyQ3pELHNEQUFBLENBQUNxSCxlQUFlO3NCQUFBNUQsUUFBQSxFQUFDO29CQUFtQixDQUFpQixDQUFDO2tCQUFBLENBQ3BDLENBQ3JCLEVBRUEvQyxXQUFXLGlCQUNWUix1REFBQSxDQUFDb0gsY0FBYztvQkFBQTdELFFBQUEsZ0JBQ2J2RCx1REFBQSxDQUFDcUgsWUFBWTtzQkFBQTlELFFBQUEsZ0JBQ1h6RCxzREFBQSxDQUFDd0gsU0FBUyxJQUFFLENBQUMseUNBRWY7b0JBQUEsQ0FBYyxDQUFDLGVBQ2Z0SCx1REFBQSxDQUFDdUgsYUFBYTtzQkFBQWhFLFFBQUEsR0FBQyxxQkFDVCxFQUFDN0MsV0FBVyxDQUFDRSxPQUFPO29CQUFBLENBQ1gsQ0FBQztrQkFBQSxDQUNGLENBQ2pCO2dCQUFBLENBQ1ksQ0FBQyxlQUVoQlosdURBQUEsQ0FBQ3dILGNBQWM7a0JBQUFqRSxRQUFBLGdCQUNidkQsdURBQUEsQ0FBQ3lILFdBQVc7b0JBQUFsRSxRQUFBLEdBQ1QsQ0FBQy9DLFdBQVcsZ0JBQ1hWLHNEQUFBLENBQUM0SCxpQkFBaUI7c0JBQ2hCakQsT0FBTyxFQUFFbkMsV0FBWTtzQkFDckJvQyxVQUFVLEVBQUU7d0JBQUVDLEtBQUssRUFBRTtzQkFBSyxDQUFFO3NCQUM1QkMsUUFBUSxFQUFFO3dCQUFFRCxLQUFLLEVBQUU7c0JBQUssQ0FBRTtzQkFBQXBCLFFBQUEsRUFDM0I7b0JBRUQsQ0FBbUIsQ0FBQyxnQkFFcEJ6RCxzREFBQSxDQUFDNkgsZ0JBQWdCO3NCQUNmbEQsT0FBTyxFQUFFeEIsVUFBVztzQkFDcEJ5QixVQUFVLEVBQUU7d0JBQUVDLEtBQUssRUFBRTtzQkFBSyxDQUFFO3NCQUM1QkMsUUFBUSxFQUFFO3dCQUFFRCxLQUFLLEVBQUU7c0JBQUssQ0FBRTtzQkFBQXBCLFFBQUEsRUFDM0I7b0JBRUQsQ0FBa0IsQ0FDbkIsZUFFRHpELHNEQUFBLENBQUM4SCxhQUFhO3NCQUNabEQsVUFBVSxFQUFFO3dCQUFFQyxLQUFLLEVBQUU7c0JBQUssQ0FBRTtzQkFDNUJDLFFBQVEsRUFBRTt3QkFBRUQsS0FBSyxFQUFFO3NCQUFLLENBQUU7c0JBQUFwQixRQUFBLEVBQzNCO29CQUVELENBQWUsQ0FBQztrQkFBQSxDQUNMLENBQUMsZUFFZHZELHVEQUFBLENBQUM2SCxjQUFjO29CQUFBdEUsUUFBQSxnQkFDYnZELHVEQUFBLENBQUM4SCxZQUFZO3NCQUFBdkUsUUFBQSxnQkFDWHpELHNEQUFBLENBQUNpSSxZQUFZO3dCQUFBeEUsUUFBQSxFQUFDO3NCQUFnQixDQUFjLENBQUMsZUFDN0N6RCxzREFBQSxDQUFDa0ksWUFBWTt3QkFDWEMsS0FBSyxFQUFFakgsY0FBYyxDQUFDRSxLQUFNO3dCQUM1QmdILFFBQVEsRUFBR0MsQ0FBQyxJQUFLbEgsaUJBQWlCLENBQUNnQixJQUFJLEtBQUs7MEJBQzFDLEdBQUdBLElBQUk7MEJBQ1BmLEtBQUssRUFBRWlILENBQUMsQ0FBQ0MsTUFBTSxDQUFDSDt3QkFDbEIsQ0FBQyxDQUFDO3NCQUFFLENBQ0wsQ0FBQztvQkFBQSxDQUNVLENBQUMsZUFFZmpJLHVEQUFBLENBQUM4SCxZQUFZO3NCQUFBdkUsUUFBQSxnQkFDWHpELHNEQUFBLENBQUNpSSxZQUFZO3dCQUFBeEUsUUFBQSxFQUFDO3NCQUFTLENBQWMsQ0FBQyxlQUN0Q3ZELHVEQUFBLENBQUNxSSxhQUFhO3dCQUNaSixLQUFLLEVBQUVqSCxjQUFjLENBQUNHLE9BQVE7d0JBQzlCK0csUUFBUSxFQUFHQyxDQUFDLElBQUtsSCxpQkFBaUIsQ0FBQ2dCLElBQUksS0FBSzswQkFDMUMsR0FBR0EsSUFBSTswQkFDUGQsT0FBTyxFQUFFZ0gsQ0FBQyxDQUFDQyxNQUFNLENBQUNIO3dCQUNwQixDQUFDLENBQUMsQ0FBRTt3QkFBQTFFLFFBQUEsZ0JBRUp6RCxzREFBQTswQkFBUW1JLEtBQUssRUFBQyxNQUFNOzBCQUFBMUUsUUFBQSxFQUFDO3dCQUFJLENBQVEsQ0FBQyxlQUNsQ3pELHNEQUFBOzBCQUFRbUksS0FBSyxFQUFDLE9BQU87MEJBQUExRSxRQUFBLEVBQUM7d0JBQUssQ0FBUSxDQUFDLGVBQ3BDekQsc0RBQUE7MEJBQVFtSSxLQUFLLEVBQUMsSUFBSTswQkFBQTFFLFFBQUEsRUFBQzt3QkFBRSxDQUFRLENBQUM7c0JBQUEsQ0FDakIsQ0FBQztvQkFBQSxDQUNKLENBQUMsZUFFZnZELHVEQUFBLENBQUNzSSxjQUFjO3NCQUFBL0UsUUFBQSxnQkFDYnZELHVEQUFBLENBQUN1SSxZQUFZO3dCQUFBaEYsUUFBQSxnQkFDWHpELHNEQUFBLENBQUMwSSxXQUFXOzBCQUNWQyxJQUFJLEVBQUMsVUFBVTswQkFDZkMsT0FBTyxFQUFFMUgsY0FBYyxDQUFDSSxVQUFXOzBCQUNuQzhHLFFBQVEsRUFBR0MsQ0FBQyxJQUFLbEgsaUJBQWlCLENBQUNnQixJQUFJLEtBQUs7NEJBQzFDLEdBQUdBLElBQUk7NEJBQ1BiLFVBQVUsRUFBRStHLENBQUMsQ0FBQ0MsTUFBTSxDQUFDTTswQkFDdkIsQ0FBQyxDQUFDO3dCQUFFLENBQ0wsQ0FBQyxlQUNGNUksc0RBQUEsQ0FBQzZJLFdBQVc7MEJBQUFwRixRQUFBLEVBQUM7d0JBQVksQ0FBYSxDQUFDO3NCQUFBLENBQzNCLENBQUMsZUFFZnZELHVEQUFBLENBQUN1SSxZQUFZO3dCQUFBaEYsUUFBQSxnQkFDWHpELHNEQUFBLENBQUMwSSxXQUFXOzBCQUNWQyxJQUFJLEVBQUMsVUFBVTswQkFDZkMsT0FBTyxFQUFFMUgsY0FBYyxDQUFDSyxlQUFnQjswQkFDeEM2RyxRQUFRLEVBQUdDLENBQUMsSUFBS2xILGlCQUFpQixDQUFDZ0IsSUFBSSxLQUFLOzRCQUMxQyxHQUFHQSxJQUFJOzRCQUNQWixlQUFlLEVBQUU4RyxDQUFDLENBQUNDLE1BQU0sQ0FBQ007MEJBQzVCLENBQUMsQ0FBQzt3QkFBRSxDQUNMLENBQUMsZUFDRjVJLHNEQUFBLENBQUM2SSxXQUFXOzBCQUFBcEYsUUFBQSxFQUFDO3dCQUFnQixDQUFhLENBQUM7c0JBQUEsQ0FDL0IsQ0FBQyxlQUVmdkQsdURBQUEsQ0FBQ3VJLFlBQVk7d0JBQUFoRixRQUFBLGdCQUNYekQsc0RBQUEsQ0FBQzBJLFdBQVc7MEJBQ1ZDLElBQUksRUFBQyxVQUFVOzBCQUNmQyxPQUFPLEVBQUUxSCxjQUFjLENBQUNNLFFBQVM7MEJBQ2pDNEcsUUFBUSxFQUFHQyxDQUFDLElBQUtsSCxpQkFBaUIsQ0FBQ2dCLElBQUksS0FBSzs0QkFDMUMsR0FBR0EsSUFBSTs0QkFDUFgsUUFBUSxFQUFFNkcsQ0FBQyxDQUFDQyxNQUFNLENBQUNNOzBCQUNyQixDQUFDLENBQUM7d0JBQUUsQ0FDTCxDQUFDLGVBQ0Y1SSxzREFBQSxDQUFDNkksV0FBVzswQkFBQXBGLFFBQUEsRUFBQzt3QkFBUyxDQUFhLENBQUM7c0JBQUEsQ0FDeEIsQ0FBQztvQkFBQSxDQUNELENBQUM7a0JBQUEsQ0FDSCxDQUFDO2dCQUFBLENBQ0gsQ0FBQztjQUFBLENBQ0YsQ0FBQyxFQUVqQi9DLFdBQVcsaUJBQ1ZSLHVEQUFBLENBQUM0SSxTQUFTO2dCQUFBckYsUUFBQSxnQkFDUnZELHVEQUFBLENBQUM2SSxRQUFRO2tCQUFBdEYsUUFBQSxnQkFDUHpELHNEQUFBLENBQUNnSixRQUFRO29CQUFBdkYsUUFBQSxFQUFDO2tCQUFHLENBQVUsQ0FBQyxlQUN4QnpELHNEQUFBLENBQUNpSixTQUFTO29CQUFBeEYsUUFBQSxFQUFFN0MsV0FBVyxDQUFDRTtrQkFBTyxDQUFZLENBQUMsZUFDNUNkLHNEQUFBLENBQUNrSixTQUFTO29CQUFBekYsUUFBQSxFQUFDO2tCQUFRLENBQVcsQ0FBQztnQkFBQSxDQUN2QixDQUFDLGVBRVh2RCx1REFBQSxDQUFDNkksUUFBUTtrQkFBQXRGLFFBQUEsZ0JBQ1B6RCxzREFBQSxDQUFDZ0osUUFBUTtvQkFBQXZGLFFBQUEsRUFBQztrQkFBRSxDQUFVLENBQUMsZUFDdkJ6RCxzREFBQSxDQUFDaUosU0FBUztvQkFBQXhGLFFBQUEsRUFBRTdDLFdBQVcsQ0FBQ0c7a0JBQVMsQ0FBWSxDQUFDLGVBQzlDZixzREFBQSxDQUFDa0osU0FBUztvQkFBQXpGLFFBQUEsRUFBQztrQkFBVyxDQUFXLENBQUM7Z0JBQUEsQ0FDMUIsQ0FBQyxlQUVYdkQsdURBQUEsQ0FBQzZJLFFBQVE7a0JBQUF0RixRQUFBLGdCQUNQekQsc0RBQUEsQ0FBQ2dKLFFBQVE7b0JBQUF2RixRQUFBLEVBQUM7a0JBQUUsQ0FBVSxDQUFDLGVBQ3ZCdkQsdURBQUEsQ0FBQytJLFNBQVM7b0JBQUF4RixRQUFBLEdBQUMsR0FBQyxFQUFDN0MsV0FBVyxDQUFDSSxTQUFTLENBQUNtSSxPQUFPLENBQUMsQ0FBQyxDQUFDO2tCQUFBLENBQVksQ0FBQyxlQUMxRG5KLHNEQUFBLENBQUNrSixTQUFTO29CQUFBekYsUUFBQSxFQUFDO2tCQUFPLENBQVcsQ0FBQztnQkFBQSxDQUN0QixDQUFDLGVBRVh2RCx1REFBQSxDQUFDNkksUUFBUTtrQkFBQXRGLFFBQUEsZ0JBQ1B6RCxzREFBQSxDQUFDZ0osUUFBUTtvQkFBQXZGLFFBQUEsRUFBQztrQkFBRSxDQUFVLENBQUMsZUFDdkJ6RCxzREFBQSxDQUFDaUosU0FBUztvQkFBQXhGLFFBQUEsRUFBRTdDLFdBQVcsQ0FBQ0s7a0JBQVksQ0FBWSxDQUFDLGVBQ2pEakIsc0RBQUEsQ0FBQ2tKLFNBQVM7b0JBQUF6RixRQUFBLEVBQUM7a0JBQVMsQ0FBVyxDQUFDO2dCQUFBLENBQ3hCLENBQUM7Y0FBQSxDQUNGLENBQ1o7WUFBQSxDQUNZO1VBQUMsR0EvSlosUUFnS00sQ0FDYixFQUVBbkQsU0FBUyxLQUFLLFdBQVcsaUJBQ3hCTixzREFBQSxDQUFDSixpREFBTSxDQUFDK0QsR0FBRztZQUVUQyxPQUFPLEVBQUU7Y0FBRUMsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRTtZQUFHLENBQUU7WUFDL0JwQixPQUFPLEVBQUU7Y0FBRUYsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRTtZQUFFLENBQUU7WUFDOUJDLElBQUksRUFBRTtjQUFFdkIsT0FBTyxFQUFFLENBQUM7Y0FBRXNCLENBQUMsRUFBRSxDQUFDO1lBQUcsQ0FBRTtZQUM3Qm5CLFVBQVUsRUFBRTtjQUFFQyxRQUFRLEVBQUU7WUFBSSxDQUFFO1lBQUFSLFFBQUEsZUFFOUJ6RCxzREFBQSxDQUFDb0osZ0JBQWdCO2NBQUEzRixRQUFBLGVBQ2Z2RCx1REFBQSxDQUFDbUosYUFBYTtnQkFBQTVGLFFBQUEsZ0JBQ1p2RCx1REFBQSxDQUFDb0osYUFBYTtrQkFBQTdGLFFBQUEsZ0JBQ1p6RCxzREFBQSxDQUFDdUosU0FBUztvQkFBQTlGLFFBQUEsRUFBQztrQkFBbUIsQ0FBVyxDQUFDLGVBQzFDdkQsdURBQUEsQ0FBQ3NKLFdBQVc7b0JBQUEvRixRQUFBLGdCQUNWdkQsdURBQUEsQ0FBQ3VKLFVBQVU7c0JBQUFoRyxRQUFBLGdCQUNUekQsc0RBQUEsQ0FBQzBKLFdBQVc7d0JBQUFqRyxRQUFBLEVBQUM7c0JBQW9CLENBQWEsQ0FBQyxlQUMvQ3pELHNEQUFBLENBQUMySixXQUFXO3dCQUFBbEcsUUFBQSxFQUFDO3NCQUFPLENBQWEsQ0FBQztvQkFBQSxDQUN4QixDQUFDLGVBQ2J2RCx1REFBQSxDQUFDdUosVUFBVTtzQkFBQWhHLFFBQUEsZ0JBQ1R6RCxzREFBQSxDQUFDMEosV0FBVzt3QkFBQWpHLFFBQUEsRUFBQztzQkFBZ0IsQ0FBYSxDQUFDLGVBQzNDekQsc0RBQUEsQ0FBQzJKLFdBQVc7d0JBQUFsRyxRQUFBLEVBQUM7c0JBQUcsQ0FBYSxDQUFDO29CQUFBLENBQ3BCLENBQUMsZUFDYnZELHVEQUFBLENBQUN1SixVQUFVO3NCQUFBaEcsUUFBQSxnQkFDVHpELHNEQUFBLENBQUMwSixXQUFXO3dCQUFBakcsUUFBQSxFQUFDO3NCQUFnQixDQUFhLENBQUMsZUFDM0N6RCxzREFBQSxDQUFDMkosV0FBVzt3QkFBQWxHLFFBQUEsRUFBQztzQkFBSyxDQUFhLENBQUM7b0JBQUEsQ0FDdEIsQ0FBQyxlQUNidkQsdURBQUEsQ0FBQ3VKLFVBQVU7c0JBQUFoRyxRQUFBLGdCQUNUekQsc0RBQUEsQ0FBQzBKLFdBQVc7d0JBQUFqRyxRQUFBLEVBQUM7c0JBQWtCLENBQWEsQ0FBQyxlQUM3Q3pELHNEQUFBLENBQUMySixXQUFXO3dCQUFBbEcsUUFBQSxFQUFDO3NCQUFJLENBQWEsQ0FBQztvQkFBQSxDQUNyQixDQUFDO2tCQUFBLENBQ0YsQ0FBQztnQkFBQSxDQUNELENBQUMsZUFFaEJ2RCx1REFBQSxDQUFDb0osYUFBYTtrQkFBQTdGLFFBQUEsZ0JBQ1p6RCxzREFBQSxDQUFDdUosU0FBUztvQkFBQTlGLFFBQUEsRUFBQztrQkFBTSxDQUFXLENBQUMsZUFDN0J2RCx1REFBQSxDQUFDMEosWUFBWTtvQkFBQW5HLFFBQUEsZ0JBQ1h6RCxzREFBQSxDQUFDNkosUUFBUTtzQkFBQ0MsTUFBTSxFQUFFLEVBQUc7c0JBQUFyRyxRQUFBLGVBQ25CekQsc0RBQUEsQ0FBQytKLFVBQVU7d0JBQUF0RyxRQUFBLEVBQUM7c0JBQUksQ0FBWTtvQkFBQyxDQUNyQixDQUFDLGVBQ1h6RCxzREFBQSxDQUFDNkosUUFBUTtzQkFBQ0MsTUFBTSxFQUFFLEVBQUc7c0JBQUFyRyxRQUFBLGVBQ25CekQsc0RBQUEsQ0FBQytKLFVBQVU7d0JBQUF0RyxRQUFBLEVBQUM7c0JBQUksQ0FBWTtvQkFBQyxDQUNyQixDQUFDLGVBQ1h6RCxzREFBQSxDQUFDNkosUUFBUTtzQkFBQ0MsTUFBTSxFQUFFLEVBQUc7c0JBQUFyRyxRQUFBLGVBQ25CekQsc0RBQUEsQ0FBQytKLFVBQVU7d0JBQUF0RyxRQUFBLEVBQUM7c0JBQUksQ0FBWTtvQkFBQyxDQUNyQixDQUFDLGVBQ1h6RCxzREFBQSxDQUFDNkosUUFBUTtzQkFBQ0MsTUFBTSxFQUFFLEVBQUc7c0JBQUFyRyxRQUFBLGVBQ25CekQsc0RBQUEsQ0FBQytKLFVBQVU7d0JBQUF0RyxRQUFBLEVBQUM7c0JBQUksQ0FBWTtvQkFBQyxDQUNyQixDQUFDO2tCQUFBLENBQ0MsQ0FBQztnQkFBQSxDQUNGLENBQUMsZUFFaEJ2RCx1REFBQSxDQUFDb0osYUFBYTtrQkFBQTdGLFFBQUEsZ0JBQ1p6RCxzREFBQSxDQUFDdUosU0FBUztvQkFBQTlGLFFBQUEsRUFBQztrQkFBVSxDQUFXLENBQUMsZUFDakN2RCx1REFBQSxDQUFDOEosVUFBVTtvQkFBQXZHLFFBQUEsZ0JBQ1R2RCx1REFBQSxDQUFDK0osV0FBVztzQkFBQXhHLFFBQUEsZ0JBQ1Z6RCxzREFBQSxDQUFDa0ssV0FBVzt3QkFBQXpHLFFBQUEsRUFBQztzQkFBRSxDQUFhLENBQUMsZUFDN0J6RCxzREFBQSxDQUFDbUssV0FBVzt3QkFBQTFHLFFBQUEsRUFBQztzQkFFYixDQUFhLENBQUM7b0JBQUEsQ0FDSCxDQUFDLGVBQ2R2RCx1REFBQSxDQUFDK0osV0FBVztzQkFBQXhHLFFBQUEsZ0JBQ1Z6RCxzREFBQSxDQUFDa0ssV0FBVzt3QkFBQXpHLFFBQUEsRUFBQztzQkFBRSxDQUFhLENBQUMsZUFDN0J6RCxzREFBQSxDQUFDbUssV0FBVzt3QkFBQTFHLFFBQUEsRUFBQztzQkFFYixDQUFhLENBQUM7b0JBQUEsQ0FDSCxDQUFDLGVBQ2R2RCx1REFBQSxDQUFDK0osV0FBVztzQkFBQXhHLFFBQUEsZ0JBQ1Z6RCxzREFBQSxDQUFDa0ssV0FBVzt3QkFBQXpHLFFBQUEsRUFBQztzQkFBRSxDQUFhLENBQUMsZUFDN0J6RCxzREFBQSxDQUFDbUssV0FBVzt3QkFBQTFHLFFBQUEsRUFBQztzQkFFYixDQUFhLENBQUM7b0JBQUEsQ0FDSCxDQUFDO2tCQUFBLENBQ0osQ0FBQztnQkFBQSxDQUNBLENBQUM7Y0FBQSxDQUNIO1lBQUMsQ0FDQTtVQUFDLEdBeEVmLFdBeUVNLENBQ2I7UUFBQSxDQUNjO01BQUMsQ0FDUixDQUFDO0lBQUEsQ0FDQztFQUFDLENBQ0MsQ0FBQztBQUV6QixDQUFDOztBQUVEO0FBQUFwRCxFQUFBLENBN2JNRixpQkFBbUQ7QUFBQWlLLEVBQUEsR0FBbkRqSyxpQkFBbUQ7QUE4YnpELE1BQU1xRCxrQkFBa0IsR0FBRzFELHlEQUFNLENBQUM2RCxHQUFJO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzBHLEdBQUEsR0FQSTdHLGtCQUFrQjtBQVN4QixNQUFNRSxjQUFjLEdBQUc1RCx5REFBTSxDQUFDNkQsR0FBSTtBQUNsQztBQUNBO0FBQ0EsQ0FBQztBQUFDMkcsR0FBQSxHQUhJNUcsY0FBYztBQUtwQixNQUFNUSxZQUFZLEdBQUdwRSx5REFBTSxDQUFDeUssRUFBRztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDQyxHQUFBLEdBWkl0RyxZQUFZO0FBY2xCLE1BQU1DLGVBQWUsR0FBR3JFLHlEQUFNLENBQUMySyxDQUFFO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDQyxHQUFBLEdBUkl2RyxlQUFlO0FBVXJCLE1BQU1DLGFBQWEsR0FBR3RFLHlEQUFNLENBQUM2RCxHQUFJO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2dILEdBQUEsR0FQSXZHLGFBQWE7QUFTbkIsTUFBTUssU0FBUyxHQUFHM0UsNkRBQU0sQ0FBQ0YsaURBQU0sQ0FBQ2dMLE1BQU0sQ0FBdUI7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0JDLEtBQUssSUFBSUEsS0FBSyxDQUFDbkcsTUFBTSxHQUFHLHlCQUF5QixHQUFHLGFBQWM7QUFDbEYsV0FBV21HLEtBQUssSUFBSUEsS0FBSyxDQUFDbkcsTUFBTSxHQUFHLFNBQVMsR0FBRywwQkFBMkI7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDb0csR0FBQSxHQW5CSXJHLFNBQVM7QUFxQmYsTUFBTU0sT0FBTyxHQUFHakYseURBQU0sQ0FBQ2lMLElBQUs7QUFDNUI7QUFDQSxDQUFDO0FBQUNDLEdBQUEsR0FGSWpHLE9BQU87QUFJYixNQUFNQyxRQUFRLEdBQUdsRix5REFBTSxDQUFDaUwsSUFBSztBQUM3QjtBQUNBLENBQUM7QUFBQ0UsR0FBQSxHQUZJakcsUUFBUTtBQUlkLE1BQU1DLFVBQVUsR0FBR25GLHlEQUFNLENBQUM2RCxHQUFJO0FBQzlCO0FBQ0EsQ0FBQztBQUFDdUgsSUFBQSxHQUZJakcsVUFBVTtBQUloQixNQUFNSSxhQUFhLEdBQUd2Rix5REFBTSxDQUFDNkQsR0FBSSxFQUFDO0FBQUN3SCxJQUFBLEdBQTdCOUYsYUFBYTtBQUVuQixNQUFNQyxhQUFhLEdBQUd4Rix5REFBTSxDQUFDNkQsR0FBSTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3lILElBQUEsR0FMSTlGLGFBQWE7QUFPbkIsTUFBTUMsYUFBYSxHQUFHekYseURBQU0sQ0FBQzZELEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzBILElBQUEsR0FOSTlGLGFBQWE7QUFRbkIsTUFBTUMsT0FBTyxHQUFHMUYseURBQU0sQ0FBQzZELEdBQUk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzJILElBQUEsR0FOSTlGLE9BQU87QUFRYixNQUFNRSxXQUFXLEdBQUc1Rix5REFBTSxDQUFDNkQsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM0SCxJQUFBLEdBSkk3RixXQUFXO0FBTWpCLE1BQU1DLFVBQVUsR0FBRzdGLDZEQUFNLENBQUNGLGlEQUFNLENBQUMrRCxHQUFHLENBQUU7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM2SCxJQUFBLEdBWEk3RixVQUFVO0FBYWhCLE1BQU1DLGVBQWUsR0FBRzlGLHlEQUFNLENBQUM2RCxHQUFJO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzhILElBQUEsR0FQSTdGLGVBQWU7QUFTckIsTUFBTUMsYUFBYSxHQUFHL0YseURBQU0sQ0FBQzZELEdBQUk7QUFDakM7QUFDQTtBQUNBLENBQUM7QUFBQytILElBQUEsR0FISTdGLGFBQWE7QUFLbkIsTUFBTUMsYUFBYSxHQUFHaEcseURBQU0sQ0FBQzZELEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNnSSxJQUFBLEdBUkk3RixhQUFhO0FBVW5CLE1BQU1DLFlBQVksR0FBR2pHLHlEQUFNLENBQUM2RCxHQUFJO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2lJLElBQUEsR0FQSTdGLFlBQVk7QUFTbEIsTUFBTUMsV0FBVyxHQUFHbEcseURBQU0sQ0FBQzZELEdBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDa0ksSUFBQSxHQVBJN0YsV0FBVztBQVNqQixNQUFNRSxTQUFTLEdBQUdwRyx5REFBTSxDQUFDNkQsR0FBSTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ21JLElBQUEsR0FYSTVGLFNBQVM7QUFhZixNQUFNQyxVQUFVLEdBQUdyRyx5REFBTSxDQUFDNkQsR0FBSTtBQUM5QjtBQUNBLENBQUM7QUFBQ29JLElBQUEsR0FGSTVGLFVBQVU7QUFJaEIsTUFBTUMsV0FBVyxHQUFHdEcseURBQU0sQ0FBQ2tNLEVBQUc7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ0MsSUFBQSxHQU5JN0YsV0FBVztBQVFqQixNQUFNQyxVQUFVLEdBQUd2Ryx5REFBTSxDQUFDNkQsR0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VJLElBQUEsR0FMSTdGLFVBQVU7QUFPaEIsTUFBTUMsWUFBWSxHQUFHeEcseURBQU0sQ0FBQ2lMLElBQUs7QUFDakM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDb0IsSUFBQSxHQUpJN0YsWUFBWTtBQU1sQixNQUFNQyxVQUFVLEdBQUd6Ryx5REFBTSxDQUFDaUwsSUFBSztBQUMvQjtBQUNBO0FBQ0EsQ0FBQztBQUFDcUIsSUFBQSxHQUhJN0YsVUFBVTtBQUtoQixNQUFNQyxVQUFVLEdBQUcxRyx5REFBTSxDQUFDNkQsR0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMwSSxJQUFBLEdBSkk3RixVQUFVO0FBTWhCLE1BQU1FLFNBQVMsR0FBRzVHLHlEQUFNLENBQUNpTCxJQUFLO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3VCLElBQUEsR0FQSTVGLFNBQVM7QUFTZixNQUFNQyxhQUFhLEdBQUc3Ryx5REFBTSxDQUFDNkQsR0FBSSxFQUFDO0FBQUM0SSxJQUFBLEdBQTdCNUYsYUFBYTtBQUVuQixNQUFNQyxlQUFlLEdBQUc5Ryx5REFBTSxDQUFDNkQsR0FBSTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDNkksSUFBQSxHQVRJNUYsZUFBZTtBQVdyQixNQUFNQyxhQUFhLEdBQUcvRyx5REFBTSxDQUFDNkQsR0FBSTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM4SSxJQUFBLEdBUEk1RixhQUFhO0FBU25CLE1BQU1DLFlBQVksR0FBR2hILHlEQUFNLENBQUMrQyxLQUFNO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzZKLElBQUEsR0FKSTVGLFlBQVk7QUFNbEIsTUFBTUssa0JBQWtCLEdBQUdySCx5REFBTSxDQUFDNkQsR0FBSTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2dKLElBQUEsR0FSSXhGLGtCQUFrQjtBQVV4QixNQUFNQyxlQUFlLEdBQUd0SCx5REFBTSxDQUFDNkQsR0FBSTtBQUNuQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNpSixJQUFBLEdBSkl4RixlQUFlO0FBTXJCLE1BQU1DLGVBQWUsR0FBR3ZILHlEQUFNLENBQUM2RCxHQUFJO0FBQ25DO0FBQ0E7QUFDQSxDQUFDO0FBQUNrSixJQUFBLEdBSEl4RixlQUFlO0FBS3JCLE1BQU1DLGNBQWMsR0FBR3hILHlEQUFNLENBQUM2RCxHQUFJO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDbUosSUFBQSxHQVJJeEYsY0FBYztBQVVwQixNQUFNQyxZQUFZLEdBQUd6SCx5REFBTSxDQUFDNkQsR0FBSTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNvSixJQUFBLEdBVkl4RixZQUFZO0FBWWxCLE1BQU1DLFNBQVMsR0FBRzFILHlEQUFNLENBQUM2RCxHQUFJO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNxSixJQUFBLEdBTkl4RixTQUFTO0FBUWYsTUFBTUMsYUFBYSxHQUFHM0gseURBQU0sQ0FBQzZELEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3NKLElBQUEsR0FOSXhGLGFBQWE7QUFRbkIsTUFBTUMsY0FBYyxHQUFHNUgseURBQU0sQ0FBQzZELEdBQUk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUN1SixJQUFBLEdBTEl4RixjQUFjO0FBT3BCLE1BQU1DLFdBQVcsR0FBRzdILHlEQUFNLENBQUM2RCxHQUFJO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3dKLElBQUEsR0FKSXhGLFdBQVc7QUFNakIsTUFBTUMsaUJBQWlCLEdBQUc5SCw2REFBTSxDQUFDRixpREFBTSxDQUFDZ0wsTUFBTSxDQUFFO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3dDLElBQUEsR0FWSXhGLGlCQUFpQjtBQVl2QixNQUFNQyxnQkFBZ0IsR0FBRy9ILDZEQUFNLENBQUNGLGlEQUFNLENBQUNnTCxNQUFNLENBQUU7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDeUMsSUFBQSxHQVZJeEYsZ0JBQWdCO0FBWXRCLE1BQU1DLGFBQWEsR0FBR2hJLDZEQUFNLENBQUNGLGlEQUFNLENBQUNnTCxNQUFNLENBQUU7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMwQyxJQUFBLEdBUkl4RixhQUFhO0FBVW5CLE1BQU1DLGNBQWMsR0FBR2pJLHlEQUFNLENBQUM2RCxHQUFJO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzRKLElBQUEsR0FKSXhGLGNBQWM7QUFNcEIsTUFBTUMsWUFBWSxHQUFHbEkseURBQU0sQ0FBQzZELEdBQUksRUFBQztBQUFDNkosSUFBQSxHQUE1QnhGLFlBQVk7QUFFbEIsTUFBTUMsWUFBWSxHQUFHbkkseURBQU0sQ0FBQ3VFLEtBQU07QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ29KLElBQUEsR0FOSXhGLFlBQVk7QUFRbEIsTUFBTUMsWUFBWSxHQUFHcEkseURBQU0sQ0FBQzROLEtBQU07QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDQyxJQUFBLEdBYkl6RixZQUFZO0FBZWxCLE1BQU1LLGFBQWEsR0FBR3pJLHlEQUFNLENBQUM4TixNQUFPO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ0MsSUFBQSxHQWJJdEYsYUFBYTtBQWVuQixNQUFNQyxjQUFjLEdBQUcxSSx5REFBTSxDQUFDNkQsR0FBSTtBQUNsQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUNtSyxJQUFBLEdBSkl0RixjQUFjO0FBTXBCLE1BQU1DLFlBQVksR0FBRzNJLHlEQUFNLENBQUM2RCxHQUFJO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ29LLElBQUEsR0FKSXRGLFlBQVk7QUFNbEIsTUFBTUMsV0FBVyxHQUFHNUkseURBQU0sQ0FBQzROLEtBQU07QUFDakM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDTSxJQUFBLEdBSkl0RixXQUFXO0FBTWpCLE1BQU1HLFdBQVcsR0FBRy9JLHlEQUFNLENBQUN1RSxLQUFNO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzRKLElBQUEsR0FKSXBGLFdBQVc7QUFNakIsTUFBTUMsU0FBUyxHQUFHaEoseURBQU0sQ0FBQzZELEdBQUk7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUN1SyxJQUFBLEdBUklwRixTQUFTO0FBVWYsTUFBTUMsUUFBUSxHQUFHakoseURBQU0sQ0FBQzZELEdBQUk7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3dLLElBQUEsR0FOSXBGLFFBQVE7QUFRZCxNQUFNQyxRQUFRLEdBQUdsSix5REFBTSxDQUFDNkQsR0FBSTtBQUM1QjtBQUNBO0FBQ0EsQ0FBQztBQUFDeUssSUFBQSxHQUhJcEYsUUFBUTtBQUtkLE1BQU1DLFNBQVMsR0FBR25KLHlEQUFNLENBQUM2RCxHQUFJO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDMEssSUFBQSxHQUxJcEYsU0FBUztBQU9mLE1BQU1DLFNBQVMsR0FBR3BKLHlEQUFNLENBQUM2RCxHQUFJO0FBQzdCO0FBQ0E7QUFDQSxDQUFDO0FBQUMySyxJQUFBLEdBSElwRixTQUFTO0FBS2YsTUFBTUUsZ0JBQWdCLEdBQUd0Six5REFBTSxDQUFDNkQsR0FBSSxFQUFDO0FBQUM0SyxJQUFBLEdBQWhDbkYsZ0JBQWdCO0FBRXRCLE1BQU1DLGFBQWEsR0FBR3ZKLHlEQUFNLENBQUM2RCxHQUFJO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQzZLLElBQUEsR0FKSW5GLGFBQWE7QUFNbkIsTUFBTUMsYUFBYSxHQUFHeEoseURBQU0sQ0FBQzZELEdBQUk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUM4SyxJQUFBLEdBTEluRixhQUFhO0FBT25CLE1BQU1DLFNBQVMsR0FBR3pKLHlEQUFNLENBQUNrTSxFQUFHO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDMEMsSUFBQSxHQUxJbkYsU0FBUztBQU9mLE1BQU1DLFdBQVcsR0FBRzFKLHlEQUFNLENBQUM2RCxHQUFJO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ2dMLElBQUEsR0FKSW5GLFdBQVc7QUFNakIsTUFBTUMsVUFBVSxHQUFHM0oseURBQU0sQ0FBQzZELEdBQUk7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDaUwsSUFBQSxHQUpJbkYsVUFBVTtBQU1oQixNQUFNQyxXQUFXLEdBQUc1Six5REFBTSxDQUFDaUwsSUFBSztBQUNoQztBQUNBO0FBQ0EsQ0FBQztBQUFDOEQsSUFBQSxHQUhJbkYsV0FBVztBQUtqQixNQUFNQyxXQUFXLEdBQUc3Six5REFBTSxDQUFDaUwsSUFBSztBQUNoQztBQUNBO0FBQ0EsQ0FBQztBQUFDK0QsSUFBQSxHQUhJbkYsV0FBVztBQUtqQixNQUFNQyxZQUFZLEdBQUc5Six5REFBTSxDQUFDNkQsR0FBSTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ29MLElBQUEsR0FMSW5GLFlBQVk7QUFPbEIsTUFBTUMsUUFBUSxHQUFHL0oseURBQU0sQ0FBQzZELEdBQXdCO0FBQ2hEO0FBQ0EsWUFBWWtILEtBQUssSUFBSUEsS0FBSyxDQUFDZixNQUFPO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDa0YsSUFBQSxHQVZJbkYsUUFBUTtBQVlkLE1BQU1FLFVBQVUsR0FBR2pLLHlEQUFNLENBQUM2RCxHQUFJO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFBQ3NMLElBQUEsR0FKSWxGLFVBQVU7QUFNaEIsTUFBTUMsVUFBVSxHQUFHbEsseURBQU0sQ0FBQzZELEdBQUk7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUFDdUwsSUFBQSxHQUpJbEYsVUFBVTtBQU1oQixNQUFNQyxXQUFXLEdBQUduSyx5REFBTSxDQUFDNkQsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUN3TCxJQUFBLEdBSklsRixXQUFXO0FBTWpCLE1BQU1DLFdBQVcsR0FBR3BLLHlEQUFNLENBQUM2RCxHQUFJO0FBQy9CO0FBQ0EsQ0FBQztBQUFDeUwsSUFBQSxHQUZJbEYsV0FBVztBQUlqQixNQUFNQyxXQUFXLEdBQUdySyx5REFBTSxDQUFDNkQsR0FBSTtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQUMwTCxJQUFBLEdBSklsRixXQUFXO0FBTWpCLCtEQUFlaEssaUJBQWlCLEVBQUM7QUFBQSxJQUFBaUssRUFBQSxFQUFBQyxHQUFBLEVBQUFDLEdBQUEsRUFBQUUsR0FBQSxFQUFBRSxHQUFBLEVBQUFDLEdBQUEsRUFBQUcsR0FBQSxFQUFBRSxHQUFBLEVBQUFDLEdBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFFLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUUsSUFBQSxFQUFBRSxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBLEVBQUFDLElBQUEsRUFBQUMsSUFBQSxFQUFBQyxJQUFBO0FBQUFDLFlBQUEsQ0FBQWxGLEVBQUE7QUFBQWtGLFlBQUEsQ0FBQWpGLEdBQUE7QUFBQWlGLFlBQUEsQ0FBQWhGLEdBQUE7QUFBQWdGLFlBQUEsQ0FBQTlFLEdBQUE7QUFBQThFLFlBQUEsQ0FBQTVFLEdBQUE7QUFBQTRFLFlBQUEsQ0FBQTNFLEdBQUE7QUFBQTJFLFlBQUEsQ0FBQXhFLEdBQUE7QUFBQXdFLFlBQUEsQ0FBQXRFLEdBQUE7QUFBQXNFLFlBQUEsQ0FBQXJFLEdBQUE7QUFBQXFFLFlBQUEsQ0FBQXBFLElBQUE7QUFBQW9FLFlBQUEsQ0FBQW5FLElBQUE7QUFBQW1FLFlBQUEsQ0FBQWxFLElBQUE7QUFBQWtFLFlBQUEsQ0FBQWpFLElBQUE7QUFBQWlFLFlBQUEsQ0FBQWhFLElBQUE7QUFBQWdFLFlBQUEsQ0FBQS9ELElBQUE7QUFBQStELFlBQUEsQ0FBQTlELElBQUE7QUFBQThELFlBQUEsQ0FBQTdELElBQUE7QUFBQTZELFlBQUEsQ0FBQTVELElBQUE7QUFBQTRELFlBQUEsQ0FBQTNELElBQUE7QUFBQTJELFlBQUEsQ0FBQTFELElBQUE7QUFBQTBELFlBQUEsQ0FBQXpELElBQUE7QUFBQXlELFlBQUEsQ0FBQXhELElBQUE7QUFBQXdELFlBQUEsQ0FBQXZELElBQUE7QUFBQXVELFlBQUEsQ0FBQXJELElBQUE7QUFBQXFELFlBQUEsQ0FBQXBELElBQUE7QUFBQW9ELFlBQUEsQ0FBQW5ELElBQUE7QUFBQW1ELFlBQUEsQ0FBQWxELElBQUE7QUFBQWtELFlBQUEsQ0FBQWpELElBQUE7QUFBQWlELFlBQUEsQ0FBQWhELElBQUE7QUFBQWdELFlBQUEsQ0FBQS9DLElBQUE7QUFBQStDLFlBQUEsQ0FBQTlDLElBQUE7QUFBQThDLFlBQUEsQ0FBQTdDLElBQUE7QUFBQTZDLFlBQUEsQ0FBQTVDLElBQUE7QUFBQTRDLFlBQUEsQ0FBQTNDLElBQUE7QUFBQTJDLFlBQUEsQ0FBQTFDLElBQUE7QUFBQTBDLFlBQUEsQ0FBQXpDLElBQUE7QUFBQXlDLFlBQUEsQ0FBQXhDLElBQUE7QUFBQXdDLFlBQUEsQ0FBQXZDLElBQUE7QUFBQXVDLFlBQUEsQ0FBQXRDLElBQUE7QUFBQXNDLFlBQUEsQ0FBQXJDLElBQUE7QUFBQXFDLFlBQUEsQ0FBQXBDLElBQUE7QUFBQW9DLFlBQUEsQ0FBQW5DLElBQUE7QUFBQW1DLFlBQUEsQ0FBQWxDLElBQUE7QUFBQWtDLFlBQUEsQ0FBQWpDLElBQUE7QUFBQWlDLFlBQUEsQ0FBQWhDLElBQUE7QUFBQWdDLFlBQUEsQ0FBQS9CLElBQUE7QUFBQStCLFlBQUEsQ0FBQTlCLElBQUE7QUFBQThCLFlBQUEsQ0FBQTdCLElBQUE7QUFBQTZCLFlBQUEsQ0FBQTNCLElBQUE7QUFBQTJCLFlBQUEsQ0FBQXpCLElBQUE7QUFBQXlCLFlBQUEsQ0FBQXhCLElBQUE7QUFBQXdCLFlBQUEsQ0FBQXZCLElBQUE7QUFBQXVCLFlBQUEsQ0FBQXRCLElBQUE7QUFBQXNCLFlBQUEsQ0FBQXJCLElBQUE7QUFBQXFCLFlBQUEsQ0FBQXBCLElBQUE7QUFBQW9CLFlBQUEsQ0FBQW5CLElBQUE7QUFBQW1CLFlBQUEsQ0FBQWxCLElBQUE7QUFBQWtCLFlBQUEsQ0FBQWpCLElBQUE7QUFBQWlCLFlBQUEsQ0FBQWhCLElBQUE7QUFBQWdCLFlBQUEsQ0FBQWYsSUFBQTtBQUFBZSxZQUFBLENBQUFkLElBQUE7QUFBQWMsWUFBQSxDQUFBYixJQUFBO0FBQUFhLFlBQUEsQ0FBQVosSUFBQTtBQUFBWSxZQUFBLENBQUFYLElBQUE7QUFBQVcsWUFBQSxDQUFBVixJQUFBO0FBQUFVLFlBQUEsQ0FBQVQsSUFBQTtBQUFBUyxZQUFBLENBQUFSLElBQUE7QUFBQVEsWUFBQSxDQUFBUCxJQUFBO0FBQUFPLFlBQUEsQ0FBQU4sSUFBQTtBQUFBTSxZQUFBLENBQUFMLElBQUE7QUFBQUssWUFBQSxDQUFBSixJQUFBO0FBQUFJLFlBQUEsQ0FBQUgsSUFBQTtBQUFBRyxZQUFBLENBQUFGLElBQUE7QUFBQUUsWUFBQSxDQUFBRCxJQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL1N0cmVhbWluZ1BsYXRmb3JtLnRzeD9iN2Q4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdzdHlsZWQtY29tcG9uZW50cyc7XG5cbmludGVyZmFjZSBTdHJlYW0ge1xuICBpZDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBzdHJlYW1lcjogc3RyaW5nO1xuICB2aWV3ZXJzOiBudW1iZXI7XG4gIGdhbWU6IHN0cmluZztcbiAgdGh1bWJuYWlsOiBzdHJpbmc7XG4gIGlzTGl2ZTogYm9vbGVhbjtcbiAgcXVhbGl0eTogJzcyMHAnIHwgJzEwODBwJyB8ICc0Syc7XG4gIGxhbmd1YWdlOiBzdHJpbmc7XG4gIHRhZ3M6IHN0cmluZ1tdO1xufVxuXG5pbnRlcmZhY2UgU3RyZWFtaW5nUGxhdGZvcm1Qcm9wcyB7XG4gIG9uU3RhcnRTdHJlYW1pbmc/OiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBTdHJlYW1pbmdQbGF0Zm9ybTogUmVhY3QuRkM8U3RyZWFtaW5nUGxhdGZvcm1Qcm9wcz4gPSAoeyBvblN0YXJ0U3RyZWFtaW5nIH0pID0+IHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCdicm93c2UnIHwgJ3N0cmVhbScgfCAnYW5hbHl0aWNzJz4oJ2Jyb3dzZScpO1xuICBjb25zdCBbZmVhdHVyZWRTdHJlYW1zLCBzZXRGZWF0dXJlZFN0cmVhbXNdID0gdXNlU3RhdGU8U3RyZWFtW10+KFtdKTtcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzdHJlYW1TdGF0cywgc2V0U3RyZWFtU3RhdHNdID0gdXNlU3RhdGUoe1xuICAgIHZpZXdlcnM6IDAsXG4gICAgZm9sbG93ZXJzOiAxMjQ3LFxuICAgIGRvbmF0aW9uczogMTU2LjUwLFxuICAgIGNoYXRNZXNzYWdlczogMFxuICB9KTtcbiAgY29uc3QgW3N0cmVhbVNldHRpbmdzLCBzZXRTdHJlYW1TZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgdGl0bGU6ICfQmtC+0LfRi9GA0Ywg0JzQsNGB0YLQtdGAIC0g0K3Qv9C40YfQtdGB0LrQuNC1INC40LPRgNGLIScsXG4gICAgcXVhbGl0eTogJzEwODBwJyBhcyBjb25zdCxcbiAgICBlbmFibGVDaGF0OiB0cnVlLFxuICAgIGVuYWJsZURvbmF0aW9uczogdHJ1ZSxcbiAgICBlbmFibGVBSTogdHJ1ZVxuICB9KTtcblxuICBjb25zdCB2aWRlb1JlZiA9IHVzZVJlZjxIVE1MVmlkZW9FbGVtZW50PihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vINCh0LjQvNGD0LvRj9GG0LjRjyDQv9C+0L/Rg9C70Y/RgNC90YvRhSDRgdGC0YDQuNC80L7QslxuICAgIHNldEZlYXR1cmVkU3RyZWFtcyhbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIHRpdGxlOiAn0KLRg9GA0L3QuNGAINC/0L4g0JTRg9GA0LDQutGDIC0g0KTQuNC90LDQuyEnLFxuICAgICAgICBzdHJlYW1lcjogJ1Byb0dhbWVyMjAyNCcsXG4gICAgICAgIHZpZXdlcnM6IDI4NDcsXG4gICAgICAgIGdhbWU6ICfQlNGD0YDQsNC6JyxcbiAgICAgICAgdGh1bWJuYWlsOiAn8J+DjycsXG4gICAgICAgIGlzTGl2ZTogdHJ1ZSxcbiAgICAgICAgcXVhbGl0eTogJzEwODBwJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdSVScsXG4gICAgICAgIHRhZ3M6IFsn0KLRg9GA0L3QuNGAJywgJ9Ck0LjQvdCw0LsnLCAn0J/RgNC+0YTQuCddXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzInLFxuICAgICAgICB0aXRsZTogJ9Cf0L7QutC10YAg0YEg0JjQmCAtINCa0YLQviDRg9C80L3QtdC1PycsXG4gICAgICAgIHN0cmVhbWVyOiAnQUlDaGFsbGVuZ2VyJyxcbiAgICAgICAgdmlld2VyczogMTUyMyxcbiAgICAgICAgZ2FtZTogJ9Cf0L7QutC10YAnLFxuICAgICAgICB0aHVtYm5haWw6ICfimaDvuI8nLFxuICAgICAgICBpc0xpdmU6IHRydWUsXG4gICAgICAgIHF1YWxpdHk6ICc0SycsXG4gICAgICAgIGxhbmd1YWdlOiAnRU4nLFxuICAgICAgICB0YWdzOiBbJ9CY0JgnLCAn0J/QvtC60LXRgCcsICfQktGL0LfQvtCyJ11cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMycsXG4gICAgICAgIHRpdGxlOiAn0J7QsdGD0YfQtdC90LjQtSDQvdC+0LLQuNGH0LrQvtCyJyxcbiAgICAgICAgc3RyZWFtZXI6ICdDYXJkTWFzdGVyJyxcbiAgICAgICAgdmlld2VyczogODkyLFxuICAgICAgICBnYW1lOiAn0JTRg9GA0LDQuicsXG4gICAgICAgIHRodW1ibmFpbDogJ/CfjpMnLFxuICAgICAgICBpc0xpdmU6IHRydWUsXG4gICAgICAgIHF1YWxpdHk6ICc3MjBwJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdSVScsXG4gICAgICAgIHRhZ3M6IFsn0J7QsdGD0YfQtdC90LjQtScsICfQndC+0LLQuNGH0LrQuCddXG4gICAgICB9XG4gICAgXSk7XG5cbiAgICAvLyDQodC40LzRg9C70Y/RhtC40Y8g0L7QsdC90L7QstC70LXQvdC40Y8g0YHRgtCw0YLQuNGB0YLQuNC60Lgg0YHRgtGA0LjQvNCwXG4gICAgaWYgKGlzU3RyZWFtaW5nKSB7XG4gICAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc2V0U3RyZWFtU3RhdHMocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgdmlld2VyczogcHJldi52aWV3ZXJzICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAgLSAzKSxcbiAgICAgICAgICBjaGF0TWVzc2FnZXM6IHByZXYuY2hhdE1lc3NhZ2VzICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogNSksXG4gICAgICAgICAgZG9uYXRpb25zOiBwcmV2LmRvbmF0aW9ucyArIChNYXRoLnJhbmRvbSgpID4gMC45ID8gTWF0aC5yYW5kb20oKSAqIDIwIDogMClcbiAgICAgICAgfSkpO1xuICAgICAgfSwgMzAwMCk7XG5cbiAgICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICB9XG4gIH0sIFtpc1N0cmVhbWluZ10pO1xuXG4gIGNvbnN0IHN0YXJ0U3RyZWFtID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDQl9Cw0L/RgNC+0YEg0LTQvtGB0YLRg9C/0LAg0Log0LrQsNC80LXRgNC1INC4INC80LjQutGA0L7RhNC+0L3Rg1xuICAgICAgY29uc3Qgc3RyZWFtID0gYXdhaXQgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5nZXRVc2VyTWVkaWEoe1xuICAgICAgICB2aWRlbzogdHJ1ZSxcbiAgICAgICAgYXVkaW86IHRydWVcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBpZiAodmlkZW9SZWYuY3VycmVudCkge1xuICAgICAgICB2aWRlb1JlZi5jdXJyZW50LnNyY09iamVjdCA9IHN0cmVhbTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG4gICAgICBzZXRTdHJlYW1TdGF0cyhwcmV2ID0+ICh7IC4uLnByZXYsIHZpZXdlcnM6IDEgfSkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfQntGI0LjQsdC60LAg0LTQvtGB0YLRg9C/0LAg0Log0LzQtdC00LjQsDonLCBlcnJvcik7XG4gICAgICAvLyDQodC40LzRg9C70Y/RhtC40Y8g0YHRgtGA0LjQvNCwINCx0LXQtyDRgNC10LDQu9GM0L3QvtCz0L4g0LLQuNC00LXQvlxuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG4gICAgICBzZXRTdHJlYW1TdGF0cyhwcmV2ID0+ICh7IC4uLnByZXYsIHZpZXdlcnM6IDEgfSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzdG9wU3RyZWFtID0gKCkgPT4ge1xuICAgIGlmICh2aWRlb1JlZi5jdXJyZW50Py5zcmNPYmplY3QpIHtcbiAgICAgIGNvbnN0IHN0cmVhbSA9IHZpZGVvUmVmLmN1cnJlbnQuc3JjT2JqZWN0IGFzIE1lZGlhU3RyZWFtO1xuICAgICAgc3RyZWFtLmdldFRyYWNrcygpLmZvckVhY2godHJhY2sgPT4gdHJhY2suc3RvcCgpKTtcbiAgICB9XG4gICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgIHNldFN0cmVhbVN0YXRzKHByZXYgPT4gKHsgLi4ucHJldiwgdmlld2VyczogMCB9KSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8U3RyZWFtaW5nQ29udGFpbmVyPlxuICAgICAgPENvbnRlbnRXcmFwcGVyPlxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogNTAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxTZWN0aW9uVGl0bGU+0KHRgtGA0LjQvNC40L3QsyDQv9C70LDRgtGE0L7RgNC80LA8L1NlY3Rpb25UaXRsZT5cbiAgICAgICAgICA8U2VjdGlvblN1YnRpdGxlPlxuICAgICAgICAgICAg0J/RgNC+0YTQtdGB0YHQuNC+0L3QsNC70YzQvdGL0LUg0LjQvdGB0YLRgNGD0LzQtdC90YLRiyDQtNC70Y8g0YHQvtC30LTQsNGC0LXQu9C10Lkg0LrQvtC90YLQtdC90YLQsFxuICAgICAgICAgIDwvU2VjdGlvblN1YnRpdGxlPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qINCd0LDQstC40LPQsNGG0LjRjyAqL31cbiAgICAgICAgPFRhYk5hdmlnYXRpb24+XG4gICAgICAgICAge1tcbiAgICAgICAgICAgIHsgaWQ6ICdicm93c2UnLCBsYWJlbDogJ9Ce0LHQt9C+0YAg0YHRgtGA0LjQvNC+0LInLCBpY29uOiAn8J+TuicgfSxcbiAgICAgICAgICAgIHsgaWQ6ICdzdHJlYW0nLCBsYWJlbDogJ9Cc0L7QuSDRgdGC0YDQuNC8JywgaWNvbjogJ/CfjqUnIH0sXG4gICAgICAgICAgICB7IGlkOiAnYW5hbHl0aWNzJywgbGFiZWw6ICfQkNC90LDQu9C40YLQuNC60LAnLCBpY29uOiAn8J+TiicgfVxuICAgICAgICAgIF0ubWFwKHRhYiA9PiAoXG4gICAgICAgICAgICA8VGFiQnV0dG9uXG4gICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICBhY3RpdmU9e2FjdGl2ZVRhYiA9PT0gdGFiLmlkfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodGFiLmlkIGFzIGFueSl9XG4gICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFRhYkljb24+e3RhYi5pY29ufTwvVGFiSWNvbj5cbiAgICAgICAgICAgICAgPFRhYkxhYmVsPnt0YWIubGFiZWx9PC9UYWJMYWJlbD5cbiAgICAgICAgICAgIDwvVGFiQnV0dG9uPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L1RhYk5hdmlnYXRpb24+XG5cbiAgICAgICAgey8qINCa0L7QvdGC0LXQvdGCINGC0LDQsdC+0LIgKi99XG4gICAgICAgIDxUYWJDb250ZW50PlxuICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIj5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdicm93c2UnICYmIChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9XCJicm93c2VcIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogNTAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEJyb3dzZVNlY3Rpb24+XG4gICAgICAgICAgICAgICAgICA8U2VjdGlvbkhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgPFNlY3Rpb25TdWJ0aXRsZT7Qn9C+0L/Rg9C70Y/RgNC90YvQtSDRgdGC0YDQuNC80Ys8L1NlY3Rpb25TdWJ0aXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPExpdmVJbmRpY2F0b3I+XG4gICAgICAgICAgICAgICAgICAgICAgPExpdmVEb3QgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZWRTdHJlYW1zLmxlbmd0aH0g0YHRgtGA0LjQvNC+0LIg0LIg0Y3RhNC40YDQtVxuICAgICAgICAgICAgICAgICAgICA8L0xpdmVJbmRpY2F0b3I+XG4gICAgICAgICAgICAgICAgICA8L1NlY3Rpb25IZWFkZXI+XG5cbiAgICAgICAgICAgICAgICAgIDxTdHJlYW1zR3JpZD5cbiAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmVkU3RyZWFtcy5tYXAoc3RyZWFtID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8U3RyZWFtQ2FyZFxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtzdHJlYW0uaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyIH19XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1UaHVtYm5haWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUaHVtYm5haWxJY29uPntzdHJlYW0udGh1bWJuYWlsfTwvVGh1bWJuYWlsSWNvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFN0cmVhbU92ZXJsYXk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFF1YWxpdHlCYWRnZT57c3RyZWFtLnF1YWxpdHl9PC9RdWFsaXR5QmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFZpZXdlckNvdW50PvCfkYHvuI8ge3N0cmVhbS52aWV3ZXJzLnRvTG9jYWxlU3RyaW5nKCl9PC9WaWV3ZXJDb3VudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TdHJlYW1PdmVybGF5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RyZWFtLmlzTGl2ZSAmJiA8TGl2ZUJhZGdlPkxJVkU8L0xpdmVCYWRnZT59XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1N0cmVhbVRodW1ibmFpbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0cmVhbUluZm8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1UaXRsZT57c3RyZWFtLnRpdGxlfTwvU3RyZWFtVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1NZXRhPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1lck5hbWU+e3N0cmVhbS5zdHJlYW1lcn08L1N0cmVhbWVyTmFtZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RyZWFtR2FtZT57c3RyZWFtLmdhbWV9PC9TdHJlYW1HYW1lPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1N0cmVhbU1ldGE+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1UYWdzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdHJlYW0udGFncy5tYXAodGFnID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1UYWcga2V5PXt0YWd9Pnt0YWd9PC9TdHJlYW1UYWc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvU3RyZWFtVGFncz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU3RyZWFtSW5mbz5cbiAgICAgICAgICAgICAgICAgICAgICA8L1N0cmVhbUNhcmQ+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9TdHJlYW1zR3JpZD5cbiAgICAgICAgICAgICAgICA8L0Jyb3dzZVNlY3Rpb24+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdzdHJlYW0nICYmIChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9XCJzdHJlYW1cIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogNTAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFN0cmVhbVNlY3Rpb24+XG4gICAgICAgICAgICAgICAgICA8U3RyZWFtQ29udGFpbmVyPlxuICAgICAgICAgICAgICAgICAgICA8U3RyZWFtUHJldmlldz5cbiAgICAgICAgICAgICAgICAgICAgICB7aXNTdHJlYW1pbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8VmlkZW9QcmV2aWV3XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlZj17dmlkZW9SZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGF1dG9QbGF5XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG11dGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYXlzSW5saW5lXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8UHJldmlld1BsYWNlaG9sZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8UGxhY2Vob2xkZXJJY29uPvCfjqU8L1BsYWNlaG9sZGVySWNvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFBsYWNlaG9sZGVyVGV4dD7Qn9GA0LXQtNC/0YDQvtGB0LzQvtGC0YAg0YHRgtGA0LjQvNCwPC9QbGFjZWhvbGRlclRleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ByZXZpZXdQbGFjZWhvbGRlcj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIHtpc1N0cmVhbWluZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RyZWFtT3ZlcmxheXM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdHJlYW1TdGF0dXM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXR1c0RvdCAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINCSINCt0KTQmNCg0JVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TdHJlYW1TdGF0dXM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxWaWV3ZXJDb3VudGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfkYHvuI8ge3N0cmVhbVN0YXRzLnZpZXdlcnN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVmlld2VyQ291bnRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU3RyZWFtT3ZlcmxheXM+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9TdHJlYW1QcmV2aWV3PlxuXG4gICAgICAgICAgICAgICAgICAgIDxTdHJlYW1Db250cm9scz5cbiAgICAgICAgICAgICAgICAgICAgICA8Q29udHJvbHNSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICB7IWlzU3RyZWFtaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RhcnRTdHJlYW1CdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtzdGFydFN0cmVhbX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfmoAg0J3QsNGH0LDRgtGMINGB0YLRgNC40LxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TdGFydFN0cmVhbUJ1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTdG9wU3RyZWFtQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c3RvcFN0cmVhbX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIOKPue+4jyDQntGB0YLQsNC90L7QstC40YLRjCDRgdGC0YDQuNC8XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvU3RvcFN0cmVhbUJ1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDb250cm9sQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAg4pqZ77iPINCd0LDRgdGC0YDQvtC50LrQuFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Db250cm9sQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ29udHJvbHNSb3c+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8U3RyZWFtU2V0dGluZ3M+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ0dyb3VwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ0xhYmVsPtCd0LDQt9Cy0LDQvdC40LUg0YHRgtGA0LjQvNCwOjwvU2V0dGluZ0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ0lucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N0cmVhbVNldHRpbmdzLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U3RyZWFtU2V0dGluZ3MocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiBlLnRhcmdldC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2V0dGluZ0dyb3VwPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ0dyb3VwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ0xhYmVsPtCa0LDRh9C10YHRgtCy0L46PC9TZXR0aW5nTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTZXR0aW5nU2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N0cmVhbVNldHRpbmdzLnF1YWxpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTdHJlYW1TZXR0aW5ncyhwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcXVhbGl0eTogZS50YXJnZXQudmFsdWUgYXMgYW55XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjcyMHBcIj43MjBwPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjEwODBwXCI+MTA4MHA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiNEtcIj40Szwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NldHRpbmdTZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NldHRpbmdHcm91cD5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPFNldHRpbmdUb2dnbGVzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9nZ2xlT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2dnbGVJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3N0cmVhbVNldHRpbmdzLmVuYWJsZUNoYXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFN0cmVhbVNldHRpbmdzKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlQ2hhdDogZS50YXJnZXQuY2hlY2tlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvZ2dsZUxhYmVsPtCS0LrQu9GO0YfQuNGC0Ywg0YfQsNGCPC9Ub2dnbGVMYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub2dnbGVPcHRpb24+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvZ2dsZU9wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9nZ2xlSW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzdHJlYW1TZXR0aW5ncy5lbmFibGVEb25hdGlvbnN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFN0cmVhbVNldHRpbmdzKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlRG9uYXRpb25zOiBlLnRhcmdldC5jaGVja2VkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9nZ2xlTGFiZWw+0J/RgNC40L3QuNC80LDRgtGMINC00L7QvdCw0YLRizwvVG9nZ2xlTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9nZ2xlT3B0aW9uPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2dnbGVPcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvZ2dsZUlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c3RyZWFtU2V0dGluZ3MuZW5hYmxlQUl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFN0cmVhbVNldHRpbmdzKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW5hYmxlQUk6IGUudGFyZ2V0LmNoZWNrZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb2dnbGVMYWJlbD7QmNCYLdCw0L3QsNC70LjQtzwvVG9nZ2xlTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9nZ2xlT3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZXR0aW5nVG9nZ2xlcz5cbiAgICAgICAgICAgICAgICAgICAgICA8L1N0cmVhbVNldHRpbmdzPlxuICAgICAgICAgICAgICAgICAgICA8L1N0cmVhbUNvbnRyb2xzPlxuICAgICAgICAgICAgICAgICAgPC9TdHJlYW1Db250YWluZXI+XG5cbiAgICAgICAgICAgICAgICAgIHtpc1N0cmVhbWluZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxMaXZlU3RhdHM+XG4gICAgICAgICAgICAgICAgICAgICAgPFN0YXRDYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXRJY29uPvCfkYHvuI88L1N0YXRJY29uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXRWYWx1ZT57c3RyZWFtU3RhdHMudmlld2Vyc308L1N0YXRWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTdGF0TGFiZWw+0JfRgNC40YLQtdC70LXQuTwvU3RhdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvU3RhdENhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgPFN0YXRDYXJkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXRJY29uPvCfkaU8L1N0YXRJY29uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXRWYWx1ZT57c3RyZWFtU3RhdHMuZm9sbG93ZXJzfTwvU3RhdFZhbHVlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXRMYWJlbD7Qn9C+0LTQv9C40YHRh9C40LrQvtCyPC9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9TdGF0Q2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8U3RhdENhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdEljb24+8J+SsDwvU3RhdEljb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdFZhbHVlPiR7c3RyZWFtU3RhdHMuZG9uYXRpb25zLnRvRml4ZWQoMil9PC9TdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdExhYmVsPtCU0L7QvdCw0YLQvtCyPC9TdGF0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9TdGF0Q2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8U3RhdENhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdEljb24+8J+SrDwvU3RhdEljb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdFZhbHVlPntzdHJlYW1TdGF0cy5jaGF0TWVzc2FnZXN9PC9TdGF0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdExhYmVsPtCh0L7QvtCx0YnQtdC90LjQuTwvU3RhdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvU3RhdENhcmQ+XG4gICAgICAgICAgICAgICAgICAgIDwvTGl2ZVN0YXRzPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L1N0cmVhbVNlY3Rpb24+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdhbmFseXRpY3MnICYmIChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9XCJhbmFseXRpY3NcIlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogNTAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC01MCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEFuYWx5dGljc1NlY3Rpb24+XG4gICAgICAgICAgICAgICAgICA8QW5hbHl0aWNzR3JpZD5cbiAgICAgICAgICAgICAgICAgICAgPEFuYWx5dGljc0NhcmQ+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZT7QodGC0LDRgtC40YHRgtC40LrQsCDQt9CwINC80LXRgdGP0YY8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljc0xpc3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1ldHJpY0xhYmVsPtCe0LHRidC10LUg0LLRgNC10LzRjyDRgdGC0YDQuNC80L7Qsjo8L01ldHJpY0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljVmFsdWU+NDfRhyAyM9C8PC9NZXRyaWNWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTWV0cmljSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxNZXRyaWNJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljTGFiZWw+0KHRgNC10LTQvdC40LUg0LfRgNC40YLQtdC70Lg6PC9NZXRyaWNMYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1ldHJpY1ZhbHVlPjIzNDwvTWV0cmljVmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L01ldHJpY0l0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1ldHJpY0xhYmVsPtCf0LjQutC+0LLRi9C1INC30YDQuNGC0LXQu9C4OjwvTWV0cmljTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZXRyaWNWYWx1ZT4xLDg0NzwvTWV0cmljVmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L01ldHJpY0l0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljSXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1ldHJpY0xhYmVsPtCd0L7QstGL0YUg0L/QvtC00L/QuNGB0YfQuNC60L7Qsjo8L01ldHJpY0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWV0cmljVmFsdWU+KzE1NjwvTWV0cmljVmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L01ldHJpY0l0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgPC9NZXRyaWNzTGlzdD5cbiAgICAgICAgICAgICAgICAgICAgPC9BbmFseXRpY3NDYXJkPlxuXG4gICAgICAgICAgICAgICAgICAgIDxBbmFseXRpY3NDYXJkPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+0JTQvtGF0L7QtNGLPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgPFJldmVudWVDaGFydD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGFydEJhciBoZWlnaHQ9ezYwfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoYXJ0VmFsdWU+JDI0NTwvQ2hhcnRWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2hhcnRCYXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hhcnRCYXIgaGVpZ2h0PXs4MH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGFydFZhbHVlPiQzMjA8L0NoYXJ0VmFsdWU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NoYXJ0QmFyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENoYXJ0QmFyIGhlaWdodD17NDV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hhcnRWYWx1ZT4kMTgwPC9DaGFydFZhbHVlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9DaGFydEJhcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGFydEJhciBoZWlnaHQ9ezk1fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoYXJ0VmFsdWU+JDQyNTwvQ2hhcnRWYWx1ZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2hhcnRCYXI+XG4gICAgICAgICAgICAgICAgICAgICAgPC9SZXZlbnVlQ2hhcnQ+XG4gICAgICAgICAgICAgICAgICAgIDwvQW5hbHl0aWNzQ2FyZD5cblxuICAgICAgICAgICAgICAgICAgICA8QW5hbHl0aWNzQ2FyZD5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPtCY0Jgg0JjQvdGB0LDQudGC0Ys8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICA8QUlJbnNpZ2h0cz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnNpZ2h0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEluc2lnaHRJY29uPvCfjq88L0luc2lnaHRJY29uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5zaWdodFRleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg0JvRg9GH0YjQtdC1INCy0YDQtdC80Y8g0LTQu9GPINGB0YLRgNC40LzQsDogMTk6MDAtMjI6MDBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnNpZ2h0VGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5zaWdodEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW5zaWdodEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnNpZ2h0SWNvbj7wn5OIPC9JbnNpZ2h0SWNvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEluc2lnaHRUZXh0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINCi0YPRgNC90LjRgNC90YvQtSDRgdGC0YDQuNC80Ysg0L/RgNC40LLQu9C10LrQsNGO0YIgKzQwJSDQt9GA0LjRgtC10LvQtdC5XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5zaWdodFRleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0luc2lnaHRJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEluc2lnaHRJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5zaWdodEljb24+8J+SoTwvSW5zaWdodEljb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnNpZ2h0VGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDQoNC10LrQvtC80LXQvdC00YPQtdC8INC00L7QsdCw0LLQuNGC0Ywg0LHQvtC70YzRiNC1INC40L3RgtC10YDQsNC60YLQuNCy0LBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JbnNpZ2h0VGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSW5zaWdodEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgPC9BSUluc2lnaHRzPlxuICAgICAgICAgICAgICAgICAgICA8L0FuYWx5dGljc0NhcmQ+XG4gICAgICAgICAgICAgICAgICA8L0FuYWx5dGljc0dyaWQ+XG4gICAgICAgICAgICAgICAgPC9BbmFseXRpY3NTZWN0aW9uPlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICA8L1RhYkNvbnRlbnQ+XG4gICAgICA8L0NvbnRlbnRXcmFwcGVyPlxuICAgIDwvU3RyZWFtaW5nQ29udGFpbmVyPlxuICApO1xufTtcblxuLy8g0KHRgtC40LvQuNC30L7QstCw0L3QvdGL0LUg0LrQvtC80L/QvtC90LXQvdGC0YtcbmNvbnN0IFN0cmVhbWluZ0NvbnRhaW5lciA9IHN0eWxlZC5kaXZgXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMGYwZjIzIDAlLCAjMWExYTJlIDUwJSwgIzE2MjEzZSAxMDAlKTtcbiAgcGFkZGluZzogNHJlbSAycmVtO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbmA7XG5cbmNvbnN0IENvbnRlbnRXcmFwcGVyID0gc3R5bGVkLmRpdmBcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIHdpZHRoOiAxMDAlO1xuYDtcblxuY29uc3QgU2VjdGlvblRpdGxlID0gc3R5bGVkLmgyYFxuICBmb250LXNpemU6IDMuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDkwMDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM0YTkwZTIsICM3YjY4ZWUsICM5MzcwZGIpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIGZvbnQtc2l6ZTogMi41cmVtO1xuICB9XG5gO1xuXG5jb25zdCBTZWN0aW9uU3VidGl0bGUgPSBzdHlsZWQucGBcbiAgZm9udC1zaXplOiAxLjNyZW07XG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogNHJlbTtcbiAgbWF4LXdpZHRoOiA4MDBweDtcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XG4gIG1hcmdpbi1yaWdodDogYXV0bztcbmA7XG5cbmNvbnN0IFRhYk5hdmlnYXRpb24gPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDAuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgcGFkZGluZzogMC41cmVtO1xuYDtcblxuY29uc3QgVGFiQnV0dG9uID0gc3R5bGVkKG1vdGlvbi5idXR0b24pPHsgYWN0aXZlOiBib29sZWFuIH0+YFxuICBmbGV4OiAxO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAwLjVyZW07XG4gIHBhZGRpbmc6IDFyZW07XG4gIGJvcmRlcjogbm9uZTtcbiAgYmFja2dyb3VuZDogJHtwcm9wcyA9PiBwcm9wcy5hY3RpdmUgPyAncmdiYSg3NCwgMTQ0LCAyMjYsIDAuMyknIDogJ3RyYW5zcGFyZW50J307XG4gIGNvbG9yOiAke3Byb3BzID0+IHByb3BzLmFjdGl2ZSA/ICcjNGE5MGUyJyA6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyknfTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBmb250LXdlaWdodDogNjAwO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoNzQsIDE0NCwgMjI2LCAwLjEpO1xuICAgIGNvbG9yOiAjNGE5MGUyO1xuICB9XG5gO1xuXG5jb25zdCBUYWJJY29uID0gc3R5bGVkLnNwYW5gXG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuYDtcblxuY29uc3QgVGFiTGFiZWwgPSBzdHlsZWQuc3BhbmBcbiAgZm9udC1zaXplOiAwLjlyZW07XG5gO1xuXG5jb25zdCBUYWJDb250ZW50ID0gc3R5bGVkLmRpdmBcbiAgbWluLWhlaWdodDogNTAwcHg7XG5gO1xuXG5jb25zdCBCcm93c2VTZWN0aW9uID0gc3R5bGVkLmRpdmBgO1xuXG5jb25zdCBTZWN0aW9uSGVhZGVyID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xuYDtcblxuY29uc3QgTGl2ZUluZGljYXRvciA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IExpdmVEb3QgPSBzdHlsZWQuZGl2YFxuICB3aWR0aDogOHB4O1xuICBoZWlnaHQ6IDhweDtcbiAgYmFja2dyb3VuZDogI2VmNDQ0NDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuYDtcblxuY29uc3QgU3RyZWFtc0dyaWQgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMwMHB4LCAxZnIpKTtcbiAgZ2FwOiAxLjVyZW07XG5gO1xuXG5jb25zdCBTdHJlYW1DYXJkID0gc3R5bGVkKG1vdGlvbi5kaXYpYFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAmOmhvdmVyIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gIH1cbmA7XG5cbmNvbnN0IFN0cmVhbVRodW1ibmFpbCA9IHN0eWxlZC5kaXZgXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgaGVpZ2h0OiAxODBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSwgIzE2MjEzZSk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuYDtcblxuY29uc3QgVGh1bWJuYWlsSWNvbiA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogNHJlbTtcbiAgZmlsdGVyOiBkcm9wLXNoYWRvdygwIDAgMjBweCBjdXJyZW50Q29sb3IpO1xuYDtcblxuY29uc3QgU3RyZWFtT3ZlcmxheSA9IHN0eWxlZC5kaXZgXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAwLjVyZW07XG4gIGxlZnQ6IDAuNXJlbTtcbiAgcmlnaHQ6IDAuNXJlbTtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuYDtcblxuY29uc3QgUXVhbGl0eUJhZGdlID0gc3R5bGVkLmRpdmBcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICBib3JkZXItcmFkaXVzOiA1cHg7XG4gIGZvbnQtc2l6ZTogMC43cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuYDtcblxuY29uc3QgVmlld2VyQ291bnQgPSBzdHlsZWQuZGl2YFxuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNyk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDVweDtcbiAgZm9udC1zaXplOiAwLjdyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG5gO1xuXG5jb25zdCBMaXZlQmFkZ2UgPSBzdHlsZWQuZGl2YFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMC41cmVtO1xuICByaWdodDogMC41cmVtO1xuICBiYWNrZ3JvdW5kOiAjZWY0NDQ0O1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xuICBib3JkZXItcmFkaXVzOiA1cHg7XG4gIGZvbnQtc2l6ZTogMC43cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuYDtcblxuY29uc3QgU3RyZWFtSW5mbyA9IHN0eWxlZC5kaXZgXG4gIHBhZGRpbmc6IDFyZW07XG5gO1xuXG5jb25zdCBTdHJlYW1UaXRsZSA9IHN0eWxlZC5oNGBcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXNpemU6IDFyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgbGluZS1oZWlnaHQ6IDEuMztcbmA7XG5cbmNvbnN0IFN0cmVhbU1ldGEgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XG5gO1xuXG5jb25zdCBTdHJlYW1lck5hbWUgPSBzdHlsZWQuc3BhbmBcbiAgY29sb3I6ICM0YTkwZTI7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuYDtcblxuY29uc3QgU3RyZWFtR2FtZSA9IHN0eWxlZC5zcGFuYFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICBmb250LXNpemU6IDAuOHJlbTtcbmA7XG5cbmNvbnN0IFN0cmVhbVRhZ3MgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIGdhcDogMC4yNXJlbTtcbmA7XG5cbmNvbnN0IFN0cmVhbVRhZyA9IHN0eWxlZC5zcGFuYFxuICBiYWNrZ3JvdW5kOiByZ2JhKDc0LCAxNDQsIDIyNiwgMC4yKTtcbiAgY29sb3I6ICM0YTkwZTI7XG4gIHBhZGRpbmc6IDAuMnJlbSAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGZvbnQtc2l6ZTogMC43cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xuYDtcblxuY29uc3QgU3RyZWFtU2VjdGlvbiA9IHN0eWxlZC5kaXZgYDtcblxuY29uc3QgU3RyZWFtQ29udGFpbmVyID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAyZnIgMWZyO1xuICBnYXA6IDJyZW07XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICB9XG5gO1xuXG5jb25zdCBTdHJlYW1QcmV2aWV3ID0gc3R5bGVkLmRpdmBcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhc3BlY3QtcmF0aW86IDE2Lzk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbmA7XG5cbmNvbnN0IFZpZGVvUHJldmlldyA9IHN0eWxlZC52aWRlb2BcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG5gO1xuXG5jb25zdCBQcmV2aWV3UGxhY2Vob2xkZXIgPSBzdHlsZWQuZGl2YFxuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSwgIzE2MjEzZSk7XG5gO1xuXG5jb25zdCBQbGFjZWhvbGRlckljb24gPSBzdHlsZWQuZGl2YFxuICBmb250LXNpemU6IDRyZW07XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIG9wYWNpdHk6IDAuNTtcbmA7XG5cbmNvbnN0IFBsYWNlaG9sZGVyVGV4dCA9IHN0eWxlZC5kaXZgXG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSk7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xuYDtcblxuY29uc3QgU3RyZWFtT3ZlcmxheXMgPSBzdHlsZWQuZGl2YFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMXJlbTtcbiAgbGVmdDogMXJlbTtcbiAgcmlnaHQ6IDFyZW07XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbmA7XG5cbmNvbnN0IFN0cmVhbVN0YXR1cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDIzOSwgNjgsIDY4LCAwLjkpO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBmb250LXdlaWdodDogNzAwO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IFN0YXR1c0RvdCA9IHN0eWxlZC5kaXZgXG4gIHdpZHRoOiA4cHg7XG4gIGhlaWdodDogOHB4O1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuYDtcblxuY29uc3QgVmlld2VyQ291bnRlciA9IHN0eWxlZC5kaXZgXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbmA7XG5cbmNvbnN0IFN0cmVhbUNvbnRyb2xzID0gc3R5bGVkLmRpdmBcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgcGFkZGluZzogMS41cmVtO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG5gO1xuXG5jb25zdCBDb250cm9sc1JvdyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuYDtcblxuY29uc3QgU3RhcnRTdHJlYW1CdXR0b24gPSBzdHlsZWQobW90aW9uLmJ1dHRvbilgXG4gIGZsZXg6IDE7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0YWRlODAsICMyMmM1NWUpO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogbm9uZTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgcGFkZGluZzogMXJlbTtcbiAgZm9udC1zaXplOiAxcmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjdXJzb3I6IHBvaW50ZXI7XG5gO1xuXG5jb25zdCBTdG9wU3RyZWFtQnV0dG9uID0gc3R5bGVkKG1vdGlvbi5idXR0b24pYFxuICBmbGV4OiAxO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZWY0NDQ0LCAjZGMyNjI2KTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXI6IG5vbmU7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIHBhZGRpbmc6IDFyZW07XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuYDtcblxuY29uc3QgQ29udHJvbEJ1dHRvbiA9IHN0eWxlZChtb3Rpb24uYnV0dG9uKWBcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBjb2xvcjogd2hpdGU7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgcGFkZGluZzogMXJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBmb250LXdlaWdodDogNjAwO1xuYDtcblxuY29uc3QgU3RyZWFtU2V0dGluZ3MgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDFyZW07XG5gO1xuXG5jb25zdCBTZXR0aW5nR3JvdXAgPSBzdHlsZWQuZGl2YGA7XG5cbmNvbnN0IFNldHRpbmdMYWJlbCA9IHN0eWxlZC5sYWJlbGBcbiAgZGlzcGxheTogYmxvY2s7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuICBmb250LXNpemU6IDAuOXJlbTtcbmA7XG5cbmNvbnN0IFNldHRpbmdJbnB1dCA9IHN0eWxlZC5pbnB1dGBcbiAgd2lkdGg6IDEwMCU7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIHBhZGRpbmc6IDAuNzVyZW07XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG5cbiAgJjpmb2N1cyB7XG4gICAgb3V0bGluZTogbm9uZTtcbiAgICBib3JkZXItY29sb3I6ICM0YTkwZTI7XG4gIH1cbmA7XG5cbmNvbnN0IFNldHRpbmdTZWxlY3QgPSBzdHlsZWQuc2VsZWN0YFxuICB3aWR0aDogMTAwJTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgcGFkZGluZzogMC43NXJlbTtcbiAgY29sb3I6IHdoaXRlO1xuICBmb250LXNpemU6IDAuOXJlbTtcblxuICAmOmZvY3VzIHtcbiAgICBvdXRsaW5lOiBub25lO1xuICAgIGJvcmRlci1jb2xvcjogIzRhOTBlMjtcbiAgfVxuYDtcblxuY29uc3QgU2V0dGluZ1RvZ2dsZXMgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDAuNzVyZW07XG5gO1xuXG5jb25zdCBUb2dnbGVPcHRpb24gPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDAuNXJlbTtcbmA7XG5cbmNvbnN0IFRvZ2dsZUlucHV0ID0gc3R5bGVkLmlucHV0YFxuICB3aWR0aDogMThweDtcbiAgaGVpZ2h0OiAxOHB4O1xuICBhY2NlbnQtY29sb3I6ICM0YTkwZTI7XG5gO1xuXG5jb25zdCBUb2dnbGVMYWJlbCA9IHN0eWxlZC5sYWJlbGBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGN1cnNvcjogcG9pbnRlcjtcbmA7XG5cbmNvbnN0IExpdmVTdGF0cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7XG4gIGdhcDogMXJlbTtcblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpO1xuICB9XG5gO1xuXG5jb25zdCBTdGF0Q2FyZCA9IHN0eWxlZC5kaXZgXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG5gO1xuXG5jb25zdCBTdGF0SWNvbiA9IHN0eWxlZC5kaXZgXG4gIGZvbnQtc2l6ZTogMnJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgU3RhdFZhbHVlID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6ICM0YTkwZTI7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuYDtcblxuY29uc3QgU3RhdExhYmVsID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgZm9udC1zaXplOiAwLjhyZW07XG5gO1xuXG5jb25zdCBBbmFseXRpY3NTZWN0aW9uID0gc3R5bGVkLmRpdmBgO1xuXG5jb25zdCBBbmFseXRpY3NHcmlkID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gIGdhcDogMS41cmVtO1xuYDtcblxuY29uc3QgQW5hbHl0aWNzQ2FyZCA9IHN0eWxlZC5kaXZgXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuYDtcblxuY29uc3QgQ2FyZFRpdGxlID0gc3R5bGVkLmg0YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuYDtcblxuY29uc3QgTWV0cmljc0xpc3QgPSBzdHlsZWQuZGl2YFxuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDAuNzVyZW07XG5gO1xuXG5jb25zdCBNZXRyaWNJdGVtID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuYDtcblxuY29uc3QgTWV0cmljTGFiZWwgPSBzdHlsZWQuc3BhbmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG5gO1xuXG5jb25zdCBNZXRyaWNWYWx1ZSA9IHN0eWxlZC5zcGFuYFxuICBjb2xvcjogIzRhOTBlMjtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbmA7XG5cbmNvbnN0IFJldmVudWVDaGFydCA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBlbmQ7XG4gIGdhcDogMXJlbTtcbiAgaGVpZ2h0OiAxMjBweDtcbmA7XG5cbmNvbnN0IENoYXJ0QmFyID0gc3R5bGVkLmRpdjx7IGhlaWdodDogbnVtYmVyIH0+YFxuICBmbGV4OiAxO1xuICBoZWlnaHQ6ICR7cHJvcHMgPT4gcHJvcHMuaGVpZ2h0fSU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byB0b3AsICM0YTkwZTIsICM3YjY4ZWUpO1xuICBib3JkZXItcmFkaXVzOiA1cHggNXB4IDAgMDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogZW5kO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcbmA7XG5cbmNvbnN0IENoYXJ0VmFsdWUgPSBzdHlsZWQuZGl2YFxuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuYDtcblxuY29uc3QgQUlJbnNpZ2h0cyA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMXJlbTtcbmA7XG5cbmNvbnN0IEluc2lnaHRJdGVtID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxcmVtO1xuYDtcblxuY29uc3QgSW5zaWdodEljb24gPSBzdHlsZWQuZGl2YFxuICBmb250LXNpemU6IDEuNXJlbTtcbmA7XG5cbmNvbnN0IEluc2lnaHRUZXh0ID0gc3R5bGVkLmRpdmBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjQ7XG5gO1xuXG5leHBvcnQgZGVmYXVsdCBTdHJlYW1pbmdQbGF0Zm9ybTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwic3R5bGVkIiwianN4IiwiX2pzeCIsImpzeHMiLCJfanN4cyIsIlN0cmVhbWluZ1BsYXRmb3JtIiwib25TdGFydFN0cmVhbWluZyIsIl9zIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiZmVhdHVyZWRTdHJlYW1zIiwic2V0RmVhdHVyZWRTdHJlYW1zIiwiaXNTdHJlYW1pbmciLCJzZXRJc1N0cmVhbWluZyIsInN0cmVhbVN0YXRzIiwic2V0U3RyZWFtU3RhdHMiLCJ2aWV3ZXJzIiwiZm9sbG93ZXJzIiwiZG9uYXRpb25zIiwiY2hhdE1lc3NhZ2VzIiwic3RyZWFtU2V0dGluZ3MiLCJzZXRTdHJlYW1TZXR0aW5ncyIsInRpdGxlIiwicXVhbGl0eSIsImVuYWJsZUNoYXQiLCJlbmFibGVEb25hdGlvbnMiLCJlbmFibGVBSSIsInZpZGVvUmVmIiwiaWQiLCJzdHJlYW1lciIsImdhbWUiLCJ0aHVtYm5haWwiLCJpc0xpdmUiLCJsYW5ndWFnZSIsInRhZ3MiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwicHJldiIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImNsZWFySW50ZXJ2YWwiLCJzdGFydFN0cmVhbSIsInN0cmVhbSIsIm5hdmlnYXRvciIsIm1lZGlhRGV2aWNlcyIsImdldFVzZXJNZWRpYSIsInZpZGVvIiwiYXVkaW8iLCJjdXJyZW50Iiwic3JjT2JqZWN0IiwiZXJyb3IiLCJjb25zb2xlIiwic3RvcFN0cmVhbSIsImdldFRyYWNrcyIsImZvckVhY2giLCJ0cmFjayIsInN0b3AiLCJTdHJlYW1pbmdDb250YWluZXIiLCJjaGlsZHJlbiIsIkNvbnRlbnRXcmFwcGVyIiwiZGl2IiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsIlNlY3Rpb25UaXRsZSIsIlNlY3Rpb25TdWJ0aXRsZSIsIlRhYk5hdmlnYXRpb24iLCJsYWJlbCIsImljb24iLCJtYXAiLCJ0YWIiLCJUYWJCdXR0b24iLCJhY3RpdmUiLCJvbkNsaWNrIiwid2hpbGVIb3ZlciIsInNjYWxlIiwid2hpbGVUYXAiLCJUYWJJY29uIiwiVGFiTGFiZWwiLCJUYWJDb250ZW50IiwibW9kZSIsIngiLCJleGl0IiwiQnJvd3NlU2VjdGlvbiIsIlNlY3Rpb25IZWFkZXIiLCJMaXZlSW5kaWNhdG9yIiwiTGl2ZURvdCIsImxlbmd0aCIsIlN0cmVhbXNHcmlkIiwiU3RyZWFtQ2FyZCIsIlN0cmVhbVRodW1ibmFpbCIsIlRodW1ibmFpbEljb24iLCJTdHJlYW1PdmVybGF5IiwiUXVhbGl0eUJhZGdlIiwiVmlld2VyQ291bnQiLCJ0b0xvY2FsZVN0cmluZyIsIkxpdmVCYWRnZSIsIlN0cmVhbUluZm8iLCJTdHJlYW1UaXRsZSIsIlN0cmVhbU1ldGEiLCJTdHJlYW1lck5hbWUiLCJTdHJlYW1HYW1lIiwiU3RyZWFtVGFncyIsInRhZyIsIlN0cmVhbVRhZyIsIlN0cmVhbVNlY3Rpb24iLCJTdHJlYW1Db250YWluZXIiLCJTdHJlYW1QcmV2aWV3IiwiVmlkZW9QcmV2aWV3IiwicmVmIiwiYXV0b1BsYXkiLCJtdXRlZCIsInBsYXlzSW5saW5lIiwiUHJldmlld1BsYWNlaG9sZGVyIiwiUGxhY2Vob2xkZXJJY29uIiwiUGxhY2Vob2xkZXJUZXh0IiwiU3RyZWFtT3ZlcmxheXMiLCJTdHJlYW1TdGF0dXMiLCJTdGF0dXNEb3QiLCJWaWV3ZXJDb3VudGVyIiwiU3RyZWFtQ29udHJvbHMiLCJDb250cm9sc1JvdyIsIlN0YXJ0U3RyZWFtQnV0dG9uIiwiU3RvcFN0cmVhbUJ1dHRvbiIsIkNvbnRyb2xCdXR0b24iLCJTdHJlYW1TZXR0aW5ncyIsIlNldHRpbmdHcm91cCIsIlNldHRpbmdMYWJlbCIsIlNldHRpbmdJbnB1dCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwiU2V0dGluZ1NlbGVjdCIsIlNldHRpbmdUb2dnbGVzIiwiVG9nZ2xlT3B0aW9uIiwiVG9nZ2xlSW5wdXQiLCJ0eXBlIiwiY2hlY2tlZCIsIlRvZ2dsZUxhYmVsIiwiTGl2ZVN0YXRzIiwiU3RhdENhcmQiLCJTdGF0SWNvbiIsIlN0YXRWYWx1ZSIsIlN0YXRMYWJlbCIsInRvRml4ZWQiLCJBbmFseXRpY3NTZWN0aW9uIiwiQW5hbHl0aWNzR3JpZCIsIkFuYWx5dGljc0NhcmQiLCJDYXJkVGl0bGUiLCJNZXRyaWNzTGlzdCIsIk1ldHJpY0l0ZW0iLCJNZXRyaWNMYWJlbCIsIk1ldHJpY1ZhbHVlIiwiUmV2ZW51ZUNoYXJ0IiwiQ2hhcnRCYXIiLCJoZWlnaHQiLCJDaGFydFZhbHVlIiwiQUlJbnNpZ2h0cyIsIkluc2lnaHRJdGVtIiwiSW5zaWdodEljb24iLCJJbnNpZ2h0VGV4dCIsIl9jIiwiX2MyIiwiX2MzIiwiaDIiLCJfYzQiLCJwIiwiX2M1IiwiX2M2IiwiYnV0dG9uIiwicHJvcHMiLCJfYzciLCJzcGFuIiwiX2M4IiwiX2M5IiwiX2MxMCIsIl9jMTEiLCJfYzEyIiwiX2MxMyIsIl9jMTQiLCJfYzE1IiwiX2MxNiIsIl9jMTciLCJfYzE4IiwiX2MxOSIsIl9jMjAiLCJfYzIxIiwiX2MyMiIsIl9jMjMiLCJoNCIsIl9jMjQiLCJfYzI1IiwiX2MyNiIsIl9jMjciLCJfYzI4IiwiX2MyOSIsIl9jMzAiLCJfYzMxIiwiX2MzMiIsIl9jMzMiLCJfYzM0IiwiX2MzNSIsIl9jMzYiLCJfYzM3IiwiX2MzOCIsIl9jMzkiLCJfYzQwIiwiX2M0MSIsIl9jNDIiLCJfYzQzIiwiX2M0NCIsIl9jNDUiLCJfYzQ2IiwiX2M0NyIsIl9jNDgiLCJpbnB1dCIsIl9jNDkiLCJzZWxlY3QiLCJfYzUwIiwiX2M1MSIsIl9jNTIiLCJfYzUzIiwiX2M1NCIsIl9jNTUiLCJfYzU2IiwiX2M1NyIsIl9jNTgiLCJfYzU5IiwiX2M2MCIsIl9jNjEiLCJfYzYyIiwiX2M2MyIsIl9jNjQiLCJfYzY1IiwiX2M2NiIsIl9jNjciLCJfYzY4IiwiX2M2OSIsIl9jNzAiLCJfYzcxIiwiX2M3MiIsIl9jNzMiLCJfYzc0IiwiJFJlZnJlc2hSZWckIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/StreamingPlatform.tsx\n"));

/***/ })

}]);