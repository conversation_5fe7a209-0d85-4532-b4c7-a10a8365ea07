"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_RevolutionaryFeatures_tsx";
exports.ids = ["src_components_RevolutionaryFeatures_tsx"];
exports.modules = {

/***/ "./src/components/RevolutionaryFeatures.tsx":
/*!**************************************************!*\
  !*** ./src/components/RevolutionaryFeatures.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst features = [{\n  id: 'quantum',\n  title: 'Квантовый игровой движок',\n  subtitle: 'Истинная случайность из квантовых источников',\n  description: 'Первая в мире игровая платформа, использующая реальные квантовые источники случайности для абсолютной честности игр.',\n  icon: '⚛️',\n  color: '#4a90e2',\n  stats: [{\n    label: 'Энтропия',\n    value: '99.99%'\n  }, {\n    label: 'Квантовых источников',\n    value: '5'\n  }, {\n    label: 'Тестов качества',\n    value: '15+'\n  }],\n  technologies: ['ANU Quantum RNG', 'ID Quantique', 'PicoQuant', 'Quantum Dice', 'NIST Tests'],\n  benefits: ['Абсолютная честность', 'Невозможность предсказания', 'Научная валидация']\n}, {\n  id: 'ai',\n  title: 'Эмоциональный ИИ',\n  subtitle: 'Глубокое понимание каждого игрока',\n  description: 'Революционная система искусственного интеллекта, анализирующая эмоциональное состояние и персонализирующая игровой опыт.',\n  icon: '🧠',\n  color: '#7b68ee',\n  stats: [{\n    label: 'Эмоциональных состояний',\n    value: '8'\n  }, {\n    label: 'Точность анализа',\n    value: '95%'\n  }, {\n    label: 'ML моделей',\n    value: '6'\n  }],\n  technologies: ['TensorFlow', 'OpenAI GPT-4', 'Computer Vision', 'NLP', 'Behavioral Analysis'],\n  benefits: ['Персонализация', 'Предотвращение тильта', 'Адаптивное обучение']\n}, {\n  id: 'metaverse',\n  title: '3D Метавселенная',\n  subtitle: 'Иммерсивные игровые миры',\n  description: 'Полноценная метавселенная с 3D мирами, VR/AR поддержкой и социальными пространствами для революционного игрового опыта.',\n  icon: '🌍',\n  color: '#9370db',\n  stats: [{\n    label: 'Тематических миров',\n    value: '6'\n  }, {\n    label: 'Одновременных игроков',\n    value: '10K+'\n  }, {\n    label: 'VR/AR поддержка',\n    value: '100%'\n  }],\n  technologies: ['Three.js', 'WebXR', 'WebGL', 'Physics Engine', 'Spatial Audio'],\n  benefits: ['Иммерсивность', 'Социальное взаимодействие', 'Новый опыт']\n}, {\n  id: 'security',\n  title: 'Квантовая безопасность',\n  subtitle: 'Непробиваемая защита',\n  description: 'Передовые системы безопасности с квантовым шифрованием, биометрией и ИИ-детекцией угроз.',\n  icon: '🛡️',\n  color: '#ff6b6b',\n  stats: [{\n    label: 'Точность детекции',\n    value: '99.9%'\n  }, {\n    label: 'Типов угроз',\n    value: '6'\n  }, {\n    label: 'Время реакции',\n    value: '<1с'\n  }],\n  technologies: ['Post-Quantum Crypto', 'Zero-Knowledge Proofs', 'Biometrics', 'AI Detection'],\n  benefits: ['Абсолютная защита', 'Приватность', 'Доверие']\n}, {\n  id: 'analytics',\n  title: 'Предиктивная аналитика',\n  subtitle: 'ИИ предсказывает будущее',\n  description: 'Мощная система машинного обучения, предсказывающая игровые события и персонализирующая опыт.',\n  icon: '📊',\n  color: '#4ecdc4',\n  stats: [{\n    label: 'Точность предсказаний',\n    value: '85%'\n  }, {\n    label: 'Анализируемых метрик',\n    value: '100+'\n  }, {\n    label: 'Обновлений в секунду',\n    value: '1000+'\n  }],\n  technologies: ['Machine Learning', 'Real-time Analytics', 'Predictive Models', 'Big Data'],\n  benefits: ['Персонализация', 'Оптимизация', 'Инсайты']\n}, {\n  id: 'web3',\n  title: 'Web3 экосистема',\n  subtitle: 'Децентрализованное будущее',\n  description: 'Полная интеграция с блокчейном, NFT картами, DeFi протоколами и DAO управлением.',\n  icon: '⛓️',\n  color: '#feca57',\n  stats: [{\n    label: 'Смарт-контрактов',\n    value: '6'\n  }, {\n    label: 'Поддерживаемых сетей',\n    value: '5+'\n  }, {\n    label: 'NFT коллекций',\n    value: '10+'\n  }],\n  technologies: ['Ethereum', 'Polygon', 'IPFS', 'Smart Contracts', 'DeFi'],\n  benefits: ['Владение активами', 'Децентрализация', 'Новая экономика']\n}];\nconst RevolutionaryFeatures = ({\n  currentSection\n}) => {\n  const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [hoveredFeature, setHoveredFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const interval = setInterval(() => {\n      setActiveFeature(prev => (prev + 1) % features.length);\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeaturesContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n          children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionSubtitle, {\n          children: \"\\u041C\\u044B \\u043E\\u0431\\u044A\\u0435\\u0434\\u0438\\u043D\\u0438\\u043B\\u0438 \\u043F\\u0435\\u0440\\u0435\\u0434\\u043E\\u0432\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438 \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0433\\u043E \\u0432 \\u043E\\u0434\\u043D\\u043E\\u0439 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0435\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeaturesGrid, {\n        children: features.map((feature, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeatureCard, {\n          active: index === activeFeature,\n          hovered: hoveredFeature === feature.id,\n          color: feature.color,\n          onMouseEnter: () => setHoveredFeature(feature.id),\n          onMouseLeave: () => setHoveredFeature(null),\n          onClick: () => setActiveFeature(index),\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5,\n            delay: index * 0.1\n          },\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureIcon, {\n            active: index === activeFeature,\n            children: feature.icon\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureTitle, {\n            children: feature.title\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureSubtitle, {\n            children: feature.subtitle\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n            children: (index === activeFeature || hoveredFeature === feature.id) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeatureDetails, {\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: 'auto'\n              },\n              exit: {\n                opacity: 0,\n                height: 0\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureDescription, {\n                children: feature.description\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatsGrid, {\n                children: feature.stats.map((stat, statIndex) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatItem, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                    children: stat.value\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                    children: stat.label\n                  })]\n                }, statIndex))\n              })]\n            })\n          })]\n        }, feature.id))\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DetailedView, {\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -50\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DetailedContent, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DetailedHeader, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DetailedIcon, {\n                color: features[activeFeature].color,\n                children: features[activeFeature].icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DetailedTitle, {\n                  children: features[activeFeature].title\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DetailedSubtitle, {\n                  children: features[activeFeature].subtitle\n                })]\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DetailedDescription, {\n              children: features[activeFeature].description\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechnologiesSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionLabel, {\n                children: \"\\u0422\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438:\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechnologiesList, {\n                children: features[activeFeature].technologies.map((tech, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechTag, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  children: tech\n                }, index))\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(BenefitsSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionLabel, {\n                children: \"\\u041F\\u0440\\u0435\\u0438\\u043C\\u0443\\u0449\\u0435\\u0441\\u0442\\u0432\\u0430:\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(BenefitsList, {\n                children: features[activeFeature].benefits.map((benefit, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(BenefitItem, {\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  children: [\"\\u2728 \", benefit]\n                }, index))\n              })]\n            })]\n          })\n        }, activeFeature)\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProgressIndicators, {\n        children: features.map((_, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProgressDot, {\n          active: index === activeFeature,\n          onClick: () => setActiveFeature(index),\n          whileHover: {\n            scale: 1.2\n          },\n          whileTap: {\n            scale: 0.9\n          }\n        }, index))\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\nconst FeaturesContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  width: 100%;\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2)`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst SectionSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\nconst FeaturesGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 2rem;\n  margin-bottom: 4rem;\n`;\nconst FeatureCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: ${props => props.active || props.hovered ? `linear-gradient(135deg, ${props.color}20, ${props.color}10)` : 'rgba(255, 255, 255, 0.05)'};\n  border: 2px solid ${props => props.active || props.hovered ? props.color : 'rgba(255, 255, 255, 0.1)'};\n  border-radius: 20px;\n  padding: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: ${props => `linear-gradient(135deg, ${props.color}10, transparent)`};\n    opacity: ${props => props.active || props.hovered ? 1 : 0};\n    transition: opacity 0.3s ease;\n  }\n`;\nconst FeatureIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  filter: ${props => props.active ? 'drop-shadow(0 0 20px currentColor)' : 'none'};\n  transition: all 0.3s ease;\n`;\nconst FeatureTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\nconst FeatureSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 1rem;\n`;\nconst FeatureDetails = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  position: relative;\n  z-index: 1;\n`;\nconst FeatureDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n`;\nconst StatsGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n`;\nconst StatItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n`;\nconst StatValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n`;\nconst StatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n`;\nconst DetailedView = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 3rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  margin-bottom: 3rem;\n`;\nconst DetailedContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst DetailedHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n`;\nconst DetailedIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  color: ${props => props.color};\n  filter: drop-shadow(0 0 20px ${props => props.color}50);\n`;\nconst DetailedTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\nconst DetailedSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.2rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\nconst DetailedDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.8;\n  margin-bottom: 2rem;\n`;\nconst TechnologiesSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 2rem;\n`;\nconst BenefitsSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst SectionLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 1rem;\n`;\nconst TechnologiesList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n`;\nconst TechTag = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span)`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n`;\nconst BenefitsList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\nconst BenefitItem = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 1rem;\n`;\nconst ProgressIndicators = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n`;\nconst ProgressDot = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RevolutionaryFeatures);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9SZXZvbHV0aW9uYXJ5RmVhdHVyZXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQW1EO0FBQ0s7QUFDakI7QUFBQTtBQWtCdkMsTUFBTVUsUUFBbUIsR0FBRyxDQUMxQjtFQUNFQyxFQUFFLEVBQUUsU0FBUztFQUNiQyxLQUFLLEVBQUUsMEJBQTBCO0VBQ2pDQyxRQUFRLEVBQUUsOENBQThDO0VBQ3hEQyxXQUFXLEVBQUUsc0hBQXNIO0VBQ25JQyxJQUFJLEVBQUUsSUFBSTtFQUNWQyxLQUFLLEVBQUUsU0FBUztFQUNoQkMsS0FBSyxFQUFFLENBQ0w7SUFBRUMsS0FBSyxFQUFFLFVBQVU7SUFBRUMsS0FBSyxFQUFFO0VBQVMsQ0FBQyxFQUN0QztJQUFFRCxLQUFLLEVBQUUsc0JBQXNCO0lBQUVDLEtBQUssRUFBRTtFQUFJLENBQUMsRUFDN0M7SUFBRUQsS0FBSyxFQUFFLGlCQUFpQjtJQUFFQyxLQUFLLEVBQUU7RUFBTSxDQUFDLENBQzNDO0VBQ0RDLFlBQVksRUFBRSxDQUFDLGlCQUFpQixFQUFFLGNBQWMsRUFBRSxXQUFXLEVBQUUsY0FBYyxFQUFFLFlBQVksQ0FBQztFQUM1RkMsUUFBUSxFQUFFLENBQUMsc0JBQXNCLEVBQUUsNEJBQTRCLEVBQUUsbUJBQW1CO0FBQ3RGLENBQUMsRUFDRDtFQUNFVixFQUFFLEVBQUUsSUFBSTtFQUNSQyxLQUFLLEVBQUUsa0JBQWtCO0VBQ3pCQyxRQUFRLEVBQUUsbUNBQW1DO0VBQzdDQyxXQUFXLEVBQUUsMEhBQTBIO0VBQ3ZJQyxJQUFJLEVBQUUsSUFBSTtFQUNWQyxLQUFLLEVBQUUsU0FBUztFQUNoQkMsS0FBSyxFQUFFLENBQ0w7SUFBRUMsS0FBSyxFQUFFLHlCQUF5QjtJQUFFQyxLQUFLLEVBQUU7RUFBSSxDQUFDLEVBQ2hEO0lBQUVELEtBQUssRUFBRSxrQkFBa0I7SUFBRUMsS0FBSyxFQUFFO0VBQU0sQ0FBQyxFQUMzQztJQUFFRCxLQUFLLEVBQUUsWUFBWTtJQUFFQyxLQUFLLEVBQUU7RUFBSSxDQUFDLENBQ3BDO0VBQ0RDLFlBQVksRUFBRSxDQUFDLFlBQVksRUFBRSxjQUFjLEVBQUUsaUJBQWlCLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixDQUFDO0VBQzdGQyxRQUFRLEVBQUUsQ0FBQyxnQkFBZ0IsRUFBRSx1QkFBdUIsRUFBRSxxQkFBcUI7QUFDN0UsQ0FBQyxFQUNEO0VBQ0VWLEVBQUUsRUFBRSxXQUFXO0VBQ2ZDLEtBQUssRUFBRSxrQkFBa0I7RUFDekJDLFFBQVEsRUFBRSwwQkFBMEI7RUFDcENDLFdBQVcsRUFBRSx5SEFBeUg7RUFDdElDLElBQUksRUFBRSxJQUFJO0VBQ1ZDLEtBQUssRUFBRSxTQUFTO0VBQ2hCQyxLQUFLLEVBQUUsQ0FDTDtJQUFFQyxLQUFLLEVBQUUsb0JBQW9CO0lBQUVDLEtBQUssRUFBRTtFQUFJLENBQUMsRUFDM0M7SUFBRUQsS0FBSyxFQUFFLHVCQUF1QjtJQUFFQyxLQUFLLEVBQUU7RUFBTyxDQUFDLEVBQ2pEO0lBQUVELEtBQUssRUFBRSxpQkFBaUI7SUFBRUMsS0FBSyxFQUFFO0VBQU8sQ0FBQyxDQUM1QztFQUNEQyxZQUFZLEVBQUUsQ0FBQyxVQUFVLEVBQUUsT0FBTyxFQUFFLE9BQU8sRUFBRSxnQkFBZ0IsRUFBRSxlQUFlLENBQUM7RUFDL0VDLFFBQVEsRUFBRSxDQUFDLGVBQWUsRUFBRSwyQkFBMkIsRUFBRSxZQUFZO0FBQ3ZFLENBQUMsRUFDRDtFQUNFVixFQUFFLEVBQUUsVUFBVTtFQUNkQyxLQUFLLEVBQUUsd0JBQXdCO0VBQy9CQyxRQUFRLEVBQUUsc0JBQXNCO0VBQ2hDQyxXQUFXLEVBQUUsMEZBQTBGO0VBQ3ZHQyxJQUFJLEVBQUUsS0FBSztFQUNYQyxLQUFLLEVBQUUsU0FBUztFQUNoQkMsS0FBSyxFQUFFLENBQ0w7SUFBRUMsS0FBSyxFQUFFLG1CQUFtQjtJQUFFQyxLQUFLLEVBQUU7RUFBUSxDQUFDLEVBQzlDO0lBQUVELEtBQUssRUFBRSxhQUFhO0lBQUVDLEtBQUssRUFBRTtFQUFJLENBQUMsRUFDcEM7SUFBRUQsS0FBSyxFQUFFLGVBQWU7SUFBRUMsS0FBSyxFQUFFO0VBQU0sQ0FBQyxDQUN6QztFQUNEQyxZQUFZLEVBQUUsQ0FBQyxxQkFBcUIsRUFBRSx1QkFBdUIsRUFBRSxZQUFZLEVBQUUsY0FBYyxDQUFDO0VBQzVGQyxRQUFRLEVBQUUsQ0FBQyxtQkFBbUIsRUFBRSxhQUFhLEVBQUUsU0FBUztBQUMxRCxDQUFDLEVBQ0Q7RUFDRVYsRUFBRSxFQUFFLFdBQVc7RUFDZkMsS0FBSyxFQUFFLHdCQUF3QjtFQUMvQkMsUUFBUSxFQUFFLDBCQUEwQjtFQUNwQ0MsV0FBVyxFQUFFLDhGQUE4RjtFQUMzR0MsSUFBSSxFQUFFLElBQUk7RUFDVkMsS0FBSyxFQUFFLFNBQVM7RUFDaEJDLEtBQUssRUFBRSxDQUNMO0lBQUVDLEtBQUssRUFBRSx1QkFBdUI7SUFBRUMsS0FBSyxFQUFFO0VBQU0sQ0FBQyxFQUNoRDtJQUFFRCxLQUFLLEVBQUUsc0JBQXNCO0lBQUVDLEtBQUssRUFBRTtFQUFPLENBQUMsRUFDaEQ7SUFBRUQsS0FBSyxFQUFFLHNCQUFzQjtJQUFFQyxLQUFLLEVBQUU7RUFBUSxDQUFDLENBQ2xEO0VBQ0RDLFlBQVksRUFBRSxDQUFDLGtCQUFrQixFQUFFLHFCQUFxQixFQUFFLG1CQUFtQixFQUFFLFVBQVUsQ0FBQztFQUMxRkMsUUFBUSxFQUFFLENBQUMsZ0JBQWdCLEVBQUUsYUFBYSxFQUFFLFNBQVM7QUFDdkQsQ0FBQyxFQUNEO0VBQ0VWLEVBQUUsRUFBRSxNQUFNO0VBQ1ZDLEtBQUssRUFBRSxpQkFBaUI7RUFDeEJDLFFBQVEsRUFBRSw0QkFBNEI7RUFDdENDLFdBQVcsRUFBRSxrRkFBa0Y7RUFDL0ZDLElBQUksRUFBRSxJQUFJO0VBQ1ZDLEtBQUssRUFBRSxTQUFTO0VBQ2hCQyxLQUFLLEVBQUUsQ0FDTDtJQUFFQyxLQUFLLEVBQUUsa0JBQWtCO0lBQUVDLEtBQUssRUFBRTtFQUFJLENBQUMsRUFDekM7SUFBRUQsS0FBSyxFQUFFLHNCQUFzQjtJQUFFQyxLQUFLLEVBQUU7RUFBSyxDQUFDLEVBQzlDO0lBQUVELEtBQUssRUFBRSxlQUFlO0lBQUVDLEtBQUssRUFBRTtFQUFNLENBQUMsQ0FDekM7RUFDREMsWUFBWSxFQUFFLENBQUMsVUFBVSxFQUFFLFNBQVMsRUFBRSxNQUFNLEVBQUUsaUJBQWlCLEVBQUUsTUFBTSxDQUFDO0VBQ3hFQyxRQUFRLEVBQUUsQ0FBQyxtQkFBbUIsRUFBRSxpQkFBaUIsRUFBRSxpQkFBaUI7QUFDdEUsQ0FBQyxDQUNGO0FBRUQsTUFBTUMscUJBQTJELEdBQUdBLENBQUM7RUFBRUM7QUFBZSxDQUFDLEtBQUs7RUFDMUYsTUFBTSxDQUFDQyxhQUFhLEVBQUVDLGdCQUFnQixDQUFDLEdBQUd4QiwrQ0FBUSxDQUFDLENBQUMsQ0FBQztFQUNyRCxNQUFNLENBQUN5QixjQUFjLEVBQUVDLGlCQUFpQixDQUFDLEdBQUcxQiwrQ0FBUSxDQUFnQixJQUFJLENBQUM7RUFFekVDLGdEQUFTLENBQUMsTUFBTTtJQUNkLE1BQU0wQixRQUFRLEdBQUdDLFdBQVcsQ0FBQyxNQUFNO01BQ2pDSixnQkFBZ0IsQ0FBRUssSUFBSSxJQUFLLENBQUNBLElBQUksR0FBRyxDQUFDLElBQUlwQixRQUFRLENBQUNxQixNQUFNLENBQUM7SUFDMUQsQ0FBQyxFQUFFLElBQUksQ0FBQztJQUVSLE9BQU8sTUFBTUMsYUFBYSxDQUFDSixRQUFRLENBQUM7RUFDdEMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUVOLG9CQUNFckIsc0RBQUEsQ0FBQzBCLGlCQUFpQjtJQUFBQyxRQUFBLGVBQ2hCekIsdURBQUEsQ0FBQzBCLGNBQWM7TUFBQUQsUUFBQSxnQkFDYnpCLHVEQUFBLENBQUNOLGlEQUFNLENBQUNpQyxHQUFHO1FBQ1RDLE9BQU8sRUFBRTtVQUFFQyxPQUFPLEVBQUUsQ0FBQztVQUFFQyxDQUFDLEVBQUU7UUFBRyxDQUFFO1FBQy9CQyxPQUFPLEVBQUU7VUFBRUYsT0FBTyxFQUFFLENBQUM7VUFBRUMsQ0FBQyxFQUFFO1FBQUUsQ0FBRTtRQUM5QkUsVUFBVSxFQUFFO1VBQUVDLFFBQVEsRUFBRTtRQUFJLENBQUU7UUFBQVIsUUFBQSxnQkFFOUIzQixzREFBQSxDQUFDb0MsWUFBWTtVQUFBVCxRQUFBLEVBQUM7UUFBd0IsQ0FBYyxDQUFDLGVBQ3JEM0Isc0RBQUEsQ0FBQ3FDLGVBQWU7VUFBQVYsUUFBQSxFQUFDO1FBRWpCLENBQWlCLENBQUM7TUFBQSxDQUNSLENBQUMsZUFFYjNCLHNEQUFBLENBQUNzQyxZQUFZO1FBQUFYLFFBQUEsRUFDVnhCLFFBQVEsQ0FBQ29DLEdBQUcsQ0FBQyxDQUFDQyxPQUFPLEVBQUVDLEtBQUssa0JBQzNCdkMsdURBQUEsQ0FBQ3dDLFdBQVc7VUFFVkMsTUFBTSxFQUFFRixLQUFLLEtBQUt4QixhQUFjO1VBQ2hDMkIsT0FBTyxFQUFFekIsY0FBYyxLQUFLcUIsT0FBTyxDQUFDcEMsRUFBRztVQUN2Q0ssS0FBSyxFQUFFK0IsT0FBTyxDQUFDL0IsS0FBTTtVQUNyQm9DLFlBQVksRUFBRUEsQ0FBQSxLQUFNekIsaUJBQWlCLENBQUNvQixPQUFPLENBQUNwQyxFQUFFLENBQUU7VUFDbEQwQyxZQUFZLEVBQUVBLENBQUEsS0FBTTFCLGlCQUFpQixDQUFDLElBQUksQ0FBRTtVQUM1QzJCLE9BQU8sRUFBRUEsQ0FBQSxLQUFNN0IsZ0JBQWdCLENBQUN1QixLQUFLLENBQUU7VUFDdkNYLE9BQU8sRUFBRTtZQUFFQyxPQUFPLEVBQUUsQ0FBQztZQUFFaUIsS0FBSyxFQUFFO1VBQUksQ0FBRTtVQUNwQ2YsT0FBTyxFQUFFO1lBQUVGLE9BQU8sRUFBRSxDQUFDO1lBQUVpQixLQUFLLEVBQUU7VUFBRSxDQUFFO1VBQ2xDZCxVQUFVLEVBQUU7WUFBRUMsUUFBUSxFQUFFLEdBQUc7WUFBRWMsS0FBSyxFQUFFUixLQUFLLEdBQUc7VUFBSSxDQUFFO1VBQ2xEUyxVQUFVLEVBQUU7WUFBRUYsS0FBSyxFQUFFO1VBQUssQ0FBRTtVQUM1QkcsUUFBUSxFQUFFO1lBQUVILEtBQUssRUFBRTtVQUFLLENBQUU7VUFBQXJCLFFBQUEsZ0JBRTFCM0Isc0RBQUEsQ0FBQ29ELFdBQVc7WUFBQ1QsTUFBTSxFQUFFRixLQUFLLEtBQUt4QixhQUFjO1lBQUFVLFFBQUEsRUFDMUNhLE9BQU8sQ0FBQ2hDO1VBQUksQ0FDRixDQUFDLGVBQ2RSLHNEQUFBLENBQUNxRCxZQUFZO1lBQUExQixRQUFBLEVBQUVhLE9BQU8sQ0FBQ25DO1VBQUssQ0FBZSxDQUFDLGVBQzVDTCxzREFBQSxDQUFDc0QsZUFBZTtZQUFBM0IsUUFBQSxFQUFFYSxPQUFPLENBQUNsQztVQUFRLENBQWtCLENBQUMsZUFFckROLHNEQUFBLENBQUNILDBEQUFlO1lBQUE4QixRQUFBLEVBQ2IsQ0FBQ2MsS0FBSyxLQUFLeEIsYUFBYSxJQUFJRSxjQUFjLEtBQUtxQixPQUFPLENBQUNwQyxFQUFFLGtCQUN4REYsdURBQUEsQ0FBQ3FELGNBQWM7Y0FDYnpCLE9BQU8sRUFBRTtnQkFBRUMsT0FBTyxFQUFFLENBQUM7Z0JBQUV5QixNQUFNLEVBQUU7Y0FBRSxDQUFFO2NBQ25DdkIsT0FBTyxFQUFFO2dCQUFFRixPQUFPLEVBQUUsQ0FBQztnQkFBRXlCLE1BQU0sRUFBRTtjQUFPLENBQUU7Y0FDeENDLElBQUksRUFBRTtnQkFBRTFCLE9BQU8sRUFBRSxDQUFDO2dCQUFFeUIsTUFBTSxFQUFFO2NBQUUsQ0FBRTtjQUNoQ3RCLFVBQVUsRUFBRTtnQkFBRUMsUUFBUSxFQUFFO2NBQUksQ0FBRTtjQUFBUixRQUFBLGdCQUU5QjNCLHNEQUFBLENBQUMwRCxrQkFBa0I7Z0JBQUEvQixRQUFBLEVBQ2hCYSxPQUFPLENBQUNqQztjQUFXLENBQ0YsQ0FBQyxlQUVyQlAsc0RBQUEsQ0FBQzJELFNBQVM7Z0JBQUFoQyxRQUFBLEVBQ1BhLE9BQU8sQ0FBQzlCLEtBQUssQ0FBQzZCLEdBQUcsQ0FBQyxDQUFDcUIsSUFBSSxFQUFFQyxTQUFTLGtCQUNqQzNELHVEQUFBLENBQUM0RCxRQUFRO2tCQUFBbkMsUUFBQSxnQkFDUDNCLHNEQUFBLENBQUMrRCxTQUFTO29CQUFBcEMsUUFBQSxFQUFFaUMsSUFBSSxDQUFDaEQ7a0JBQUssQ0FBWSxDQUFDLGVBQ25DWixzREFBQSxDQUFDZ0UsU0FBUztvQkFBQXJDLFFBQUEsRUFBRWlDLElBQUksQ0FBQ2pEO2tCQUFLLENBQVksQ0FBQztnQkFBQSxHQUZ0QmtELFNBR0wsQ0FDWDtjQUFDLENBQ08sQ0FBQztZQUFBLENBQ0U7VUFDakIsQ0FDYyxDQUFDO1FBQUEsR0F6Q2JyQixPQUFPLENBQUNwQyxFQTBDRixDQUNkO01BQUMsQ0FDVSxDQUFDLGVBR2ZKLHNEQUFBLENBQUNILDBEQUFlO1FBQUNvRSxJQUFJLEVBQUMsTUFBTTtRQUFBdEMsUUFBQSxlQUMxQjNCLHNEQUFBLENBQUNrRSxZQUFZO1VBRVhwQyxPQUFPLEVBQUU7WUFBRUMsT0FBTyxFQUFFLENBQUM7WUFBRW9DLENBQUMsRUFBRTtVQUFHLENBQUU7VUFDL0JsQyxPQUFPLEVBQUU7WUFBRUYsT0FBTyxFQUFFLENBQUM7WUFBRW9DLENBQUMsRUFBRTtVQUFFLENBQUU7VUFDOUJWLElBQUksRUFBRTtZQUFFMUIsT0FBTyxFQUFFLENBQUM7WUFBRW9DLENBQUMsRUFBRSxDQUFDO1VBQUcsQ0FBRTtVQUM3QmpDLFVBQVUsRUFBRTtZQUFFQyxRQUFRLEVBQUU7VUFBSSxDQUFFO1VBQUFSLFFBQUEsZUFFOUJ6Qix1REFBQSxDQUFDa0UsZUFBZTtZQUFBekMsUUFBQSxnQkFDZHpCLHVEQUFBLENBQUNtRSxjQUFjO2NBQUExQyxRQUFBLGdCQUNiM0Isc0RBQUEsQ0FBQ3NFLFlBQVk7Z0JBQUM3RCxLQUFLLEVBQUVOLFFBQVEsQ0FBQ2MsYUFBYSxDQUFDLENBQUNSLEtBQU07Z0JBQUFrQixRQUFBLEVBQ2hEeEIsUUFBUSxDQUFDYyxhQUFhLENBQUMsQ0FBQ1Q7Y0FBSSxDQUNqQixDQUFDLGVBQ2ZOLHVEQUFBO2dCQUFBeUIsUUFBQSxnQkFDRTNCLHNEQUFBLENBQUN1RSxhQUFhO2tCQUFBNUMsUUFBQSxFQUFFeEIsUUFBUSxDQUFDYyxhQUFhLENBQUMsQ0FBQ1o7Z0JBQUssQ0FBZ0IsQ0FBQyxlQUM5REwsc0RBQUEsQ0FBQ3dFLGdCQUFnQjtrQkFBQTdDLFFBQUEsRUFBRXhCLFFBQVEsQ0FBQ2MsYUFBYSxDQUFDLENBQUNYO2dCQUFRLENBQW1CLENBQUM7Y0FBQSxDQUNwRSxDQUFDO1lBQUEsQ0FDUSxDQUFDLGVBRWpCTixzREFBQSxDQUFDeUUsbUJBQW1CO2NBQUE5QyxRQUFBLEVBQ2pCeEIsUUFBUSxDQUFDYyxhQUFhLENBQUMsQ0FBQ1Y7WUFBVyxDQUNqQixDQUFDLGVBRXRCTCx1REFBQSxDQUFDd0UsbUJBQW1CO2NBQUEvQyxRQUFBLGdCQUNsQjNCLHNEQUFBLENBQUMyRSxZQUFZO2dCQUFBaEQsUUFBQSxFQUFDO2NBQVcsQ0FBYyxDQUFDLGVBQ3hDM0Isc0RBQUEsQ0FBQzRFLGdCQUFnQjtnQkFBQWpELFFBQUEsRUFDZHhCLFFBQVEsQ0FBQ2MsYUFBYSxDQUFDLENBQUNKLFlBQVksQ0FBQzBCLEdBQUcsQ0FBQyxDQUFDc0MsSUFBSSxFQUFFcEMsS0FBSyxrQkFDcER6QyxzREFBQSxDQUFDOEUsT0FBTztrQkFFTmhELE9BQU8sRUFBRTtvQkFBRUMsT0FBTyxFQUFFLENBQUM7b0JBQUVpQixLQUFLLEVBQUU7a0JBQUUsQ0FBRTtrQkFDbENmLE9BQU8sRUFBRTtvQkFBRUYsT0FBTyxFQUFFLENBQUM7b0JBQUVpQixLQUFLLEVBQUU7a0JBQUUsQ0FBRTtrQkFDbENkLFVBQVUsRUFBRTtvQkFBRWUsS0FBSyxFQUFFUixLQUFLLEdBQUc7a0JBQUksQ0FBRTtrQkFBQWQsUUFBQSxFQUVsQ2tEO2dCQUFJLEdBTEFwQyxLQU1FLENBQ1Y7Y0FBQyxDQUNjLENBQUM7WUFBQSxDQUNBLENBQUMsZUFFdEJ2Qyx1REFBQSxDQUFDNkUsZUFBZTtjQUFBcEQsUUFBQSxnQkFDZDNCLHNEQUFBLENBQUMyRSxZQUFZO2dCQUFBaEQsUUFBQSxFQUFDO2NBQWEsQ0FBYyxDQUFDLGVBQzFDM0Isc0RBQUEsQ0FBQ2dGLFlBQVk7Z0JBQUFyRCxRQUFBLEVBQ1Z4QixRQUFRLENBQUNjLGFBQWEsQ0FBQyxDQUFDSCxRQUFRLENBQUN5QixHQUFHLENBQUMsQ0FBQzBDLE9BQU8sRUFBRXhDLEtBQUssa0JBQ25EdkMsdURBQUEsQ0FBQ2dGLFdBQVc7a0JBRVZwRCxPQUFPLEVBQUU7b0JBQUVDLE9BQU8sRUFBRSxDQUFDO29CQUFFb0MsQ0FBQyxFQUFFLENBQUM7a0JBQUcsQ0FBRTtrQkFDaENsQyxPQUFPLEVBQUU7b0JBQUVGLE9BQU8sRUFBRSxDQUFDO29CQUFFb0MsQ0FBQyxFQUFFO2tCQUFFLENBQUU7a0JBQzlCakMsVUFBVSxFQUFFO29CQUFFZSxLQUFLLEVBQUVSLEtBQUssR0FBRztrQkFBSSxDQUFFO2tCQUFBZCxRQUFBLEdBQ3BDLFNBQ0csRUFBQ3NELE9BQU87Z0JBQUEsR0FMTHhDLEtBTU0sQ0FDZDtjQUFDLENBQ1UsQ0FBQztZQUFBLENBQ0EsQ0FBQztVQUFBLENBQ0g7UUFBQyxHQXBEYnhCLGFBcURPO01BQUMsQ0FDQSxDQUFDLGVBR2xCakIsc0RBQUEsQ0FBQ21GLGtCQUFrQjtRQUFBeEQsUUFBQSxFQUNoQnhCLFFBQVEsQ0FBQ29DLEdBQUcsQ0FBQyxDQUFDNkMsQ0FBQyxFQUFFM0MsS0FBSyxrQkFDckJ6QyxzREFBQSxDQUFDcUYsV0FBVztVQUVWMUMsTUFBTSxFQUFFRixLQUFLLEtBQUt4QixhQUFjO1VBQ2hDOEIsT0FBTyxFQUFFQSxDQUFBLEtBQU03QixnQkFBZ0IsQ0FBQ3VCLEtBQUssQ0FBRTtVQUN2Q1MsVUFBVSxFQUFFO1lBQUVGLEtBQUssRUFBRTtVQUFJLENBQUU7VUFDM0JHLFFBQVEsRUFBRTtZQUFFSCxLQUFLLEVBQUU7VUFBSTtRQUFFLEdBSnBCUCxLQUtOLENBQ0Y7TUFBQyxDQUNnQixDQUFDO0lBQUEsQ0FDUDtFQUFDLENBQ0EsQ0FBQztBQUV4QixDQUFDOztBQUVEO0FBQ0EsTUFBTWYsaUJBQWlCLEdBQUc1Qiw4REFBVztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTThCLGNBQWMsR0FBRzlCLDhEQUFXO0FBQ2xDO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTXNDLFlBQVksR0FBR3RDLDZEQUFVO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTXVDLGVBQWUsR0FBR3ZDLDREQUFTO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU13QyxZQUFZLEdBQUd4Qyw4REFBVztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFFRCxNQUFNNEMsV0FBVyxHQUFHNUMsd0RBQU0sQ0FBQ0YsaURBQU0sQ0FBQ2lDLEdBQUcsQ0FBd0Q7QUFDN0YsZ0JBQWdCMkQsS0FBSyxJQUFJQSxLQUFLLENBQUM3QyxNQUFNLElBQUk2QyxLQUFLLENBQUM1QyxPQUFPLEdBQy9DLDJCQUEwQjRDLEtBQUssQ0FBQy9FLEtBQU0sT0FBTStFLEtBQUssQ0FBQy9FLEtBQU0sS0FBSSxHQUM3RCwyQkFBNEI7QUFDbEMsc0JBQXNCK0UsS0FBSyxJQUFJQSxLQUFLLENBQUM3QyxNQUFNLElBQUk2QyxLQUFLLENBQUM1QyxPQUFPLEdBQUc0QyxLQUFLLENBQUMvRSxLQUFLLEdBQUcsMEJBQTJCO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQitFLEtBQUssSUFBSywyQkFBMEJBLEtBQUssQ0FBQy9FLEtBQU0sa0JBQWtCO0FBQ3BGLGVBQWUrRSxLQUFLLElBQUlBLEtBQUssQ0FBQzdDLE1BQU0sSUFBSTZDLEtBQUssQ0FBQzVDLE9BQU8sR0FBRyxDQUFDLEdBQUcsQ0FBRTtBQUM5RDtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU1RLFdBQVcsR0FBR3RELDhEQUFnQztBQUNwRDtBQUNBO0FBQ0E7QUFDQSxZQUFZMEYsS0FBSyxJQUFJQSxLQUFLLENBQUM3QyxNQUFNLEdBQUcsb0NBQW9DLEdBQUcsTUFBTztBQUNsRjtBQUNBLENBQUM7QUFFRCxNQUFNVSxZQUFZLEdBQUd2RCw2REFBVTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU13RCxlQUFlLEdBQUd4RCw0REFBUztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFFRCxNQUFNeUQsY0FBYyxHQUFHekQsd0RBQU0sQ0FBQ0YsaURBQU0sQ0FBQ2lDLEdBQUcsQ0FBRTtBQUMxQztBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU02QixrQkFBa0IsR0FBRzVELDREQUFTO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU02RCxTQUFTLEdBQUc3RCw4REFBVztBQUM3QjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTWdFLFFBQVEsR0FBR2hFLDhEQUFXO0FBQzVCO0FBQ0EsQ0FBQztBQUVELE1BQU1pRSxTQUFTLEdBQUdqRSw4REFBVztBQUM3QjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTWtFLFNBQVMsR0FBR2xFLDhEQUFXO0FBQzdCO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTW9FLFlBQVksR0FBR3BFLHdEQUFNLENBQUNGLGlEQUFNLENBQUNpQyxHQUFHLENBQUU7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU11QyxlQUFlLEdBQUd0RSw4REFBVyxFQUFDO0FBRXBDLE1BQU11RSxjQUFjLEdBQUd2RSw4REFBVztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFFRCxNQUFNd0UsWUFBWSxHQUFHeEUsOERBQThCO0FBQ25EO0FBQ0EsV0FBVzBGLEtBQUssSUFBSUEsS0FBSyxDQUFDL0UsS0FBTTtBQUNoQyxpQ0FBaUMrRSxLQUFLLElBQUlBLEtBQUssQ0FBQy9FLEtBQU07QUFDdEQsQ0FBQztBQUVELE1BQU04RCxhQUFhLEdBQUd6RSw2REFBVTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFFRCxNQUFNMEUsZ0JBQWdCLEdBQUcxRSw0REFBUztBQUNsQztBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU0yRSxtQkFBbUIsR0FBRzNFLDREQUFTO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU00RSxtQkFBbUIsR0FBRzVFLDhEQUFXO0FBQ3ZDO0FBQ0EsQ0FBQztBQUVELE1BQU1pRixlQUFlLEdBQUdqRiw4REFBVyxFQUFDO0FBRXBDLE1BQU02RSxZQUFZLEdBQUc3RSw2REFBVTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFFRCxNQUFNOEUsZ0JBQWdCLEdBQUc5RSw4REFBVztBQUNwQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTWdGLE9BQU8sR0FBR2hGLHdEQUFNLENBQUNGLGlEQUFNLENBQUMrRixJQUFJLENBQUU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTVgsWUFBWSxHQUFHbEYsOERBQVc7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU1vRixXQUFXLEdBQUdwRix3REFBTSxDQUFDRixpREFBTSxDQUFDaUMsR0FBRyxDQUFFO0FBQ3ZDO0FBQ0E7QUFDQSxDQUFDO0FBRUQsTUFBTXNELGtCQUFrQixHQUFHckYsOERBQVc7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUVELE1BQU11RixXQUFXLEdBQUd2Rix3REFBTSxDQUFDRixpREFBTSxDQUFDaUMsR0FBRyxDQUF1QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IyRCxLQUFLLElBQUlBLEtBQUssQ0FBQzdDLE1BQU0sR0FBRyxTQUFTLEdBQUcsMEJBQTJCO0FBQy9FO0FBQ0E7QUFDQSxDQUFDO0FBRUQsaUVBQWU1QixxQkFBcUIsRSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi9zcmMvY29tcG9uZW50cy9SZXZvbHV0aW9uYXJ5RmVhdHVyZXMudHN4P2Q4MGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdzdHlsZWQtY29tcG9uZW50cyc7XG5cbmludGVyZmFjZSBSZXZvbHV0aW9uYXJ5RmVhdHVyZXNQcm9wcyB7XG4gIGN1cnJlbnRTZWN0aW9uOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBGZWF0dXJlIHtcbiAgaWQ6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgc3VidGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgaWNvbjogc3RyaW5nO1xuICBjb2xvcjogc3RyaW5nO1xuICBzdGF0czogeyBsYWJlbDogc3RyaW5nOyB2YWx1ZTogc3RyaW5nIH1bXTtcbiAgdGVjaG5vbG9naWVzOiBzdHJpbmdbXTtcbiAgYmVuZWZpdHM6IHN0cmluZ1tdO1xufVxuXG5jb25zdCBmZWF0dXJlczogRmVhdHVyZVtdID0gW1xuICB7XG4gICAgaWQ6ICdxdWFudHVtJyxcbiAgICB0aXRsZTogJ9Ca0LLQsNC90YLQvtCy0YvQuSDQuNCz0YDQvtCy0L7QuSDQtNCy0LjQttC+0LonLFxuICAgIHN1YnRpdGxlOiAn0JjRgdGC0LjQvdC90LDRjyDRgdC70YPRh9Cw0LnQvdC+0YHRgtGMINC40Lcg0LrQstCw0L3RgtC+0LLRi9GFINC40YHRgtC+0YfQvdC40LrQvtCyJyxcbiAgICBkZXNjcmlwdGlvbjogJ9Cf0LXRgNCy0LDRjyDQsiDQvNC40YDQtSDQuNCz0YDQvtCy0LDRjyDQv9C70LDRgtGE0L7RgNC80LAsINC40YHQv9C+0LvRjNC30YPRjtGJ0LDRjyDRgNC10LDQu9GM0L3Ri9C1INC60LLQsNC90YLQvtCy0YvQtSDQuNGB0YLQvtGH0L3QuNC60Lgg0YHQu9GD0YfQsNC50L3QvtGB0YLQuCDQtNC70Y8g0LDQsdGB0L7Qu9GO0YLQvdC+0Lkg0YfQtdGB0YLQvdC+0YHRgtC4INC40LPRgC4nLFxuICAgIGljb246ICfimpvvuI8nLFxuICAgIGNvbG9yOiAnIzRhOTBlMicsXG4gICAgc3RhdHM6IFtcbiAgICAgIHsgbGFiZWw6ICfQrdC90YLRgNC+0L/QuNGPJywgdmFsdWU6ICc5OS45OSUnIH0sXG4gICAgICB7IGxhYmVsOiAn0JrQstCw0L3RgtC+0LLRi9GFINC40YHRgtC+0YfQvdC40LrQvtCyJywgdmFsdWU6ICc1JyB9LFxuICAgICAgeyBsYWJlbDogJ9Ci0LXRgdGC0L7QsiDQutCw0YfQtdGB0YLQstCwJywgdmFsdWU6ICcxNSsnIH1cbiAgICBdLFxuICAgIHRlY2hub2xvZ2llczogWydBTlUgUXVhbnR1bSBSTkcnLCAnSUQgUXVhbnRpcXVlJywgJ1BpY29RdWFudCcsICdRdWFudHVtIERpY2UnLCAnTklTVCBUZXN0cyddLFxuICAgIGJlbmVmaXRzOiBbJ9CQ0LHRgdC+0LvRjtGC0L3QsNGPINGH0LXRgdGC0L3QvtGB0YLRjCcsICfQndC10LLQvtC30LzQvtC20L3QvtGB0YLRjCDQv9GA0LXQtNGB0LrQsNC30LDQvdC40Y8nLCAn0J3QsNGD0YfQvdCw0Y8g0LLQsNC70LjQtNCw0YbQuNGPJ11cbiAgfSxcbiAge1xuICAgIGlkOiAnYWknLFxuICAgIHRpdGxlOiAn0K3QvNC+0YbQuNC+0L3QsNC70YzQvdGL0Lkg0JjQmCcsXG4gICAgc3VidGl0bGU6ICfQk9C70YPQsdC+0LrQvtC1INC/0L7QvdC40LzQsNC90LjQtSDQutCw0LbQtNC+0LPQviDQuNCz0YDQvtC60LAnLFxuICAgIGRlc2NyaXB0aW9uOiAn0KDQtdCy0L7Qu9GO0YbQuNC+0L3QvdCw0Y8g0YHQuNGB0YLQtdC80LAg0LjRgdC60YPRgdGB0YLQstC10L3QvdC+0LPQviDQuNC90YLQtdC70LvQtdC60YLQsCwg0LDQvdCw0LvQuNC30LjRgNGD0Y7RidCw0Y8g0Y3QvNC+0YbQuNC+0L3QsNC70YzQvdC+0LUg0YHQvtGB0YLQvtGP0L3QuNC1INC4INC/0LXRgNGB0L7QvdCw0LvQuNC30LjRgNGD0Y7RidCw0Y8g0LjQs9GA0L7QstC+0Lkg0L7Qv9GL0YIuJyxcbiAgICBpY29uOiAn8J+noCcsXG4gICAgY29sb3I6ICcjN2I2OGVlJyxcbiAgICBzdGF0czogW1xuICAgICAgeyBsYWJlbDogJ9Ct0LzQvtGG0LjQvtC90LDQu9GM0L3Ri9GFINGB0L7RgdGC0L7Rj9C90LjQuScsIHZhbHVlOiAnOCcgfSxcbiAgICAgIHsgbGFiZWw6ICfQotC+0YfQvdC+0YHRgtGMINCw0L3QsNC70LjQt9CwJywgdmFsdWU6ICc5NSUnIH0sXG4gICAgICB7IGxhYmVsOiAnTUwg0LzQvtC00LXQu9C10LknLCB2YWx1ZTogJzYnIH1cbiAgICBdLFxuICAgIHRlY2hub2xvZ2llczogWydUZW5zb3JGbG93JywgJ09wZW5BSSBHUFQtNCcsICdDb21wdXRlciBWaXNpb24nLCAnTkxQJywgJ0JlaGF2aW9yYWwgQW5hbHlzaXMnXSxcbiAgICBiZW5lZml0czogWyfQn9C10YDRgdC+0L3QsNC70LjQt9Cw0YbQuNGPJywgJ9Cf0YDQtdC00L7RgtCy0YDQsNGJ0LXQvdC40LUg0YLQuNC70YzRgtCwJywgJ9CQ0LTQsNC/0YLQuNCy0L3QvtC1INC+0LHRg9GH0LXQvdC40LUnXVxuICB9LFxuICB7XG4gICAgaWQ6ICdtZXRhdmVyc2UnLFxuICAgIHRpdGxlOiAnM0Qg0JzQtdGC0LDQstGB0LXQu9C10L3QvdCw0Y8nLFxuICAgIHN1YnRpdGxlOiAn0JjQvNC80LXRgNGB0LjQstC90YvQtSDQuNCz0YDQvtCy0YvQtSDQvNC40YDRiycsXG4gICAgZGVzY3JpcHRpb246ICfQn9C+0LvQvdC+0YbQtdC90L3QsNGPINC80LXRgtCw0LLRgdC10LvQtdC90L3QsNGPINGBIDNEINC80LjRgNCw0LzQuCwgVlIvQVIg0L/QvtC00LTQtdGA0LbQutC+0Lkg0Lgg0YHQvtGG0LjQsNC70YzQvdGL0LzQuCDQv9GA0L7RgdGC0YDQsNC90YHRgtCy0LDQvNC4INC00LvRjyDRgNC10LLQvtC70Y7RhtC40L7QvdC90L7Qs9C+INC40LPRgNC+0LLQvtCz0L4g0L7Qv9GL0YLQsC4nLFxuICAgIGljb246ICfwn4yNJyxcbiAgICBjb2xvcjogJyM5MzcwZGInLFxuICAgIHN0YXRzOiBbXG4gICAgICB7IGxhYmVsOiAn0KLQtdC80LDRgtC40YfQtdGB0LrQuNGFINC80LjRgNC+0LInLCB2YWx1ZTogJzYnIH0sXG4gICAgICB7IGxhYmVsOiAn0J7QtNC90L7QstGA0LXQvNC10L3QvdGL0YUg0LjQs9GA0L7QutC+0LInLCB2YWx1ZTogJzEwSysnIH0sXG4gICAgICB7IGxhYmVsOiAnVlIvQVIg0L/QvtC00LTQtdGA0LbQutCwJywgdmFsdWU6ICcxMDAlJyB9XG4gICAgXSxcbiAgICB0ZWNobm9sb2dpZXM6IFsnVGhyZWUuanMnLCAnV2ViWFInLCAnV2ViR0wnLCAnUGh5c2ljcyBFbmdpbmUnLCAnU3BhdGlhbCBBdWRpbyddLFxuICAgIGJlbmVmaXRzOiBbJ9CY0LzQvNC10YDRgdC40LLQvdC+0YHRgtGMJywgJ9Ch0L7RhtC40LDQu9GM0L3QvtC1INCy0LfQsNC40LzQvtC00LXQudGB0YLQstC40LUnLCAn0J3QvtCy0YvQuSDQvtC/0YvRgiddXG4gIH0sXG4gIHtcbiAgICBpZDogJ3NlY3VyaXR5JyxcbiAgICB0aXRsZTogJ9Ca0LLQsNC90YLQvtCy0LDRjyDQsdC10LfQvtC/0LDRgdC90L7RgdGC0YwnLFxuICAgIHN1YnRpdGxlOiAn0J3QtdC/0YDQvtCx0LjQstCw0LXQvNCw0Y8g0LfQsNGJ0LjRgtCwJyxcbiAgICBkZXNjcmlwdGlvbjogJ9Cf0LXRgNC10LTQvtCy0YvQtSDRgdC40YHRgtC10LzRiyDQsdC10LfQvtC/0LDRgdC90L7RgdGC0Lgg0YEg0LrQstCw0L3RgtC+0LLRi9C8INGI0LjRhNGA0L7QstCw0L3QuNC10LwsINCx0LjQvtC80LXRgtGA0LjQtdC5INC4INCY0Jgt0LTQtdGC0LXQutGG0LjQtdC5INGD0LPRgNC+0LcuJyxcbiAgICBpY29uOiAn8J+boe+4jycsXG4gICAgY29sb3I6ICcjZmY2YjZiJyxcbiAgICBzdGF0czogW1xuICAgICAgeyBsYWJlbDogJ9Ci0L7Rh9C90L7RgdGC0Ywg0LTQtdGC0LXQutGG0LjQuCcsIHZhbHVlOiAnOTkuOSUnIH0sXG4gICAgICB7IGxhYmVsOiAn0KLQuNC/0L7QsiDRg9Cz0YDQvtC3JywgdmFsdWU6ICc2JyB9LFxuICAgICAgeyBsYWJlbDogJ9CS0YDQtdC80Y8g0YDQtdCw0LrRhtC40LgnLCB2YWx1ZTogJzwx0YEnIH1cbiAgICBdLFxuICAgIHRlY2hub2xvZ2llczogWydQb3N0LVF1YW50dW0gQ3J5cHRvJywgJ1plcm8tS25vd2xlZGdlIFByb29mcycsICdCaW9tZXRyaWNzJywgJ0FJIERldGVjdGlvbiddLFxuICAgIGJlbmVmaXRzOiBbJ9CQ0LHRgdC+0LvRjtGC0L3QsNGPINC30LDRidC40YLQsCcsICfQn9GA0LjQstCw0YLQvdC+0YHRgtGMJywgJ9CU0L7QstC10YDQuNC1J11cbiAgfSxcbiAge1xuICAgIGlkOiAnYW5hbHl0aWNzJyxcbiAgICB0aXRsZTogJ9Cf0YDQtdC00LjQutGC0LjQstC90LDRjyDQsNC90LDQu9C40YLQuNC60LAnLFxuICAgIHN1YnRpdGxlOiAn0JjQmCDQv9GA0LXQtNGB0LrQsNC30YvQstCw0LXRgiDQsdGD0LTRg9GJ0LXQtScsXG4gICAgZGVzY3JpcHRpb246ICfQnNC+0YnQvdCw0Y8g0YHQuNGB0YLQtdC80LAg0LzQsNGI0LjQvdC90L7Qs9C+INC+0LHRg9GH0LXQvdC40Y8sINC/0YDQtdC00YHQutCw0LfRi9Cy0LDRjtGJ0LDRjyDQuNCz0YDQvtCy0YvQtSDRgdC+0LHRi9GC0LjRjyDQuCDQv9C10YDRgdC+0L3QsNC70LjQt9C40YDRg9GO0YnQsNGPINC+0L/Ri9GCLicsXG4gICAgaWNvbjogJ/Cfk4onLFxuICAgIGNvbG9yOiAnIzRlY2RjNCcsXG4gICAgc3RhdHM6IFtcbiAgICAgIHsgbGFiZWw6ICfQotC+0YfQvdC+0YHRgtGMINC/0YDQtdC00YHQutCw0LfQsNC90LjQuScsIHZhbHVlOiAnODUlJyB9LFxuICAgICAgeyBsYWJlbDogJ9CQ0L3QsNC70LjQt9C40YDRg9C10LzRi9GFINC80LXRgtGA0LjQuicsIHZhbHVlOiAnMTAwKycgfSxcbiAgICAgIHsgbGFiZWw6ICfQntCx0L3QvtCy0LvQtdC90LjQuSDQsiDRgdC10LrRg9C90LTRgycsIHZhbHVlOiAnMTAwMCsnIH1cbiAgICBdLFxuICAgIHRlY2hub2xvZ2llczogWydNYWNoaW5lIExlYXJuaW5nJywgJ1JlYWwtdGltZSBBbmFseXRpY3MnLCAnUHJlZGljdGl2ZSBNb2RlbHMnLCAnQmlnIERhdGEnXSxcbiAgICBiZW5lZml0czogWyfQn9C10YDRgdC+0L3QsNC70LjQt9Cw0YbQuNGPJywgJ9Ce0L/RgtC40LzQuNC30LDRhtC40Y8nLCAn0JjQvdGB0LDQudGC0YsnXVxuICB9LFxuICB7XG4gICAgaWQ6ICd3ZWIzJyxcbiAgICB0aXRsZTogJ1dlYjMg0Y3QutC+0YHQuNGB0YLQtdC80LAnLFxuICAgIHN1YnRpdGxlOiAn0JTQtdGG0LXQvdGC0YDQsNC70LjQt9C+0LLQsNC90L3QvtC1INCx0YPQtNGD0YnQtdC1JyxcbiAgICBkZXNjcmlwdGlvbjogJ9Cf0L7Qu9C90LDRjyDQuNC90YLQtdCz0YDQsNGG0LjRjyDRgSDQsdC70L7QutGH0LXQudC90L7QvCwgTkZUINC60LDRgNGC0LDQvNC4LCBEZUZpINC/0YDQvtGC0L7QutC+0LvQsNC80Lgg0LggREFPINGD0L/RgNCw0LLQu9C10L3QuNC10LwuJyxcbiAgICBpY29uOiAn4puT77iPJyxcbiAgICBjb2xvcjogJyNmZWNhNTcnLFxuICAgIHN0YXRzOiBbXG4gICAgICB7IGxhYmVsOiAn0KHQvNCw0YDRgi3QutC+0L3RgtGA0LDQutGC0L7QsicsIHZhbHVlOiAnNicgfSxcbiAgICAgIHsgbGFiZWw6ICfQn9C+0LTQtNC10YDQttC40LLQsNC10LzRi9GFINGB0LXRgtC10LknLCB2YWx1ZTogJzUrJyB9LFxuICAgICAgeyBsYWJlbDogJ05GVCDQutC+0LvQu9C10LrRhtC40LknLCB2YWx1ZTogJzEwKycgfVxuICAgIF0sXG4gICAgdGVjaG5vbG9naWVzOiBbJ0V0aGVyZXVtJywgJ1BvbHlnb24nLCAnSVBGUycsICdTbWFydCBDb250cmFjdHMnLCAnRGVGaSddLFxuICAgIGJlbmVmaXRzOiBbJ9CS0LvQsNC00LXQvdC40LUg0LDQutGC0LjQstCw0LzQuCcsICfQlNC10YbQtdC90YLRgNCw0LvQuNC30LDRhtC40Y8nLCAn0J3QvtCy0LDRjyDRjdC60L7QvdC+0LzQuNC60LAnXVxuICB9XG5dO1xuXG5jb25zdCBSZXZvbHV0aW9uYXJ5RmVhdHVyZXM6IFJlYWN0LkZDPFJldm9sdXRpb25hcnlGZWF0dXJlc1Byb3BzPiA9ICh7IGN1cnJlbnRTZWN0aW9uIH0pID0+IHtcbiAgY29uc3QgW2FjdGl2ZUZlYXR1cmUsIHNldEFjdGl2ZUZlYXR1cmVdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtob3ZlcmVkRmVhdHVyZSwgc2V0SG92ZXJlZEZlYXR1cmVdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldEFjdGl2ZUZlYXR1cmUoKHByZXYpID0+IChwcmV2ICsgMSkgJSBmZWF0dXJlcy5sZW5ndGgpO1xuICAgIH0sIDUwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8RmVhdHVyZXNDb250YWluZXI+XG4gICAgICA8Q29udGVudFdyYXBwZXI+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiA1MCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICA+XG4gICAgICAgICAgPFNlY3Rpb25UaXRsZT7QoNC10LLQvtC70Y7RhtC40L7QvdC90YvQtSDRgtC10YXQvdC+0LvQvtCz0LjQuDwvU2VjdGlvblRpdGxlPlxuICAgICAgICAgIDxTZWN0aW9uU3VidGl0bGU+XG4gICAgICAgICAgICDQnNGLINC+0LHRitC10LTQuNC90LjQu9C4INC/0LXRgNC10LTQvtCy0YvQtSDRgtC10YXQvdC+0LvQvtCz0LjQuCDQsdGD0LTRg9GJ0LXQs9C+INCyINC+0LTQvdC+0Lkg0L/Qu9Cw0YLRhNC+0YDQvNC1XG4gICAgICAgICAgPC9TZWN0aW9uU3VidGl0bGU+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICA8RmVhdHVyZXNHcmlkPlxuICAgICAgICAgIHtmZWF0dXJlcy5tYXAoKGZlYXR1cmUsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8RmVhdHVyZUNhcmRcbiAgICAgICAgICAgICAga2V5PXtmZWF0dXJlLmlkfVxuICAgICAgICAgICAgICBhY3RpdmU9e2luZGV4ID09PSBhY3RpdmVGZWF0dXJlfVxuICAgICAgICAgICAgICBob3ZlcmVkPXtob3ZlcmVkRmVhdHVyZSA9PT0gZmVhdHVyZS5pZH1cbiAgICAgICAgICAgICAgY29sb3I9e2ZlYXR1cmUuY29sb3J9XG4gICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KCkgPT4gc2V0SG92ZXJlZEZlYXR1cmUoZmVhdHVyZS5pZCl9XG4gICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0SG92ZXJlZEZlYXR1cmUobnVsbCl9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZUZlYXR1cmUoaW5kZXgpfVxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHNjYWxlOiAwLjggfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxGZWF0dXJlSWNvbiBhY3RpdmU9e2luZGV4ID09PSBhY3RpdmVGZWF0dXJlfT5cbiAgICAgICAgICAgICAgICB7ZmVhdHVyZS5pY29ufVxuICAgICAgICAgICAgICA8L0ZlYXR1cmVJY29uPlxuICAgICAgICAgICAgICA8RmVhdHVyZVRpdGxlPntmZWF0dXJlLnRpdGxlfTwvRmVhdHVyZVRpdGxlPlxuICAgICAgICAgICAgICA8RmVhdHVyZVN1YnRpdGxlPntmZWF0dXJlLnN1YnRpdGxlfTwvRmVhdHVyZVN1YnRpdGxlPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICAgICAgICB7KGluZGV4ID09PSBhY3RpdmVGZWF0dXJlIHx8IGhvdmVyZWRGZWF0dXJlID09PSBmZWF0dXJlLmlkKSAmJiAoXG4gICAgICAgICAgICAgICAgICA8RmVhdHVyZURldGFpbHNcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBoZWlnaHQ6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIGhlaWdodDogMCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEZlYXR1cmVEZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9GZWF0dXJlRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICA8U3RhdHNHcmlkPlxuICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLnN0YXRzLm1hcCgoc3RhdCwgc3RhdEluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8U3RhdEl0ZW0ga2V5PXtzdGF0SW5kZXh9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RhdFZhbHVlPntzdGF0LnZhbHVlfTwvU3RhdFZhbHVlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RhdExhYmVsPntzdGF0LmxhYmVsfTwvU3RhdExhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TdGF0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9TdGF0c0dyaWQ+XG4gICAgICAgICAgICAgICAgICA8L0ZlYXR1cmVEZXRhaWxzPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAgPC9GZWF0dXJlQ2FyZD5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9GZWF0dXJlc0dyaWQ+XG5cbiAgICAgICAgey8qINCU0LXRgtCw0LvRjNC90LDRjyDQuNC90YTQvtGA0LzQsNGG0LjRjyDQvtCxINCw0LrRgtC40LLQvdC+0Lkg0YTRg9C90LrRhtC40LggKi99XG4gICAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIj5cbiAgICAgICAgICA8RGV0YWlsZWRWaWV3XG4gICAgICAgICAgICBrZXk9e2FjdGl2ZUZlYXR1cmV9XG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDUwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeDogLTUwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RGV0YWlsZWRDb250ZW50PlxuICAgICAgICAgICAgICA8RGV0YWlsZWRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPERldGFpbGVkSWNvbiBjb2xvcj17ZmVhdHVyZXNbYWN0aXZlRmVhdHVyZV0uY29sb3J9PlxuICAgICAgICAgICAgICAgICAge2ZlYXR1cmVzW2FjdGl2ZUZlYXR1cmVdLmljb259XG4gICAgICAgICAgICAgICAgPC9EZXRhaWxlZEljb24+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxEZXRhaWxlZFRpdGxlPntmZWF0dXJlc1thY3RpdmVGZWF0dXJlXS50aXRsZX08L0RldGFpbGVkVGl0bGU+XG4gICAgICAgICAgICAgICAgICA8RGV0YWlsZWRTdWJ0aXRsZT57ZmVhdHVyZXNbYWN0aXZlRmVhdHVyZV0uc3VidGl0bGV9PC9EZXRhaWxlZFN1YnRpdGxlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0RldGFpbGVkSGVhZGVyPlxuXG4gICAgICAgICAgICAgIDxEZXRhaWxlZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIHtmZWF0dXJlc1thY3RpdmVGZWF0dXJlXS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgPC9EZXRhaWxlZERlc2NyaXB0aW9uPlxuXG4gICAgICAgICAgICAgIDxUZWNobm9sb2dpZXNTZWN0aW9uPlxuICAgICAgICAgICAgICAgIDxTZWN0aW9uTGFiZWw+0KLQtdGF0L3QvtC70L7Qs9C40Lg6PC9TZWN0aW9uTGFiZWw+XG4gICAgICAgICAgICAgICAgPFRlY2hub2xvZ2llc0xpc3Q+XG4gICAgICAgICAgICAgICAgICB7ZmVhdHVyZXNbYWN0aXZlRmVhdHVyZV0udGVjaG5vbG9naWVzLm1hcCgodGVjaCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPFRlY2hUYWdcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHNjYWxlOiAxIH19XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHt0ZWNofVxuICAgICAgICAgICAgICAgICAgICA8L1RlY2hUYWc+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L1RlY2hub2xvZ2llc0xpc3Q+XG4gICAgICAgICAgICAgIDwvVGVjaG5vbG9naWVzU2VjdGlvbj5cblxuICAgICAgICAgICAgICA8QmVuZWZpdHNTZWN0aW9uPlxuICAgICAgICAgICAgICAgIDxTZWN0aW9uTGFiZWw+0J/RgNC10LjQvNGD0YnQtdGB0YLQstCwOjwvU2VjdGlvbkxhYmVsPlxuICAgICAgICAgICAgICAgIDxCZW5lZml0c0xpc3Q+XG4gICAgICAgICAgICAgICAgICB7ZmVhdHVyZXNbYWN0aXZlRmVhdHVyZV0uYmVuZWZpdHMubWFwKChiZW5lZml0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8QmVuZWZpdEl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIOKcqCB7YmVuZWZpdH1cbiAgICAgICAgICAgICAgICAgICAgPC9CZW5lZml0SXRlbT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvQmVuZWZpdHNMaXN0PlxuICAgICAgICAgICAgICA8L0JlbmVmaXRzU2VjdGlvbj5cbiAgICAgICAgICAgIDwvRGV0YWlsZWRDb250ZW50PlxuICAgICAgICAgIDwvRGV0YWlsZWRWaWV3PlxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cblxuICAgICAgICB7Lyog0JjQvdC00LjQutCw0YLQvtGA0Ysg0L/RgNC+0LPRgNC10YHRgdCwICovfVxuICAgICAgICA8UHJvZ3Jlc3NJbmRpY2F0b3JzPlxuICAgICAgICAgIHtmZWF0dXJlcy5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8UHJvZ3Jlc3NEb3RcbiAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgYWN0aXZlPXtpbmRleCA9PT0gYWN0aXZlRmVhdHVyZX1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlRmVhdHVyZShpbmRleCl9XG4gICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMiB9fVxuICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45IH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L1Byb2dyZXNzSW5kaWNhdG9ycz5cbiAgICAgIDwvQ29udGVudFdyYXBwZXI+XG4gICAgPC9GZWF0dXJlc0NvbnRhaW5lcj5cbiAgKTtcbn07XG5cbi8vINCh0YLQuNC70LjQt9C+0LLQsNC90L3Ri9C1INC60L7QvNC/0L7QvdC10L3RgtGLXG5jb25zdCBGZWF0dXJlc0NvbnRhaW5lciA9IHN0eWxlZC5kaXZgXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMGYwZjIzIDAlLCAjMWExYTJlIDUwJSwgIzE2MjEzZSAxMDAlKTtcbiAgcGFkZGluZzogNHJlbSAycmVtO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbmA7XG5cbmNvbnN0IENvbnRlbnRXcmFwcGVyID0gc3R5bGVkLmRpdmBcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIHdpZHRoOiAxMDAlO1xuYDtcblxuY29uc3QgU2VjdGlvblRpdGxlID0gc3R5bGVkLmgyYFxuICBmb250LXNpemU6IDMuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDkwMDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM0YTkwZTIsICM3YjY4ZWUsICM5MzcwZGIpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBcbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgZm9udC1zaXplOiAyLjVyZW07XG4gIH1cbmA7XG5cbmNvbnN0IFNlY3Rpb25TdWJ0aXRsZSA9IHN0eWxlZC5wYFxuICBmb250LXNpemU6IDEuM3JlbTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiA0cmVtO1xuICBtYXgtd2lkdGg6IDgwMHB4O1xuICBtYXJnaW4tbGVmdDogYXV0bztcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xuYDtcblxuY29uc3QgRmVhdHVyZXNHcmlkID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzNTBweCwgMWZyKSk7XG4gIGdhcDogMnJlbTtcbiAgbWFyZ2luLWJvdHRvbTogNHJlbTtcbmA7XG5cbmNvbnN0IEZlYXR1cmVDYXJkID0gc3R5bGVkKG1vdGlvbi5kaXYpPHsgYWN0aXZlOiBib29sZWFuOyBob3ZlcmVkOiBib29sZWFuOyBjb2xvcjogc3RyaW5nIH0+YFxuICBiYWNrZ3JvdW5kOiAke3Byb3BzID0+IHByb3BzLmFjdGl2ZSB8fCBwcm9wcy5ob3ZlcmVkIFxuICAgID8gYGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICR7cHJvcHMuY29sb3J9MjAsICR7cHJvcHMuY29sb3J9MTApYCBcbiAgICA6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpJ307XG4gIGJvcmRlcjogMnB4IHNvbGlkICR7cHJvcHMgPT4gcHJvcHMuYWN0aXZlIHx8IHByb3BzLmhvdmVyZWQgPyBwcm9wcy5jb2xvciA6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknfTtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgcGFkZGluZzogMnJlbTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcblxuICAmOjpiZWZvcmUge1xuICAgIGNvbnRlbnQ6ICcnO1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgYmFja2dyb3VuZDogJHtwcm9wcyA9PiBgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHtwcm9wcy5jb2xvcn0xMCwgdHJhbnNwYXJlbnQpYH07XG4gICAgb3BhY2l0eTogJHtwcm9wcyA9PiBwcm9wcy5hY3RpdmUgfHwgcHJvcHMuaG92ZXJlZCA/IDEgOiAwfTtcbiAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcbiAgfVxuYDtcblxuY29uc3QgRmVhdHVyZUljb24gPSBzdHlsZWQuZGl2PHsgYWN0aXZlOiBib29sZWFuIH0+YFxuICBmb250LXNpemU6IDNyZW07XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgZmlsdGVyOiAke3Byb3BzID0+IHByb3BzLmFjdGl2ZSA/ICdkcm9wLXNoYWRvdygwIDAgMjBweCBjdXJyZW50Q29sb3IpJyA6ICdub25lJ307XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5gO1xuXG5jb25zdCBGZWF0dXJlVGl0bGUgPSBzdHlsZWQuaDNgXG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogd2hpdGU7XG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuYDtcblxuY29uc3QgRmVhdHVyZVN1YnRpdGxlID0gc3R5bGVkLnBgXG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuYDtcblxuY29uc3QgRmVhdHVyZURldGFpbHMgPSBzdHlsZWQobW90aW9uLmRpdilgXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMTtcbmA7XG5cbmNvbnN0IEZlYXR1cmVEZXNjcmlwdGlvbiA9IHN0eWxlZC5wYFxuICBmb250LXNpemU6IDAuOXJlbTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuYDtcblxuY29uc3QgU3RhdHNHcmlkID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgMWZyKTtcbiAgZ2FwOiAxcmVtO1xuYDtcblxuY29uc3QgU3RhdEl0ZW0gPSBzdHlsZWQuZGl2YFxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG5gO1xuXG5jb25zdCBTdGF0VmFsdWUgPSBzdHlsZWQuZGl2YFxuICBmb250LXNpemU6IDEuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6IHdoaXRlO1xuYDtcblxuY29uc3QgU3RhdExhYmVsID0gc3R5bGVkLmRpdmBcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNik7XG5gO1xuXG5jb25zdCBEZXRhaWxlZFZpZXcgPSBzdHlsZWQobW90aW9uLmRpdilgXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIHBhZGRpbmc6IDNyZW07XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBtYXJnaW4tYm90dG9tOiAzcmVtO1xuYDtcblxuY29uc3QgRGV0YWlsZWRDb250ZW50ID0gc3R5bGVkLmRpdmBgO1xuXG5jb25zdCBEZXRhaWxlZEhlYWRlciA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMS41cmVtO1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xuYDtcblxuY29uc3QgRGV0YWlsZWRJY29uID0gc3R5bGVkLmRpdjx7IGNvbG9yOiBzdHJpbmcgfT5gXG4gIGZvbnQtc2l6ZTogNHJlbTtcbiAgY29sb3I6ICR7cHJvcHMgPT4gcHJvcHMuY29sb3J9O1xuICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMCAyMHB4ICR7cHJvcHMgPT4gcHJvcHMuY29sb3J9NTApO1xuYDtcblxuY29uc3QgRGV0YWlsZWRUaXRsZSA9IHN0eWxlZC5oM2BcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuYDtcblxuY29uc3QgRGV0YWlsZWRTdWJ0aXRsZSA9IHN0eWxlZC5wYFxuICBmb250LXNpemU6IDEuMnJlbTtcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbmA7XG5cbmNvbnN0IERldGFpbGVkRGVzY3JpcHRpb24gPSBzdHlsZWQucGBcbiAgZm9udC1zaXplOiAxLjFyZW07XG4gIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7XG4gIGxpbmUtaGVpZ2h0OiAxLjg7XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG5gO1xuXG5jb25zdCBUZWNobm9sb2dpZXNTZWN0aW9uID0gc3R5bGVkLmRpdmBcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbmA7XG5cbmNvbnN0IEJlbmVmaXRzU2VjdGlvbiA9IHN0eWxlZC5kaXZgYDtcblxuY29uc3QgU2VjdGlvbkxhYmVsID0gc3R5bGVkLmg0YFxuICBmb250LXNpemU6IDEuMnJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6IHdoaXRlO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuYDtcblxuY29uc3QgVGVjaG5vbG9naWVzTGlzdCA9IHN0eWxlZC5kaXZgXG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgZ2FwOiAwLjVyZW07XG5gO1xuXG5jb25zdCBUZWNoVGFnID0gc3R5bGVkKG1vdGlvbi5zcGFuKWBcbiAgYmFja2dyb3VuZDogcmdiYSg3NCwgMTQ0LCAyMjYsIDAuMik7XG4gIGNvbG9yOiAjNGE5MGUyO1xuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNzQsIDE0NCwgMjI2LCAwLjMpO1xuYDtcblxuY29uc3QgQmVuZWZpdHNMaXN0ID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAwLjVyZW07XG5gO1xuXG5jb25zdCBCZW5lZml0SXRlbSA9IHN0eWxlZChtb3Rpb24uZGl2KWBcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTtcbiAgZm9udC1zaXplOiAxcmVtO1xuYDtcblxuY29uc3QgUHJvZ3Jlc3NJbmRpY2F0b3JzID0gc3R5bGVkLmRpdmBcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGdhcDogMXJlbTtcbmA7XG5cbmNvbnN0IFByb2dyZXNzRG90ID0gc3R5bGVkKG1vdGlvbi5kaXYpPHsgYWN0aXZlOiBib29sZWFuIH0+YFxuICB3aWR0aDogMTJweDtcbiAgaGVpZ2h0OiAxMnB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJhY2tncm91bmQ6ICR7cHJvcHMgPT4gcHJvcHMuYWN0aXZlID8gJyM0YTkwZTInIDogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKSd9O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5gO1xuXG5leHBvcnQgZGVmYXVsdCBSZXZvbHV0aW9uYXJ5RmVhdHVyZXM7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsInN0eWxlZCIsImpzeCIsIl9qc3giLCJqc3hzIiwiX2pzeHMiLCJmZWF0dXJlcyIsImlkIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImNvbG9yIiwic3RhdHMiLCJsYWJlbCIsInZhbHVlIiwidGVjaG5vbG9naWVzIiwiYmVuZWZpdHMiLCJSZXZvbHV0aW9uYXJ5RmVhdHVyZXMiLCJjdXJyZW50U2VjdGlvbiIsImFjdGl2ZUZlYXR1cmUiLCJzZXRBY3RpdmVGZWF0dXJlIiwiaG92ZXJlZEZlYXR1cmUiLCJzZXRIb3ZlcmVkRmVhdHVyZSIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwibGVuZ3RoIiwiY2xlYXJJbnRlcnZhbCIsIkZlYXR1cmVzQ29udGFpbmVyIiwiY2hpbGRyZW4iLCJDb250ZW50V3JhcHBlciIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJTZWN0aW9uVGl0bGUiLCJTZWN0aW9uU3VidGl0bGUiLCJGZWF0dXJlc0dyaWQiLCJtYXAiLCJmZWF0dXJlIiwiaW5kZXgiLCJGZWF0dXJlQ2FyZCIsImFjdGl2ZSIsImhvdmVyZWQiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJvbkNsaWNrIiwic2NhbGUiLCJkZWxheSIsIndoaWxlSG92ZXIiLCJ3aGlsZVRhcCIsIkZlYXR1cmVJY29uIiwiRmVhdHVyZVRpdGxlIiwiRmVhdHVyZVN1YnRpdGxlIiwiRmVhdHVyZURldGFpbHMiLCJoZWlnaHQiLCJleGl0IiwiRmVhdHVyZURlc2NyaXB0aW9uIiwiU3RhdHNHcmlkIiwic3RhdCIsInN0YXRJbmRleCIsIlN0YXRJdGVtIiwiU3RhdFZhbHVlIiwiU3RhdExhYmVsIiwibW9kZSIsIkRldGFpbGVkVmlldyIsIngiLCJEZXRhaWxlZENvbnRlbnQiLCJEZXRhaWxlZEhlYWRlciIsIkRldGFpbGVkSWNvbiIsIkRldGFpbGVkVGl0bGUiLCJEZXRhaWxlZFN1YnRpdGxlIiwiRGV0YWlsZWREZXNjcmlwdGlvbiIsIlRlY2hub2xvZ2llc1NlY3Rpb24iLCJTZWN0aW9uTGFiZWwiLCJUZWNobm9sb2dpZXNMaXN0IiwidGVjaCIsIlRlY2hUYWciLCJCZW5lZml0c1NlY3Rpb24iLCJCZW5lZml0c0xpc3QiLCJiZW5lZml0IiwiQmVuZWZpdEl0ZW0iLCJQcm9ncmVzc0luZGljYXRvcnMiLCJfIiwiUHJvZ3Jlc3NEb3QiLCJoMiIsInAiLCJwcm9wcyIsImgzIiwiaDQiLCJzcGFuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/RevolutionaryFeatures.tsx\n");

/***/ })

};
;