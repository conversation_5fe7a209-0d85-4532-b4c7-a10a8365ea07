"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_Hero3D_tsx";
exports.ids = ["src_components_Hero3D_tsx"];
exports.modules = {

/***/ "./src/components/Hero3D.tsx":
/*!***********************************!*\
  !*** ./src/components/Hero3D.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"@react-three/fiber\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/drei */ \"@react-three/drei\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// 3D компонент квантовой сферы\nconst QuantumSphere = ({\n  quantumStatus\n}) => {\n  const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.useFrame)(state => {\n    if (meshRef.current) {\n      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n\n      // Пульсация в зависимости от квантового статуса\n      const scale = quantumStatus?.isQuantumAvailable ? 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1 : 1 + Math.sin(state.clock.elapsedTime) * 0.05;\n      meshRef.current.scale.setScalar(scale);\n    }\n  });\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.Float, {\n    speed: 2,\n    rotationIntensity: 1,\n    floatIntensity: 2,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.Sphere, {\n      ref: meshRef,\n      args: [1, 64, 64],\n      onPointerOver: () => setHovered(true),\n      onPointerOut: () => setHovered(false),\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.MeshDistortMaterial, {\n        color: quantumStatus?.isQuantumAvailable ? \"#4a90e2\" : \"#7b68ee\",\n        attach: \"material\",\n        distort: 0.4,\n        speed: 2,\n        roughness: 0.1,\n        metalness: 0.8,\n        emissive: quantumStatus?.isQuantumAvailable ? \"#1a4480\" : \"#3d3470\",\n        emissiveIntensity: hovered ? 0.5 : 0.2\n      })\n    })\n  });\n};\n\n// 3D текст с анимацией\nconst AnimatedText = ({\n  text,\n  position\n}) => {\n  const textRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.useFrame)(state => {\n    if (textRef.current) {\n      textRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;\n    }\n  });\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.Text, {\n    ref: textRef,\n    position: position,\n    fontSize: 0.5,\n    color: \"#ffffff\",\n    anchorX: \"center\",\n    anchorY: \"middle\",\n    font: \"/fonts/Inter-Bold.woff\",\n    children: text\n  });\n};\n\n// Частицы для фона\nconst Particles = () => {\n  const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const particleCount = 1000;\n  const positions = new Float32Array(particleCount * 3);\n  const colors = new Float32Array(particleCount * 3);\n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n    colors[i * 3] = Math.random();\n    colors[i * 3 + 1] = Math.random() * 0.5 + 0.5;\n    colors[i * 3 + 2] = 1;\n  }\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.useFrame)(state => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n    }\n  });\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"points\", {\n    ref: particlesRef,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"bufferGeometry\", {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"bufferAttribute\", {\n        attach: \"attributes-position\",\n        count: particleCount,\n        array: positions,\n        itemSize: 3\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"bufferAttribute\", {\n        attach: \"attributes-color\",\n        count: particleCount,\n        array: colors,\n        itemSize: 3\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pointsMaterial\", {\n      size: 0.02,\n      vertexColors: true,\n      transparent: true,\n      opacity: 0.6\n    })]\n  });\n};\nconst Hero3D = ({\n  quantumStatus,\n  emotionalState,\n  onStartJourney\n}) => {\n  const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    x: 0,\n    y: 0\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const handleMouseMove = event => {\n      setMousePosition({\n        x: event.clientX / window.innerWidth * 2 - 1,\n        y: -(event.clientY / window.innerHeight) * 2 + 1\n      });\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(HeroContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(CanvasContainer, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.Canvas, {\n        camera: {\n          position: [0, 0, 5],\n          fov: 75\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"ambientLight\", {\n          intensity: 0.3\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pointLight\", {\n          position: [10, 10, 10],\n          intensity: 1\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pointLight\", {\n          position: [-10, -10, -10],\n          intensity: 0.5,\n          color: \"#7b68ee\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.Stars, {\n          radius: 100,\n          depth: 50,\n          count: 5000,\n          factor: 4,\n          saturation: 0,\n          fade: true\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Particles, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(QuantumSphere, {\n          quantumStatus: quantumStatus\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(AnimatedText, {\n          text: \"\\u041A\\u041E\\u0417\\u042B\\u0420\\u042C \\u041C\\u0410\\u0421\\u0422\\u0415\\u0420 4.0\",\n          position: [0, 2, 0]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(AnimatedText, {\n          text: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u044F \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440\",\n          position: [0, -2, 0]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_2__.OrbitControls, {\n          enableZoom: false,\n          enablePan: false\n        })]\n      })\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ContentOverlay, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 1,\n          delay: 0.5\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(MainTitle, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n            animate: {\n              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity\n            },\n            style: {\n              background: 'linear-gradient(90deg, #4a90e2, #7b68ee, #9370db, #4a90e2)',\n              backgroundSize: '200% 100%',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent'\n            },\n            children: \"\\u041A\\u041E\\u0417\\u042B\\u0420\\u042C \\u041C\\u0410\\u0421\\u0422\\u0415\\u0420 4.0\"\n          })\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Subtitle, {\n          children: \"\\u0413\\u0434\\u0435 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u0430\\u044F \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C \\u0432\\u0441\\u0442\\u0440\\u0435\\u0447\\u0430\\u0435\\u0442\\u0441\\u044F \\u0441 \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u043E\\u043C\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeaturesList, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureItem, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 1\n            },\n            children: \"\\uD83D\\uDD2C \\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u0430\\u044F \\u0447\\u0435\\u0441\\u0442\\u043D\\u043E\\u0441\\u0442\\u044C\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureItem, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 1.2\n            },\n            children: \"\\uD83E\\uDDE0 \\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureItem, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 1.4\n            },\n            children: \"\\uD83C\\uDF0D 3D \\u041C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u0430\\u044F\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureItem, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: 1.6\n            },\n            children: \"\\u26D3\\uFE0F Web3 \\u044D\\u043A\\u043E\\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ButtonContainer, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StartButton, {\n              onClick: onStartJourney,\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ButtonGlow, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"span\", {\n                children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u043F\\u0443\\u0442\\u0435\\u0448\\u0435\\u0441\\u0442\\u0432\\u0438\\u0435\"\n              })]\n            })\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SecondaryButton, {\n              children: \"\\u0421\\u043C\\u043E\\u0442\\u0440\\u0435\\u0442\\u044C \\u0434\\u0435\\u043C\\u043E\"\n            })\n          })]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusPanels, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusIcon, {\n            children: \"\\u269B\\uFE0F\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusLabel, {\n              children: \"\\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0439 \\u0441\\u0442\\u0430\\u0442\\u0443\\u0441\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusValue, {\n              active: quantumStatus?.isQuantumAvailable,\n              children: quantumStatus?.isQuantumAvailable ? 'Активен' : 'Инициализация'\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusIcon, {\n            children: \"\\uD83E\\uDDE0\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusLabel, {\n              children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusValue, {\n              active: true,\n              children: [\"\\u041D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0435\\u043D\\u0438\\u0435: \", emotionalState?.happiness > 0.7 ? 'Отличное' : emotionalState?.happiness > 0.4 ? 'Хорошее' : 'Нормальное']\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusIcon, {\n            children: \"\\uD83C\\uDF10\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusLabel, {\n              children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432 \\u043E\\u043D\\u043B\\u0430\\u0439\\u043D\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatusValue, {\n              active: true,\n              children: (Math.random() * 50000 + 10000).toFixed(0)\n            })]\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InteractiveCursor, {\n      style: {\n        left: `${(mousePosition.x + 1) * 50}%`,\n        top: `${(-mousePosition.y + 1) * 50}%`\n      }\n    })]\n  });\n};\n\n// Стилизованные компоненты\nconst HeroContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  overflow: hidden;\n  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n`;\nconst CanvasContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n`;\nconst ContentOverlay = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n  pointer-events: none;\n  \n  > * {\n    pointer-events: auto;\n  }\n`;\nconst MainTitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().h1)`\n  font-size: 4rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  text-shadow: 0 0 30px rgba(74, 144, 226, 0.5);\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst Subtitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().p)`\n  font-size: 1.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  text-align: center;\n  margin-bottom: 3rem;\n  max-width: 600px;\n  \n  @media (max-width: 768px) {\n    font-size: 1.2rem;\n  }\n`;\nconst FeaturesList = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  margin-bottom: 3rem;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst FeatureItem = styled_components__WEBPACK_IMPORTED_MODULE_4___default()(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div)`\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 1.1rem;\n  padding: 0.5rem;\n  text-align: center;\n`;\nconst ButtonContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n  }\n`;\nconst StartButton = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().button)`\n  position: relative;\n  background: linear-gradient(135deg, #4a90e2, #7b68ee);\n  color: white;\n  border: none;\n  border-radius: 50px;\n  padding: 1rem 2.5rem;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);\n  \n  &:hover {\n    box-shadow: 0 15px 40px rgba(74, 144, 226, 0.5);\n    transform: translateY(-2px);\n  }\n`;\nconst ButtonGlow = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  animation: shimmer 3s infinite;\n  \n  @keyframes shimmer {\n    0% { left: -100%; }\n    100% { left: 100%; }\n  }\n`;\nconst SecondaryButton = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().button)`\n  background: transparent;\n  color: white;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50px;\n  padding: 1rem 2.5rem;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    border-color: #4a90e2;\n    background: rgba(74, 144, 226, 0.1);\n    transform: translateY(-2px);\n  }\n`;\nconst StatusPanels = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  bottom: 2rem;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 1rem;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n  }\n`;\nconst StatusPanel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 0.75rem 1rem;\n`;\nconst StatusIcon = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  font-size: 1.2rem;\n`;\nconst StatusText = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  flex-direction: column;\n`;\nconst StatusLabel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().span)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n`;\nconst StatusValue = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().span)`\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};\n`;\nconst InteractiveCursor = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(74, 144, 226, 0.8), transparent);\n  pointer-events: none;\n  transition: all 0.1s ease;\n  z-index: 5;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero3D);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Hero3D.tsx\n");

/***/ })

};
;