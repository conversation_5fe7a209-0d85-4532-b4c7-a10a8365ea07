"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_StreamingPlatform_tsx";
exports.ids = ["src_components_StreamingPlatform_tsx"];
exports.modules = {

/***/ "./src/components/StreamingPlatform.tsx":
/*!**********************************************!*\
  !*** ./src/components/StreamingPlatform.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst StreamingPlatform = ({\n  onStartStreaming\n}) => {\n  const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('browse');\n  const [featuredStreams, setFeaturedStreams] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [streamStats, setStreamStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    viewers: 0,\n    followers: 1247,\n    donations: 156.50,\n    chatMessages: 0\n  });\n  const [streamSettings, setStreamSettings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    title: 'Козырь Мастер - Эпические игры!',\n    quality: '1080p',\n    enableChat: true,\n    enableDonations: true,\n    enableAI: true\n  });\n  const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция популярных стримов\n    setFeaturedStreams([{\n      id: '1',\n      title: 'Турнир по Дураку - Финал!',\n      streamer: 'ProGamer2024',\n      viewers: 2847,\n      game: 'Дурак',\n      thumbnail: '🃏',\n      isLive: true,\n      quality: '1080p',\n      language: 'RU',\n      tags: ['Турнир', 'Финал', 'Профи']\n    }, {\n      id: '2',\n      title: 'Покер с ИИ - Кто умнее?',\n      streamer: 'AIChallenger',\n      viewers: 1523,\n      game: 'Покер',\n      thumbnail: '♠️',\n      isLive: true,\n      quality: '4K',\n      language: 'EN',\n      tags: ['ИИ', 'Покер', 'Вызов']\n    }, {\n      id: '3',\n      title: 'Обучение новичков',\n      streamer: 'CardMaster',\n      viewers: 892,\n      game: 'Дурак',\n      thumbnail: '🎓',\n      isLive: true,\n      quality: '720p',\n      language: 'RU',\n      tags: ['Обучение', 'Новички']\n    }]);\n\n    // Симуляция обновления статистики стрима\n    if (isStreaming) {\n      const interval = setInterval(() => {\n        setStreamStats(prev => ({\n          ...prev,\n          viewers: prev.viewers + Math.floor(Math.random() * 10 - 3),\n          chatMessages: prev.chatMessages + Math.floor(Math.random() * 5),\n          donations: prev.donations + (Math.random() > 0.9 ? Math.random() * 20 : 0)\n        }));\n      }, 3000);\n      return () => clearInterval(interval);\n    }\n  }, [isStreaming]);\n  const startStream = async () => {\n    try {\n      // Запрос доступа к камере и микрофону\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n      setIsStreaming(true);\n      setStreamStats(prev => ({\n        ...prev,\n        viewers: 1\n      }));\n    } catch (error) {\n      console.error('Ошибка доступа к медиа:', error);\n      // Симуляция стрима без реального видео\n      setIsStreaming(true);\n      setStreamStats(prev => ({\n        ...prev,\n        viewers: 1\n      }));\n    }\n  };\n  const stopStream = () => {\n    if (videoRef.current?.srcObject) {\n      const stream = videoRef.current.srcObject;\n      stream.getTracks().forEach(track => track.stop());\n    }\n    setIsStreaming(false);\n    setStreamStats(prev => ({\n      ...prev,\n      viewers: 0\n    }));\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamingContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n          children: \"\\u0421\\u0442\\u0440\\u0438\\u043C\\u0438\\u043D\\u0433 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionSubtitle, {\n          children: \"\\u041F\\u0440\\u043E\\u0444\\u0435\\u0441\\u0441\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u044B \\u0434\\u043B\\u044F \\u0441\\u043E\\u0437\\u0434\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439 \\u043A\\u043E\\u043D\\u0442\\u0435\\u043D\\u0442\\u0430\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabNavigation, {\n        children: [{\n          id: 'browse',\n          label: 'Обзор стримов',\n          icon: '📺'\n        }, {\n          id: 'stream',\n          label: 'Мой стрим',\n          icon: '🎥'\n        }, {\n          id: 'analytics',\n          label: 'Аналитика',\n          icon: '📊'\n        }].map(tab => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TabButton, {\n          active: activeTab === tab.id,\n          onClick: () => setActiveTab(tab.id),\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabIcon, {\n            children: tab.icon\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabLabel, {\n            children: tab.label\n          })]\n        }, tab.id))\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabContent, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n          mode: \"wait\",\n          children: [activeTab === 'browse' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(BrowseSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SectionHeader, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionSubtitle, {\n                  children: \"\\u041F\\u043E\\u043F\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u044B\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(LiveIndicator, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LiveDot, {}), featuredStreams.length, \" \\u0441\\u0442\\u0440\\u0438\\u043C\\u043E\\u0432 \\u0432 \\u044D\\u0444\\u0438\\u0440\\u0435\"]\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamsGrid, {\n                children: featuredStreams.map(stream => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamCard, {\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamThumbnail, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ThumbnailIcon, {\n                      children: stream.thumbnail\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamOverlay, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(QualityBadge, {\n                        children: stream.quality\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ViewerCount, {\n                        children: [\"\\uD83D\\uDC41\\uFE0F \", stream.viewers.toLocaleString()]\n                      })]\n                    }), stream.isLive && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LiveBadge, {\n                      children: \"LIVE\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamInfo, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamTitle, {\n                      children: stream.title\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamMeta, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamerName, {\n                        children: stream.streamer\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamGame, {\n                        children: stream.game\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamTags, {\n                      children: stream.tags.map(tag => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StreamTag, {\n                        children: tag\n                      }, tag))\n                    })]\n                  })]\n                }, stream.id))\n              })]\n            })\n          }, \"browse\"), activeTab === 'stream' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamSection, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamContainer, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamPreview, {\n                  children: [isStreaming ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VideoPreview, {\n                    ref: videoRef,\n                    autoPlay: true,\n                    muted: true,\n                    playsInline: true\n                  }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PreviewPlaceholder, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlaceholderIcon, {\n                      children: \"\\uD83C\\uDFA5\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PlaceholderText, {\n                      children: \"\\u041F\\u0440\\u0435\\u0434\\u043F\\u0440\\u043E\\u0441\\u043C\\u043E\\u0442\\u0440 \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430\"\n                    })]\n                  }), isStreaming && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamOverlays, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamStatus, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatusDot, {}), \"\\u0412 \\u042D\\u0424\\u0418\\u0420\\u0415\"]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ViewerCounter, {\n                      children: [\"\\uD83D\\uDC41\\uFE0F \", streamStats.viewers]\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamControls, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ControlsRow, {\n                    children: [!isStreaming ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StartStreamButton, {\n                      onClick: startStream,\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\uD83D\\uDE80 \\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0441\\u0442\\u0440\\u0438\\u043C\"\n                    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StopStreamButton, {\n                      onClick: stopStream,\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\u23F9\\uFE0F \\u041E\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0438\\u0442\\u044C \\u0441\\u0442\\u0440\\u0438\\u043C\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ControlButton, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: \"\\u2699\\uFE0F \\u041D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0439\\u043A\\u0438\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StreamSettings, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SettingGroup, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SettingLabel, {\n                        children: \"\\u041D\\u0430\\u0437\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SettingInput, {\n                        value: streamSettings.title,\n                        onChange: e => setStreamSettings(prev => ({\n                          ...prev,\n                          title: e.target.value\n                        }))\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SettingGroup, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SettingLabel, {\n                        children: \"\\u041A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SettingSelect, {\n                        value: streamSettings.quality,\n                        onChange: e => setStreamSettings(prev => ({\n                          ...prev,\n                          quality: e.target.value\n                        })),\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"option\", {\n                          value: \"720p\",\n                          children: \"720p\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"option\", {\n                          value: \"1080p\",\n                          children: \"1080p\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"option\", {\n                          value: \"4K\",\n                          children: \"4K\"\n                        })]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SettingToggles, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableChat,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableChat: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleLabel, {\n                          children: \"\\u0412\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u044C \\u0447\\u0430\\u0442\"\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableDonations,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableDonations: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleLabel, {\n                          children: \"\\u041F\\u0440\\u0438\\u043D\\u0438\\u043C\\u0430\\u0442\\u044C \\u0434\\u043E\\u043D\\u0430\\u0442\\u044B\"\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ToggleOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleInput, {\n                          type: \"checkbox\",\n                          checked: streamSettings.enableAI,\n                          onChange: e => setStreamSettings(prev => ({\n                            ...prev,\n                            enableAI: e.target.checked\n                          }))\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleLabel, {\n                          children: \"\\u0418\\u0418-\\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\"\n                        })]\n                      })]\n                    })]\n                  })]\n                })]\n              }), isStreaming && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(LiveStats, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDC41\\uFE0F\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                    children: streamStats.viewers\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                    children: \"\\u0417\\u0440\\u0438\\u0442\\u0435\\u043B\\u0435\\u0439\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDC65\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                    children: streamStats.followers\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                    children: \"\\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDCB0\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatValue, {\n                    children: [\"$\", streamStats.donations.toFixed(2)]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                    children: \"\\u0414\\u043E\\u043D\\u0430\\u0442\\u043E\\u0432\"\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatIcon, {\n                    children: \"\\uD83D\\uDCAC\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                    children: streamStats.chatMessages\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                    children: \"\\u0421\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0439\"\n                  })]\n                })]\n              })]\n            })\n          }, \"stream\"), activeTab === 'analytics' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -50\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(AnalyticsSection, {\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(AnalyticsGrid, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(CardTitle, {\n                    children: \"\\u0421\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u043A\\u0430 \\u0437\\u0430 \\u043C\\u0435\\u0441\\u044F\\u0446\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricsList, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricLabel, {\n                        children: \"\\u041E\\u0431\\u0449\\u0435\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F \\u0441\\u0442\\u0440\\u0438\\u043C\\u043E\\u0432:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricValue, {\n                        children: \"47\\u0447 23\\u043C\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricLabel, {\n                        children: \"\\u0421\\u0440\\u0435\\u0434\\u043D\\u0438\\u0435 \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0438:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricValue, {\n                        children: \"234\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricLabel, {\n                        children: \"\\u041F\\u0438\\u043A\\u043E\\u0432\\u044B\\u0435 \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0438:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricValue, {\n                        children: \"1,847\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricLabel, {\n                        children: \"\\u041D\\u043E\\u0432\\u044B\\u0445 \\u043F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432:\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricValue, {\n                        children: \"+156\"\n                      })]\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(CardTitle, {\n                    children: \"\\u0414\\u043E\\u0445\\u043E\\u0434\\u044B\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(RevenueChart, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartBar, {\n                      height: 60,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartValue, {\n                        children: \"$245\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartBar, {\n                      height: 80,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartValue, {\n                        children: \"$320\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartBar, {\n                      height: 45,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartValue, {\n                        children: \"$180\"\n                      })\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartBar, {\n                      height: 95,\n                      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ChartValue, {\n                        children: \"$425\"\n                      })\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(AnalyticsCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(CardTitle, {\n                    children: \"\\u0418\\u0418 \\u0418\\u043D\\u0441\\u0430\\u0439\\u0442\\u044B\"\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(AIInsights, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightIcon, {\n                        children: \"\\uD83C\\uDFAF\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightText, {\n                        children: \"\\u041B\\u0443\\u0447\\u0448\\u0435\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F \\u0434\\u043B\\u044F \\u0441\\u0442\\u0440\\u0438\\u043C\\u0430: 19:00-22:00\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightIcon, {\n                        children: \"\\uD83D\\uDCC8\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightText, {\n                        children: \"\\u0422\\u0443\\u0440\\u043D\\u0438\\u0440\\u043D\\u044B\\u0435 \\u0441\\u0442\\u0440\\u0438\\u043C\\u044B \\u043F\\u0440\\u0438\\u0432\\u043B\\u0435\\u043A\\u0430\\u044E\\u0442 +40% \\u0437\\u0440\\u0438\\u0442\\u0435\\u043B\\u0435\\u0439\"\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(InsightItem, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightIcon, {\n                        children: \"\\uD83D\\uDCA1\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(InsightText, {\n                        children: \"\\u0420\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0443\\u0435\\u043C \\u0434\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435 \\u0438\\u043D\\u0442\\u0435\\u0440\\u0430\\u043A\\u0442\\u0438\\u0432\\u0430\"\n                      })]\n                    })]\n                  })]\n                })]\n              })\n            })\n          }, \"analytics\")]\n        })\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\nconst StreamingContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  width: 100%;\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2)`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst SectionSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\nconst TabNavigation = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 0.5rem;\n`;\nconst TabButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  border: none;\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 600;\n\n  &:hover {\n    background: rgba(74, 144, 226, 0.1);\n    color: #4a90e2;\n  }\n`;\nconst TabIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 1.2rem;\n`;\nconst TabLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 0.9rem;\n`;\nconst TabContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 500px;\n`;\nconst BrowseSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst SectionHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;\nconst LiveIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\nconst LiveDot = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 8px;\n  height: 8px;\n  background: #ef4444;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\nconst StreamsGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\nconst StreamCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\nconst StreamThumbnail = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: relative;\n  height: 180px;\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ThumbnailIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  filter: drop-shadow(0 0 20px currentColor);\n`;\nconst StreamOverlay = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  bottom: 0.5rem;\n  left: 0.5rem;\n  right: 0.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\nconst QualityBadge = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\nconst ViewerCount = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\nconst LiveBadge = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background: #ef4444;\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 700;\n  animation: pulse 2s infinite;\n`;\nconst StreamInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  padding: 1rem;\n`;\nconst StreamTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  color: white;\n  font-size: 1rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  line-height: 1.3;\n`;\nconst StreamMeta = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n`;\nconst StreamerName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: #4a90e2;\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\nconst StreamGame = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.8rem;\n`;\nconst StreamTags = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n`;\nconst StreamTag = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-size: 0.7rem;\n  font-weight: 500;\n`;\nconst StreamSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst StreamContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst StreamPreview = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  overflow: hidden;\n  aspect-ratio: 16/9;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst VideoPreview = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().video)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\nconst PreviewPlaceholder = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\n`;\nconst PlaceholderIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n`;\nconst PlaceholderText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 1.1rem;\n`;\nconst StreamOverlays = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  right: 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\nconst StreamStatus = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: rgba(239, 68, 68, 0.9);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-weight: 700;\n  font-size: 0.9rem;\n`;\nconst StatusDot = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 8px;\n  height: 8px;\n  background: white;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\nconst ViewerCounter = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-weight: 600;\n`;\nconst StreamControls = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst ControlsRow = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\nconst StartStreamButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  background: linear-gradient(135deg, #4ade80, #22c55e);\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 1rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\nconst StopStreamButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  color: white;\n  border: none;\n  border-radius: 10px;\n  padding: 1rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\nconst ControlButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  padding: 1rem;\n  cursor: pointer;\n  font-weight: 600;\n`;\nconst StreamSettings = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\nconst SettingGroup = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst SettingLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().label)`\n  display: block;\n  color: white;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n`;\nconst SettingInput = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().input)`\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\nconst SettingSelect = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().select)`\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n\n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n  }\n`;\nconst SettingToggles = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\nconst ToggleOption = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\nconst ToggleInput = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().input)`\n  width: 18px;\n  height: 18px;\n  accent-color: #4a90e2;\n`;\nconst ToggleLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().label)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  cursor: pointer;\n`;\nconst LiveStats = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 1rem;\n\n  @media (max-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n`;\nconst StatCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst StatIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\nconst StatValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: #4a90e2;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.25rem;\n`;\nconst StatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.8rem;\n`;\nconst AnalyticsSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst AnalyticsGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\nconst AnalyticsCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst CardTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  color: white;\n  font-size: 1.2rem;\n  font-weight: 700;\n  margin-bottom: 1rem;\n`;\nconst MetricsList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\nconst MetricItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\nconst MetricLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\nconst MetricValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: #4a90e2;\n  font-weight: 600;\n`;\nconst RevenueChart = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: end;\n  gap: 1rem;\n  height: 120px;\n`;\nconst ChartBar = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  flex: 1;\n  height: ${props => props.height}%;\n  background: linear-gradient(to top, #4a90e2, #7b68ee);\n  border-radius: 5px 5px 0 0;\n  position: relative;\n  display: flex;\n  align-items: end;\n  justify-content: center;\n  padding-bottom: 0.5rem;\n`;\nconst ChartValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-size: 0.8rem;\n  font-weight: 600;\n`;\nconst AIInsights = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\nconst InsightItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\nconst InsightIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.5rem;\n`;\nconst InsightText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  line-height: 1.4;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamingPlatform);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/StreamingPlatform.tsx\n");

/***/ })

};
;