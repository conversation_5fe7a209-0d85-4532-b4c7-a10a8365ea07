"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_Footer_tsx";
exports.ids = ["src_components_Footer_tsx"];
exports.modules = {

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Footer = ({\n  onSubscribe\n}) => {\n  const [email, setEmail] = react__WEBPACK_IMPORTED_MODULE_0___default().useState('');\n  const handleSubscribe = e => {\n    e.preventDefault();\n    if (email && onSubscribe) {\n      onSubscribe(email);\n      setEmail('');\n    }\n  };\n  const socialLinks = [{\n    name: 'Discord',\n    icon: '💬',\n    url: 'https://discord.gg/kozyrmasterr',\n    color: '#5865F2'\n  }, {\n    name: 'Telegram',\n    icon: '📱',\n    url: 'https://t.me/kozyrmasterr',\n    color: '#0088cc'\n  }, {\n    name: 'Twitter',\n    icon: '🐦',\n    url: 'https://twitter.com/kozyrmasterr',\n    color: '#1DA1F2'\n  }, {\n    name: 'YouTube',\n    icon: '📺',\n    url: 'https://youtube.com/@kozyrmasterr',\n    color: '#FF0000'\n  }, {\n    name: 'Twitch',\n    icon: '🎮',\n    url: 'https://twitch.tv/kozyrmasterr',\n    color: '#9146FF'\n  }, {\n    name: 'GitHub',\n    icon: '💻',\n    url: 'https://github.com/kozyrmasterr',\n    color: '#333'\n  }];\n  const quickLinks = [{\n    name: 'Игры',\n    url: '/games'\n  }, {\n    name: 'Турниры',\n    url: '/tournaments'\n  }, {\n    name: 'Обучение',\n    url: '/tutorials'\n  }, {\n    name: 'Рейтинг',\n    url: '/leaderboard'\n  }, {\n    name: 'Профиль',\n    url: '/profile'\n  }, {\n    name: 'Поддержка',\n    url: '/support'\n  }];\n  const legalLinks = [{\n    name: 'Пользовательское соглашение',\n    url: '/terms'\n  }, {\n    name: 'Политика конфиденциальности',\n    url: '/privacy'\n  }, {\n    name: 'Правила игры',\n    url: '/rules'\n  }, {\n    name: 'FAQ',\n    url: '/faq'\n  }];\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FooterContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FooterContent, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          viewport: {\n            once: true\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(BrandSection, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Logo, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LogoIcon, {\n                children: \"\\uD83C\\uDFAE\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(LogoText, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LogoTitle, {\n                  children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LogoVersion, {\n                  children: \"4.0\"\n                })]\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(BrandDescription, {\n              children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u0430\\u044F \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0430 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440 \\u0441 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E, \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u0418\\u0418 \\u0438 3D \\u043C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0439. \\u0411\\u0443\\u0434\\u0443\\u0449\\u0435\\u0435 \\u0438\\u0433\\u0440 \\u0443\\u0436\\u0435 \\u0437\\u0434\\u0435\\u0441\\u044C!\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechBadges, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechBadge, {\n                children: \"\\u269B\\uFE0F \\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0438\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechBadge, {\n                children: \"\\uD83E\\uDDE0 \\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechBadge, {\n                children: \"\\uD83C\\uDF0D 3D \\u041C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u0430\\u044F\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechBadge, {\n                children: \"\\u26D3\\uFE0F Web3 & NFT\"\n              })]\n            })]\n          })\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.1\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n            children: \"\\u0411\\u044B\\u0441\\u0442\\u0440\\u044B\\u0435 \\u0441\\u0441\\u044B\\u043B\\u043A\\u0438\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LinksList, {\n            children: quickLinks.map((link, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LinkItem, {\n              href: link.url,\n              whileHover: {\n                x: 5,\n                color: '#4a90e2'\n              },\n              transition: {\n                duration: 0.2\n              },\n              children: link.name\n            }, link.name))\n          })]\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n            children: \"\\u0421\\u043E\\u043E\\u0431\\u0449\\u0435\\u0441\\u0442\\u0432\\u043E\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SocialLinks, {\n            children: socialLinks.map((social, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SocialLink, {\n              href: social.url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              color: social.color,\n              whileHover: {\n                scale: 1.1,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              initial: {\n                opacity: 0,\n                scale: 0\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SocialIcon, {\n                children: social.icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SocialName, {\n                children: social.name\n              })]\n            }, social.name))\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(CommunityStats, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                children: \"50K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                children: \"15K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                children: \"Discord\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(StatItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatValue, {\n                children: \"25K+\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StatLabel, {\n                children: \"\\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0447\\u0438\\u043A\\u043E\\u0432\"\n              })]\n            })]\n          })]\n        })\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FooterSection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n            children: \"\\u041D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NewsletterDescription, {\n            children: \"\\u0411\\u0443\\u0434\\u044C\\u0442\\u0435 \\u0432 \\u043A\\u0443\\u0440\\u0441\\u0435 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u043D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0435\\u0439, \\u0442\\u0443\\u0440\\u043D\\u0438\\u0440\\u043E\\u0432 \\u0438 \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u0439 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u044B\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NewsletterForm, {\n            onSubmit: handleSubscribe,\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(EmailInput, {\n              type: \"email\",\n              placeholder: \"\\u0412\\u0430\\u0448 email \\u0430\\u0434\\u0440\\u0435\\u0441\",\n              value: email,\n              onChange: e => setEmail(e.target.value),\n              required: true\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SubscribeButton, {\n              type: \"submit\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\uD83D\\uDE80 \\u041F\\u043E\\u0434\\u043F\\u0438\\u0441\\u0430\\u0442\\u044C\\u0441\\u044F\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NewsletterBenefits, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(BenefitItem, {\n              children: \"\\u2728 \\u042D\\u043A\\u0441\\u043A\\u043B\\u044E\\u0437\\u0438\\u0432\\u043D\\u044B\\u0435 \\u0442\\u0443\\u0440\\u043D\\u0438\\u0440\\u044B\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(BenefitItem, {\n              children: \"\\uD83C\\uDF81 \\u0411\\u043E\\u043D\\u0443\\u0441\\u044B \\u0438 \\u043F\\u0440\\u043E\\u043C\\u043E\\u043A\\u043E\\u0434\\u044B\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(BenefitItem, {\n              children: \"\\uD83D\\uDCF0 \\u041F\\u0435\\u0440\\u0432\\u044B\\u043C\\u0438 \\u0443\\u0437\\u043D\\u0430\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0438\"\n            })]\n          })]\n        })\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FooterBottom, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(BottomContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LegalLinks, {\n          children: legalLinks.map((link, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LegalLink, {\n            href: link.url,\n            whileHover: {\n              color: '#4a90e2'\n            },\n            children: link.name\n          }, link.name))\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(Copyright, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(CopyrightText, {\n            children: \"\\xA9 2024 \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0. \\u0412\\u0441\\u0435 \\u043F\\u0440\\u0430\\u0432\\u0430 \\u0437\\u0430\\u0449\\u0438\\u0449\\u0435\\u043D\\u044B.\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechInfo, {\n            children: \"\\u0420\\u0430\\u0431\\u043E\\u0442\\u0430\\u0435\\u0442 \\u043D\\u0430 \\u043A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0445 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u0445 \\u0438 \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0418\\u0418\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoweredBy, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoweredText, {\n            children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u043D\\u043E \\u0441 \\u2764\\uFE0F \\u043A\\u043E\\u043C\\u0430\\u043D\\u0434\\u043E\\u0439 \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechStack, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechItem, {\n              children: \"React\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechItem, {\n              children: \"Three.js\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechItem, {\n              children: \"Web3\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechItem, {\n              children: \"AI/ML\"\n            })]\n          })]\n        })]\n      })\n    })]\n  });\n};\n\n// Стилизованные компоненты\nconst FooterContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().footer)`\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, #4a90e2, transparent);\n  }\n`;\nconst FooterContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 4rem 2rem 2rem;\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1.5fr;\n  gap: 3rem;\n  \n  @media (max-width: 1024px) {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 2rem;\n  }\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n    padding: 2rem 1rem 1rem;\n  }\n`;\nconst FooterSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst BrandSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst Logo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\nconst LogoIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 3rem;\n  filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.5));\n`;\nconst LogoText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst LogoTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.8rem;\n  font-weight: 900;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  line-height: 1;\n`;\nconst LogoVersion = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1rem;\n  color: #4a90e2;\n  font-weight: 700;\n`;\nconst BrandDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n`;\nconst TechBadges = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n`;\nconst TechBadge = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  background: rgba(74, 144, 226, 0.2);\n  color: #4a90e2;\n  padding: 0.3rem 0.8rem;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  font-size: 1.3rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: white;\n`;\nconst LinksList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\nconst LinkItem = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a)`\n  color: rgba(255, 255, 255, 0.7);\n  text-decoration: none;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    color: #4a90e2;\n  }\n`;\nconst SocialLinks = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.75rem;\n  margin-bottom: 1.5rem;\n`;\nconst SocialLink = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  text-decoration: none;\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => `${props.color}20`};\n    border-color: ${props => props.color};\n  }\n`;\nconst SocialIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 1.2rem;\n`;\nconst SocialName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 0.85rem;\n  font-weight: 600;\n`;\nconst CommunityStats = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n`;\nconst StatItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 1rem 0.5rem;\n`;\nconst StatValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #4a90e2;\n  margin-bottom: 0.25rem;\n`;\nconst StatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\nconst NewsletterDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 1.5rem;\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\nconst NewsletterForm = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().form)`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n`;\nconst EmailInput = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().input)`\n  flex: 1;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 0.75rem;\n  color: white;\n  font-size: 0.9rem;\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: #4a90e2;\n    background: rgba(255, 255, 255, 0.15);\n  }\n`;\nconst SubscribeButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: linear-gradient(135deg, #4a90e2, #7b68ee);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  cursor: pointer;\n  white-space: nowrap;\n`;\nconst NewsletterBenefits = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\nconst BenefitItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.85rem;\n`;\nconst FooterBottom = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 2rem 0;\n`;\nconst BottomContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: grid;\n  grid-template-columns: 1fr auto 1fr;\n  gap: 2rem;\n  align-items: center;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    text-align: center;\n    gap: 1rem;\n    padding: 0 1rem;\n  }\n`;\nconst LegalLinks = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1.5rem;\n  \n  @media (max-width: 768px) {\n    justify-content: center;\n  }\n`;\nconst LegalLink = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a)`\n  color: rgba(255, 255, 255, 0.6);\n  text-decoration: none;\n  font-size: 0.8rem;\n  transition: color 0.3s ease;\n  \n  &:hover {\n    color: #4a90e2;\n  }\n`;\nconst Copyright = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n`;\nconst CopyrightText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n  margin-bottom: 0.25rem;\n`;\nconst TechInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.75rem;\n`;\nconst PoweredBy = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: right;\n  \n  @media (max-width: 768px) {\n    text-align: center;\n  }\n`;\nconst PoweredText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.85rem;\n  margin-bottom: 0.5rem;\n`;\nconst TechStack = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n  \n  @media (max-width: 768px) {\n    justify-content: center;\n  }\n`;\nconst TechItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  background: rgba(74, 144, 226, 0.1);\n  color: #4a90e2;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n");

/***/ })

};
;