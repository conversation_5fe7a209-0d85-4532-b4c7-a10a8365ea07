"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_Web3Dashboard_tsx";
exports.ids = ["src_components_Web3Dashboard_tsx"];
exports.modules = {

/***/ "./src/components/Web3Dashboard.tsx":
/*!******************************************!*\
  !*** ./src/components/Web3Dashboard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst Web3Dashboard = ({\n  web3Status,\n  onConnectWallet\n}) => {\n  const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('wallet');\n  const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [userAddress, setUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const [tokenBalances, setTokenBalances] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [nftCollection, setNftCollection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [defiPools, setDefiPools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [totalPortfolioValue, setTotalPortfolioValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция данных Web3\n    if (walletConnected) {\n      setUserAddress('******************************************');\n      setTokenBalances([{\n        symbol: 'KOZYR',\n        balance: 15420.5,\n        value: 7710.25,\n        change24h: 12.5\n      }, {\n        symbol: 'ETH',\n        balance: 2.45,\n        value: 4900,\n        change24h: -3.2\n      }, {\n        symbol: 'USDC',\n        balance: 1250,\n        value: 1250,\n        change24h: 0.1\n      }]);\n      setNftCollection([{\n        id: '1',\n        name: 'Legendary King',\n        image: '👑',\n        rarity: 'legendary',\n        power: 95,\n        price: 2.5,\n        isStaked: true\n      }, {\n        id: '2',\n        name: 'Epic Queen',\n        image: '👸',\n        rarity: 'epic',\n        power: 85,\n        price: 1.2,\n        isStaked: false\n      }, {\n        id: '3',\n        name: 'Rare Ace',\n        image: '🃏',\n        rarity: 'rare',\n        power: 75,\n        price: 0.8,\n        isStaked: true\n      }]);\n      setDefiPools([{\n        id: '1',\n        name: 'KOZYR/ETH',\n        apr: 145.2,\n        tvl: 2500000,\n        userStaked: 1500,\n        rewards: 25.4\n      }, {\n        id: '2',\n        name: 'KOZYR/USDC',\n        apr: 89.7,\n        tvl: 1800000,\n        userStaked: 800,\n        rewards: 12.1\n      }]);\n      setTotalPortfolioValue(13860.25);\n    }\n  }, [walletConnected]);\n  const connectWallet = async () => {\n    // Симуляция подключения кошелька\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    setWalletConnected(true);\n    onConnectWallet();\n  };\n  const getRarityColor = rarity => {\n    switch (rarity) {\n      case 'legendary':\n        return '#ffd700';\n      case 'epic':\n        return '#9370db';\n      case 'rare':\n        return '#4a90e2';\n      default:\n        return '#6b7280';\n    }\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Web3Container, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n          children: \"Web3 \\u042D\\u043A\\u043E\\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionSubtitle, {\n          children: \"\\u0414\\u0435\\u0446\\u0435\\u043D\\u0442\\u0440\\u0430\\u043B\\u0438\\u0437\\u043E\\u0432\\u0430\\u043D\\u043D\\u043E\\u0435 \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0435 \\u043A\\u0430\\u0440\\u0442\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438\\u0433\\u0440\"\n        })]\n      }), !walletConnected ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletConnection, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ConnectionCard, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8\n          },\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ConnectionIcon, {\n            children: \"\\uD83D\\uDD17\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ConnectionTitle, {\n            children: \"\\u041F\\u043E\\u0434\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 \\u043A\\u043E\\u0448\\u0435\\u043B\\u0451\\u043A\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ConnectionDescription, {\n            children: \"\\u041F\\u043E\\u0434\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 Web3 \\u043A\\u043E\\u0448\\u0435\\u043B\\u0451\\u043A \\u0434\\u043B\\u044F \\u0434\\u043E\\u0441\\u0442\\u0443\\u043F\\u0430 \\u043A NFT, DeFi \\u0438 DAO \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u044F\\u043C\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(WalletOptions, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(WalletOption, {\n              onClick: connectWallet,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletIcon, {\n                children: \"\\uD83E\\uDD8A\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletName, {\n                children: \"MetaMask\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(WalletOption, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletIcon, {\n                children: \"\\uD83C\\uDF08\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletName, {\n                children: \"Rainbow\"\n              })]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(WalletOption, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletIcon, {\n                children: \"\\uD83D\\uDC99\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(WalletName, {\n                children: \"Coinbase\"\n              })]\n            })]\n          })]\n        })\n      }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DashboardContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DashboardHeader, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(UserInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(UserAvatar, {\n              children: \"\\uD83D\\uDC64\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(UserDetails, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(UserAddress, {\n                children: [userAddress.slice(0, 6), \"...\", userAddress.slice(-4)]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PortfolioValue, {\n                children: [\"$\", totalPortfolioValue.toLocaleString()]\n              })]\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NetworkInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NetworkIndicator, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NetworkName, {\n              children: \"Ethereum Mainnet\"\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabNavigation, {\n          children: [{\n            id: 'wallet',\n            label: 'Кошелёк',\n            icon: '💰'\n          }, {\n            id: 'nft',\n            label: 'NFT',\n            icon: '🎴'\n          }, {\n            id: 'defi',\n            label: 'DeFi',\n            icon: '🏦'\n          }, {\n            id: 'dao',\n            label: 'DAO',\n            icon: '🗳️'\n          }].map(tab => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TabButton, {\n            active: activeTab === tab.id,\n            onClick: () => setActiveTab(tab.id),\n            whileHover: {\n              scale: 1.02\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabIcon, {\n              children: tab.icon\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabLabel, {\n              children: tab.label\n            })]\n          }, tab.id))\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TabContent, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n            mode: \"wait\",\n            children: [activeTab === 'wallet' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TokenGrid, {\n                children: tokenBalances.map(token => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TokenCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TokenHeader, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TokenSymbol, {\n                      children: token.symbol\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TokenChange, {\n                      positive: token.change24h > 0,\n                      children: [token.change24h > 0 ? '↗' : '↘', \" \", Math.abs(token.change24h), \"%\"]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TokenBalance, {\n                    children: token.balance.toLocaleString()\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TokenValue, {\n                    children: [\"$\", token.value.toLocaleString()]\n                  })]\n                }, token.symbol))\n              })\n            }, \"wallet\"), activeTab === 'nft' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NFTGrid, {\n                children: nftCollection.map(nft => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NFTCard, {\n                  rarity: nft.rarity,\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NFTImage, {\n                    children: nft.image\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NFTInfo, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NFTName, {\n                      children: nft.name\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(NFTRarity, {\n                      rarity: nft.rarity,\n                      children: nft.rarity.toUpperCase()\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NFTPower, {\n                      children: [\"\\u26A1 \", nft.power]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(NFTPrice, {\n                      children: [nft.price, \" ETH\"]\n                    })]\n                  }), nft.isStaked && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(StakedBadge, {\n                    children: \"STAKED\"\n                  })]\n                }, nft.id))\n              })\n            }, \"nft\"), activeTab === 'defi' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DeFiGrid, {\n                children: defiPools.map(pool => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolCard, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolHeader, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolName, {\n                      children: pool.name\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolAPR, {\n                      children: [pool.apr, \"% APR\"]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStats, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolStatLabel, {\n                        children: \"TVL\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStatValue, {\n                        children: [\"$\", (pool.tvl / 1000000).toFixed(1), \"M\"]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolStatLabel, {\n                        children: \"\\u0412\\u0430\\u0448 \\u0441\\u0442\\u0435\\u0439\\u043A\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStatValue, {\n                        children: [\"$\", pool.userStaked]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStat, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolStatLabel, {\n                        children: \"\\u041D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolStatValue, {\n                        children: [pool.rewards, \" KOZYR\"]\n                      })]\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PoolActions, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolButton, {\n                      whileHover: {\n                        scale: 1.02\n                      },\n                      whileTap: {\n                        scale: 0.98\n                      },\n                      children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PoolButton, {\n                      secondary: true,\n                      whileHover: {\n                        scale: 1.02\n                      },\n                      whileTap: {\n                        scale: 0.98\n                      },\n                      children: \"\\u0417\\u0430\\u0431\\u0440\\u0430\\u0442\\u044C\"\n                    })]\n                  })]\n                }, pool.id))\n              })\n            }, \"defi\"), activeTab === 'dao' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n              initial: {\n                opacity: 0,\n                x: 50\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -50\n              },\n              transition: {\n                duration: 0.3\n              },\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DAOSection, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DAOStats, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatValue, {\n                      children: \"15,420\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatLabel, {\n                      children: \"\\u0412\\u0430\\u0448\\u0430 voting power\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatValue, {\n                      children: \"7\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatLabel, {\n                      children: \"\\u0410\\u043A\\u0442\\u0438\\u0432\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439\"\n                    })]\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DAOStat, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatValue, {\n                      children: \"89%\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DAOStatLabel, {\n                      children: \"\\u0423\\u0447\\u0430\\u0441\\u0442\\u0438\\u0435 \\u0432 \\u0433\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u043D\\u0438\\u0438\"\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProposalsList, {\n                  children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ProposalCard, {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProposalTitle, {\n                      children: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u044C \\u043D\\u043E\\u0432\\u0443\\u044E \\u0438\\u0433\\u0440\\u0443: \\u0411\\u043B\\u044D\\u043A\\u0434\\u0436\\u0435\\u043A\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProposalDescription, {\n                      children: \"\\u041F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u043E \\u0434\\u043E\\u0431\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0438 \\u0431\\u043B\\u044D\\u043A\\u0434\\u0436\\u0435\\u043A\\u0430 \\u0432 \\u043F\\u043B\\u0430\\u0442\\u0444\\u043E\\u0440\\u043C\\u0443\"\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ProposalVotes, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(VoteOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteLabel, {\n                          children: \"\\u0417\\u0430: 85%\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteBar, {\n                          width: 85\n                        })]\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(VoteOption, {\n                        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteLabel, {\n                          children: \"\\u041F\\u0440\\u043E\\u0442\\u0438\\u0432: 15%\"\n                        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteBar, {\n                          width: 15\n                        })]\n                      })]\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ProposalActions, {\n                      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteButton, {\n                        whileHover: {\n                          scale: 1.02\n                        },\n                        whileTap: {\n                          scale: 0.98\n                        },\n                        children: \"\\u0413\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u0442\\u044C \\u0417\\u0410\"\n                      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(VoteButton, {\n                        secondary: true,\n                        whileHover: {\n                          scale: 1.02\n                        },\n                        whileTap: {\n                          scale: 0.98\n                        },\n                        children: \"\\u0413\\u043E\\u043B\\u043E\\u0441\\u043E\\u0432\\u0430\\u0442\\u044C \\u041F\\u0420\\u041E\\u0422\\u0418\\u0412\"\n                      })]\n                    })]\n                  })\n                })]\n              })\n            }, \"dao\")]\n          })\n        })]\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты (первая часть)\nconst Web3Container = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #16213e 0%, #0f0f23 50%, #1a1a2e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  width: 100%;\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2)`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst SectionSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\nconst WalletConnection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n`;\nconst ConnectionCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 3rem;\n  text-align: center;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  max-width: 500px;\n  width: 100%;\n`;\nconst ConnectionIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n`;\nconst ConnectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  font-size: 2rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 1rem;\n`;\nconst ConnectionDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 2rem;\n  line-height: 1.6;\n`;\nconst WalletOptions = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst WalletOption = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 15px;\n  padding: 1.5rem 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-color: #4a90e2;\n  }\n`;\nconst WalletIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\nconst WalletName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\n\n// Стилизованные компоненты (продолжение)\nconst DashboardContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst DashboardHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst UserInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\nconst UserAvatar = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 50px;\n  height: 50px;\n  background: linear-gradient(135deg, #4a90e2, #7b68ee);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n`;\nconst UserDetails = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst UserAddress = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\nconst PortfolioValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: #4ade80;\n  font-weight: 700;\n  font-size: 1.3rem;\n`;\nconst NetworkInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\nconst NetworkIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 12px;\n  height: 12px;\n  background: #4ade80;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n`;\nconst NetworkName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\nconst TabNavigation = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 0.5rem;\n`;\nconst TabButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem;\n  border: none;\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.3)' : 'transparent'};\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.7)'};\n  border-radius: 10px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-weight: 600;\n\n  &:hover {\n    background: rgba(74, 144, 226, 0.1);\n    color: #4a90e2;\n  }\n`;\nconst TabIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 1.2rem;\n`;\nconst TabLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 0.9rem;\n`;\nconst TabContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 400px;\n`;\nconst TokenGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n`;\nconst TokenCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst TokenHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;\nconst TokenSymbol = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 700;\n  font-size: 1.2rem;\n`;\nconst TokenChange = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: ${props => props.positive ? '#4ade80' : '#ef4444'};\n  font-weight: 600;\n  font-size: 0.9rem;\n`;\nconst TokenBalance = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\nconst TokenValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 1rem;\n`;\nconst NFTGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n`;\nconst NFTCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: relative;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 2px solid ${props => getRarityColor(props.rarity)};\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, ${props => getRarityColor(props.rarity)}20, transparent);\n    pointer-events: none;\n  }\n`;\nconst NFTImage = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  text-align: center;\n  margin-bottom: 1rem;\n  filter: drop-shadow(0 0 20px currentColor);\n`;\nconst NFTInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: relative;\n  z-index: 1;\n`;\nconst NFTName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 700;\n  font-size: 1.1rem;\n  margin-bottom: 0.5rem;\n`;\nconst NFTRarity = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: ${props => getRarityColor(props.rarity)};\n  font-weight: 600;\n  font-size: 0.8rem;\n  text-transform: uppercase;\n  margin-bottom: 0.5rem;\n`;\nconst NFTPower = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: #fbbf24;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n`;\nconst NFTPrice = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\nconst StakedBadge = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background: #4ade80;\n  color: black;\n  padding: 0.25rem 0.5rem;\n  border-radius: 5px;\n  font-size: 0.7rem;\n  font-weight: 700;\n`;\nconst DeFiGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\nconst PoolCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst PoolHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\nconst PoolName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 700;\n  font-size: 1.2rem;\n`;\nconst PoolAPR = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: #4ade80;\n  font-weight: 700;\n  font-size: 1.1rem;\n`;\nconst PoolStats = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\nconst PoolStat = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n`;\nconst PoolStatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 0.8rem;\n  margin-bottom: 0.25rem;\n`;\nconst PoolStatValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-weight: 600;\n`;\nconst PoolActions = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 1rem;\n`;\nconst PoolButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  background: ${props => props.secondary ? 'transparent' : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};\n  color: white;\n  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};\n  border-radius: 8px;\n  padding: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\nconst DAOSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst DAOStats = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n`;\nconst DAOStat = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst DAOStatValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: #4a90e2;\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\nconst DAOStatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\nconst ProposalsList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)``;\nconst ProposalCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst ProposalTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  color: white;\n  font-size: 1.2rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n`;\nconst ProposalDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 1.5rem;\n  line-height: 1.5;\n`;\nconst ProposalVotes = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 1.5rem;\n`;\nconst VoteOption = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 1rem;\n`;\nconst VoteLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: white;\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n`;\nconst VoteBar = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 100%;\n  height: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  overflow: hidden;\n\n  &::after {\n    content: '';\n    display: block;\n    width: ${props => props.width}%;\n    height: 100%;\n    background: linear-gradient(90deg, #4a90e2, #7b68ee);\n    transition: width 0.8s ease;\n  }\n`;\nconst ProposalActions = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 1rem;\n`;\nconst VoteButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  flex: 1;\n  background: ${props => props.secondary ? 'transparent' : 'linear-gradient(135deg, #4a90e2, #7b68ee)'};\n  color: white;\n  border: ${props => props.secondary ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'};\n  border-radius: 8px;\n  padding: 0.75rem;\n  font-weight: 600;\n  cursor: pointer;\n`;\n\n// Вспомогательная функция\nconst getRarityColor = rarity => {\n  switch (rarity) {\n    case 'legendary':\n      return '#ffd700';\n    case 'epic':\n      return '#9370db';\n    case 'rare':\n      return '#4a90e2';\n    default:\n      return '#6b7280';\n  }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Web3Dashboard);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Web3Dashboard.tsx\n");

/***/ })

};
;