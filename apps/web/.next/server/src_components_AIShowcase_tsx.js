"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_AIShowcase_tsx";
exports.ids = ["src_components_AIShowcase_tsx"];
exports.modules = {

/***/ "./src/components/AIShowcase.tsx":
/*!***************************************!*\
  !*** ./src/components/AIShowcase.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst emotionalMetrics = [{\n  name: 'Счастье',\n  value: 0.85,\n  color: '#4ade80',\n  icon: '😊',\n  description: 'Уровень позитивных эмоций игрока'\n}, {\n  name: 'Фокус',\n  value: 0.92,\n  color: '#3b82f6',\n  icon: '🎯',\n  description: 'Концентрация и внимание к игре'\n}, {\n  name: 'Уверенность',\n  value: 0.78,\n  color: '#8b5cf6',\n  icon: '💪',\n  description: 'Уверенность в принятии решений'\n}, {\n  name: 'Стресс',\n  value: 0.23,\n  color: '#ef4444',\n  icon: '😰',\n  description: 'Уровень стресса и напряжения'\n}, {\n  name: 'Мотивация',\n  value: 0.89,\n  color: '#f59e0b',\n  icon: '🔥',\n  description: 'Желание продолжать игру'\n}, {\n  name: 'Усталость',\n  value: 0.31,\n  color: '#6b7280',\n  icon: '😴',\n  description: 'Уровень усталости игрока'\n}];\nconst aiFeatures = [{\n  title: 'Анализ эмоций в реальном времени',\n  description: 'ИИ анализирует микровыражения, тон голоса и поведенческие паттерны',\n  icon: '🧠',\n  metrics: ['Точность: 95%', 'Задержка: <100мс', 'Источников данных: 8']\n}, {\n  title: 'Предиктивная аналитика',\n  description: 'Предсказание следующих ходов и вероятности победы',\n  icon: '🔮',\n  metrics: ['Точность предсказаний: 85%', 'Анализируемых факторов: 50+', 'Обновлений/сек: 1000+']\n}, {\n  title: 'Персонализированное обучение',\n  description: 'Адаптивные уроки и рекомендации на основе стиля игры',\n  icon: '📚',\n  metrics: ['Персональных путей: ∞', 'Адаптация: в реальном времени', 'Эффективность: +300%']\n}, {\n  title: 'Детекция тильта и выгорания',\n  description: 'Раннее обнаружение негативных состояний и превентивные меры',\n  icon: '🛡️',\n  metrics: ['Точность детекции: 99%', 'Время реакции: <1сек', 'Предотвращённых тильтов: 10K+']\n}];\nconst AIShowcase = ({\n  emotionalState\n}) => {\n  const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [currentMetrics, setCurrentMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(emotionalMetrics);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Симуляция обновления метрик в реальном времени\n    const interval = setInterval(() => {\n      setCurrentMetrics(prev => prev.map(metric => ({\n        ...metric,\n        value: Math.max(0, Math.min(1, metric.value + (Math.random() - 0.5) * 0.1))\n      })));\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Автоматическое переключение функций\n    const interval = setInterval(() => {\n      setActiveFeature(prev => (prev + 1) % aiFeatures.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(AIContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionTitle, {\n          children: \"\\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SectionSubtitle, {\n          children: \"\\u0420\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u0430\\u044F \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430 \\u043F\\u043E\\u043D\\u0438\\u043C\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430 \\u0438\\u0433\\u0440\\u043E\\u043A\\u043E\\u0432\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MainContent, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricsPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PanelHeader, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(PanelTitle, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(AnalysisIndicator, {\n                active: isAnalyzing\n              }), \"\\u0410\\u043D\\u0430\\u043B\\u0438\\u0437 \\u0432 \\u0440\\u0435\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u0438\"]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(PanelSubtitle, {\n              children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u0438\\u0440\\u0443\\u0435\\u0442 8 \\u044D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0441\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u0438\\u0439 \\u043E\\u0434\\u043D\\u043E\\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u043D\\u043E\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricsGrid, {\n            children: currentMetrics.map((metric, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricCard, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              whileHover: {\n                scale: 1.05\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricHeader, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricIcon, {\n                  children: metric.icon\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricName, {\n                  children: metric.name\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(MetricValue, {\n                color: metric.color,\n                children: [(metric.value * 100).toFixed(0), \"%\"]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricBar, {\n                children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricProgress, {\n                  color: metric.color,\n                  width: metric.value * 100,\n                  initial: {\n                    width: 0\n                  },\n                  animate: {\n                    width: `${metric.value * 100}%`\n                  },\n                  transition: {\n                    duration: 0.8,\n                    delay: index * 0.1\n                  }\n                })\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(MetricDescription, {\n                children: metric.description\n              })]\n            }, metric.name))\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeaturesPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureSelector, {\n            children: aiFeatures.map((feature, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeatureTab, {\n              active: index === activeFeature,\n              onClick: () => setActiveFeature(index),\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureTabIcon, {\n                children: feature.icon\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureTabTitle, {\n                children: feature.title\n              })]\n            }, index))\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureContent, {\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.AnimatePresence, {\n              mode: \"wait\",\n              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 50\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                exit: {\n                  opacity: 0,\n                  x: -50\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeatureHeader, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureIcon, {\n                    children: aiFeatures[activeFeature].icon\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n                    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureTitle, {\n                      children: aiFeatures[activeFeature].title\n                    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureDescription, {\n                      children: aiFeatures[activeFeature].description\n                    })]\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(FeatureMetrics, {\n                  children: aiFeatures[activeFeature].metrics.map((metric, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(FeatureMetric, {\n                    initial: {\n                      opacity: 0,\n                      y: 20\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    transition: {\n                      delay: index * 0.1\n                    },\n                    children: [\"\\u2728 \", metric]\n                  }, index))\n                })]\n              }, activeFeature)\n            })\n          })]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DemoSection, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoTitle, {\n          children: \"\\u041F\\u043E\\u043F\\u0440\\u043E\\u0431\\u0443\\u0439\\u0442\\u0435 \\u0418\\u0418 \\u0432 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u0438\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DemoGrid, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoIcon, {\n              children: \"\\uD83C\\uDFAE\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardTitle, {\n              children: \"\\u0418\\u0433\\u0440\\u043E\\u0432\\u0430\\u044F \\u0441\\u0435\\u0441\\u0441\\u0438\\u044F\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardDescription, {\n              children: \"\\u041D\\u0430\\u0447\\u043D\\u0438\\u0442\\u0435 \\u0438\\u0433\\u0440\\u0443 \\u0438 \\u043D\\u0430\\u0431\\u043B\\u044E\\u0434\\u0430\\u0439\\u0442\\u0435 \\u0437\\u0430 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u043E\\u043C \\u0418\\u0418\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoIcon, {\n              children: \"\\uD83D\\uDCCA\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardTitle, {\n              children: \"\\u0410\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardDescription, {\n              children: \"\\u0418\\u0437\\u0443\\u0447\\u0438\\u0442\\u0435 \\u0434\\u0435\\u0442\\u0430\\u043B\\u044C\\u043D\\u0443\\u044E \\u0430\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0443 \\u0441\\u0432\\u043E\\u0435\\u0439 \\u0438\\u0433\\u0440\\u044B\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(DemoCard, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoIcon, {\n              children: \"\\uD83C\\uDFAF\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardTitle, {\n              children: \"\\u041F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DemoCardDescription, {\n              children: \"\\u041F\\u043E\\u043B\\u0443\\u0447\\u0438\\u0442\\u0435 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0440\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0430\\u0446\\u0438\\u0438\"\n            })]\n          })]\n        })]\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\nconst AIContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  max-width: 1400px;\n  width: 100%;\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2)`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst SectionSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\nconst MainContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 3rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n`;\nconst MetricsPanel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 2rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst PanelHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 2rem;\n`;\nconst PanelTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\nconst PanelSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\nconst AnalysisIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: ${props => props.active ? '#4ade80' : '#6b7280'};\n  animation: ${props => props.active ? 'pulse 2s infinite' : 'none'};\n`;\nconst MetricsGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst MetricCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst MetricHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n`;\nconst MetricIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 1.5rem;\n`;\nconst MetricName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-weight: 600;\n  color: white;\n`;\nconst MetricValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 2rem;\n  font-weight: 700;\n  color: ${props => props.color};\n  margin-bottom: 0.5rem;\n`;\nconst MetricBar = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 100%;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 3px;\n  overflow: hidden;\n  margin-bottom: 0.5rem;\n`;\nconst MetricProgress = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  height: 100%;\n  background: ${props => props.color};\n  border-radius: 3px;\n`;\nconst MetricDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.6);\n  line-height: 1.4;\n`;\nconst FeaturesPanel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  padding: 2rem;\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst FeatureSelector = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n  margin-bottom: 2rem;\n  \n  @media (max-width: 640px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst FeatureTab = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: ${props => props.active ? 'rgba(74, 144, 226, 0.2)' : 'transparent'};\n  border: 1px solid ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.1)'};\n  border-radius: 10px;\n  padding: 1rem;\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.8)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n`;\nconst FeatureTabIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.5rem;\n  margin-bottom: 0.5rem;\n`;\nconst FeatureTabTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.9rem;\n  font-weight: 600;\n`;\nconst FeatureContent = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 200px;\n`;\nconst FeatureHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\nconst FeatureIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 3rem;\n  color: #4a90e2;\n`;\nconst FeatureTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\nconst FeatureDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.7);\n  line-height: 1.6;\n`;\nconst FeatureMetrics = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\nconst FeatureMetric = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\nconst DemoSection = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n`;\nconst DemoTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  font-size: 2rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 2rem;\n`;\nconst DemoGrid = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 2rem;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst DemoCard = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 2rem;\n  text-align: center;\n  cursor: pointer;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\nconst DemoIcon = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 3rem;\n  margin-bottom: 1rem;\n`;\nconst DemoCardTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h4)`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: white;\n  margin-bottom: 0.5rem;\n`;\nconst DemoCardDescription = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n  line-height: 1.5;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIShowcase);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AIShowcase.tsx\n");

/***/ })

};
;