"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_LoadingScreen_tsx";
exports.ids = ["src_components_LoadingScreen_tsx"];
exports.modules = {

/***/ "./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingScreen: () => (/* binding */ LoadingScreen)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst LoadingScreen = ({\n  message = \"Инициализация квантовых систем...\"\n}) => {\n  const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [currentMessage, setCurrentMessage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(message);\n  const loadingMessages = [\"Подключение к квантовым источникам...\", \"Инициализация эмоционального ИИ...\", \"Загрузка 3D метавселенной...\", \"Настройка блокчейн соединения...\", \"Активация предиктивной аналитики...\", \"Запуск систем безопасности...\", \"Финализация загрузки...\"];\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        const newProgress = prev + Math.random() * 15;\n        if (newProgress >= 100) {\n          clearInterval(interval);\n          return 100;\n        }\n\n        // Обновляем сообщение в зависимости от прогресса\n        const messageIndex = Math.floor(newProgress / 100 * loadingMessages.length);\n        if (messageIndex < loadingMessages.length) {\n          setCurrentMessage(loadingMessages[messageIndex]);\n        }\n        return newProgress;\n      });\n    }, 200);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(LoadingContainer, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ParticlesBackground, {\n      children: Array.from({\n        length: 50\n      }).map((_, i) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Particle, {\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 3}s`,\n          animationDuration: `${3 + Math.random() * 4}s`\n        }\n      }, i))\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(LogoContainer, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n          animate: {\n            rotate: 360,\n            scale: [1, 1.1, 1]\n          },\n          transition: {\n            rotate: {\n              duration: 4,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            scale: {\n              duration: 2,\n              repeat: Infinity\n            }\n          },\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Logo, {\n            children: \"\\uD83C\\uDFAE\"\n          })\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.5\n          },\n          children: \"\\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440 4.0\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(QuantumIndicator, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(QuantumRing, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"linear\"\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(QuantumOrb, {})\n          })\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(QuantumText, {\n          children: \"\\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u0430\\u044F \\u0438\\u043D\\u0438\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ProgressContainer, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProgressBar, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            initial: {\n              width: 0\n            },\n            animate: {\n              width: `${progress}%`\n            },\n            transition: {\n              duration: 0.3\n            },\n            style: {\n              height: '100%',\n              background: 'linear-gradient(90deg, #4a90e2, #7b68ee, #9370db)',\n              borderRadius: '10px',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ProgressGlow, {})\n          })\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(ProgressText, {\n          children: [Math.round(progress), \"%\"]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 10\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -10\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(LoadingMessage, {\n          children: currentMessage\n        })\n      }, currentMessage), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SystemIndicators, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SystemIndicator, {\n          active: progress > 20,\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(IndicatorDot, {\n            active: progress > 20\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n            children: \"\\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0439 \\u0434\\u0432\\u0438\\u0436\\u043E\\u043A\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SystemIndicator, {\n          active: progress > 40,\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(IndicatorDot, {\n            active: progress > 40\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n            children: \"\\u042D\\u043C\\u043E\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0418\\u0418\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SystemIndicator, {\n          active: progress > 60,\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(IndicatorDot, {\n            active: progress > 60\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n            children: \"\\u041C\\u0435\\u0442\\u0430\\u0432\\u0441\\u0435\\u043B\\u0435\\u043D\\u043D\\u0430\\u044F\"\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(SystemIndicator, {\n          active: progress > 80,\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(IndicatorDot, {\n            active: progress > 80\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n            children: \"Web3 \\u044D\\u043A\\u043E\\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430\"\n          })]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechDetails, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechLabel, {\n            children: \"\\u042D\\u043D\\u0442\\u0440\\u043E\\u043F\\u0438\\u044F:\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechValue, {\n            children: (progress / 100 * 0.999).toFixed(3)\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechLabel, {\n            children: \"\\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u044B\\u0435 \\u0438\\u0441\\u0442\\u043E\\u0447\\u043D\\u0438\\u043A\\u0438:\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechValue, {\n            children: [Math.min(5, Math.floor(progress / 20)), \"/5\"]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(TechLabel, {\n            children: \"\\u0418\\u0418 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438:\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(TechValue, {\n            children: [Math.min(6, Math.floor(progress / 16)), \"/6\"]\n          })]\n        })]\n      })]\n    })]\n  });\n};\n\n// Стилизованные компоненты\nconst LoadingContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  overflow: hidden;\n`;\nconst ParticlesBackground = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n`;\nconst Particle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  width: 2px;\n  height: 2px;\n  background: rgba(74, 144, 226, 0.6);\n  border-radius: 50%;\n  animation: float linear infinite;\n\n  @keyframes float {\n    0% {\n      transform: translateY(100vh) scale(0);\n      opacity: 0;\n    }\n    10% {\n      opacity: 1;\n    }\n    90% {\n      opacity: 1;\n    }\n    100% {\n      transform: translateY(-100px) scale(1);\n      opacity: 0;\n    }\n  }\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n  color: white;\n  z-index: 1;\n`;\nconst LogoContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 3rem;\n\n  h1 {\n    font-size: 2.5rem;\n    font-weight: 700;\n    background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    margin-top: 1rem;\n  }\n`;\nconst Logo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  filter: drop-shadow(0 0 20px rgba(74, 144, 226, 0.5));\n`;\nconst QuantumIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  margin-bottom: 3rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n`;\nconst QuantumRing = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 80px;\n  height: 80px;\n  border: 2px solid rgba(74, 144, 226, 0.3);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  margin-bottom: 1rem;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: -2px;\n    left: -2px;\n    right: -2px;\n    bottom: -2px;\n    border-radius: 50%;\n    background: conic-gradient(from 0deg, transparent, #4a90e2, transparent);\n    animation: spin 2s linear infinite;\n  }\n\n  @keyframes spin {\n    to { transform: rotate(360deg); }\n  }\n`;\nconst QuantumOrb = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 20px;\n  height: 20px;\n  background: radial-gradient(circle, #4a90e2, #7b68ee);\n  border-radius: 50%;\n  box-shadow: 0 0 20px rgba(74, 144, 226, 0.8);\n  z-index: 1;\n`;\nconst QuantumText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\nconst ProgressContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 400px;\n  margin-bottom: 2rem;\n  \n  @media (max-width: 480px) {\n    width: 300px;\n  }\n`;\nconst ProgressBar = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 100%;\n  height: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  overflow: hidden;\n  margin-bottom: 0.5rem;\n  position: relative;\n`;\nconst ProgressGlow = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  animation: shimmer 2s infinite;\n\n  @keyframes shimmer {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(100%); }\n  }\n`;\nconst ProgressText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #4a90e2;\n`;\nconst LoadingMessage = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 2rem;\n  min-height: 1.5rem;\n`;\nconst SystemIndicators = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 1rem;\n  margin-bottom: 2rem;\n  max-width: 400px;\n  \n  @media (max-width: 480px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst SystemIndicator = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.9rem;\n  color: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.5)'};\n  transition: color 0.3s ease;\n`;\nconst IndicatorDot = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${props => props.active ? '#4a90e2' : 'rgba(255, 255, 255, 0.3)'};\n  box-shadow: ${props => props.active ? '0 0 10px rgba(74, 144, 226, 0.5)' : 'none'};\n  transition: all 0.3s ease;\n`;\nconst TechDetails = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  font-size: 0.8rem;\n  \n  @media (max-width: 480px) {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n`;\nconst TechItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n`;\nconst TechLabel = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: rgba(255, 255, 255, 0.6);\n`;\nconst TechValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  color: #4a90e2;\n  font-weight: 600;\n`;\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/LoadingScreen.tsx\n");

/***/ })

};
;