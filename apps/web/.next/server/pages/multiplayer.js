/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/multiplayer";
exports.ids = ["pages/multiplayer"];
exports.modules = {

/***/ "./src/app/store/index.ts":
/*!********************************!*\
  !*** ./src/app/store/index.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @entities/game/model/durakSlice */ \"./src/entities/game/model/durakSlice.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__]);\n([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Импорт редьюсеров\n// import { authReducer } from '@features/auth/model/slice';\n// import { gameReducer } from \"@entities/game/model/slice\"; // Removed old game slice\n // Import the new Durak slice\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n  reducer: {\n    // auth: authReducer,\n    // game: gameReducer, // Removed old game slice\n    durak: _entities_game_model_durakSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"] // Add the Durak game slice\n    // Здесь будут добавлены другие редьюсеры\n  },\n\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: false\n  })\n});\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvYXBwL3N0b3JlL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDs7QUFFbEQ7QUFDQTtBQUNBO0FBQzJELENBQUM7O0FBRXJELE1BQU1FLEtBQUssR0FBR0YsZ0VBQWMsQ0FBQztFQUNsQ0csT0FBTyxFQUFFO0lBQ1A7SUFDQTtJQUNBQyxLQUFLLEVBQUVILHVFQUFZLENBQUU7SUFDckI7RUFDRixDQUFDOztFQUNESSxVQUFVLEVBQUdDLG9CQUFvQixJQUMvQkEsb0JBQW9CLENBQUM7SUFDbkJDLGlCQUFpQixFQUFFO0VBQ3JCLENBQUM7QUFDTCxDQUFDLENBQUMsQyIsInNvdXJjZXMiOlsid2VicGFjazovL2tvenlyLW1hc3Rlci13ZWIvLi9zcmMvYXBwL3N0b3JlL2luZGV4LnRzPzljZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29uZmlndXJlU3RvcmUgfSBmcm9tIFwiQHJlZHV4anMvdG9vbGtpdFwiO1xuXG4vLyDQmNC80L/QvtGA0YIg0YDQtdC00YzRjtGB0LXRgNC+0LJcbi8vIGltcG9ydCB7IGF1dGhSZWR1Y2VyIH0gZnJvbSAnQGZlYXR1cmVzL2F1dGgvbW9kZWwvc2xpY2UnO1xuLy8gaW1wb3J0IHsgZ2FtZVJlZHVjZXIgfSBmcm9tIFwiQGVudGl0aWVzL2dhbWUvbW9kZWwvc2xpY2VcIjsgLy8gUmVtb3ZlZCBvbGQgZ2FtZSBzbGljZVxuaW1wb3J0IGR1cmFrUmVkdWNlciBmcm9tICdAZW50aXRpZXMvZ2FtZS9tb2RlbC9kdXJha1NsaWNlJzsgLy8gSW1wb3J0IHRoZSBuZXcgRHVyYWsgc2xpY2VcblxuZXhwb3J0IGNvbnN0IHN0b3JlID0gY29uZmlndXJlU3RvcmUoe1xuICByZWR1Y2VyOiB7XG4gICAgLy8gYXV0aDogYXV0aFJlZHVjZXIsXG4gICAgLy8gZ2FtZTogZ2FtZVJlZHVjZXIsIC8vIFJlbW92ZWQgb2xkIGdhbWUgc2xpY2VcbiAgICBkdXJhazogZHVyYWtSZWR1Y2VyLCAvLyBBZGQgdGhlIER1cmFrIGdhbWUgc2xpY2VcbiAgICAvLyDQl9C00LXRgdGMINCx0YPQtNGD0YIg0LTQvtCx0LDQstC70LXQvdGLINC00YDRg9Cz0LjQtSDRgNC10LTRjNGO0YHQtdGA0YtcbiAgfSxcbiAgbWlkZGxld2FyZTogKGdldERlZmF1bHRNaWRkbGV3YXJlKSA9PlxuICAgIGdldERlZmF1bHRNaWRkbGV3YXJlKHtcbiAgICAgIHNlcmlhbGl6YWJsZUNoZWNrOiBmYWxzZSxcbiAgICB9KSxcbn0pO1xuXG5leHBvcnQgdHlwZSBSb290U3RhdGUgPSBSZXR1cm5UeXBlPHR5cGVvZiBzdG9yZS5nZXRTdGF0ZT47XG5leHBvcnQgdHlwZSBBcHBEaXNwYXRjaCA9IHR5cGVvZiBzdG9yZS5kaXNwYXRjaDtcbiJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsImR1cmFrUmVkdWNlciIsInN0b3JlIiwicmVkdWNlciIsImR1cmFrIiwibWlkZGxld2FyZSIsImdldERlZmF1bHRNaWRkbGV3YXJlIiwic2VyaWFsaXphYmxlQ2hlY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/app/store/index.ts\n");

/***/ }),

/***/ "./src/entities/game/model/durakSlice.ts":
/*!***********************************************!*\
  !*** ./src/entities/game/model/durakSlice.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeGame: () => (/* binding */ initializeGame),\n/* harmony export */   makeMove: () => (/* binding */ makeMove),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   startGame: () => (/* binding */ startGame)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @kozyr-master/core */ \"../../packages/core/dist/index.js\");\n/* harmony import */ var _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n // Импортируем типы из пакета core\n\n// Начальное состояние для среза\nconst initialState = {\n  players: [],\n  deck: [],\n  tableCards: [],\n  discardPile: [],\n  trumpCard: undefined,\n  trumpSuit: null,\n  // Инициализируем как null или подходящим значением по умолчанию\n  currentPlayerIndex: -1,\n  attackerIndex: -1,\n  defenderIndex: -1,\n  gameStatus: _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED,\n  winner: undefined,\n  passCount: 0,\n  gameInstance: null,\n  // Добавляем для хранения экземпляра игры\n  error: null // Для хранения сообщений об ошибках\n};\n\nconst durakSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n  name: 'durak',\n  initialState,\n  reducers: {\n    // Редьюсер для инициализации новой игры\n    initializeGame: (state, action) => {\n      try {\n        const game = new _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.DurakGame(action.payload.players, action.payload.rules);\n        const gameState = game.getState();\n        // Обновляем состояние Redux из состояния игры\n        Object.assign(state, gameState);\n        state.gameInstance = game; // Сохраняем экземпляр игры\n        state.gameStatus = _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED; // Устанавливаем статус\n        state.error = null;\n      } catch (e) {\n        state.error = e.message || 'Failed to initialize game';\n        console.error(\"Error initializing game:\", e);\n      }\n    },\n    // Редьюсер для старта игры\n    startGame: state => {\n      if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.NOT_STARTED) {\n        try {\n          state.gameInstance.startGame();\n          const newState = state.gameInstance.getState();\n          Object.assign(state, newState);\n          state.error = null;\n        } catch (e) {\n          state.error = e.message || 'Failed to start game';\n          console.error(\"Error starting game:\", e);\n        }\n      } else {\n        state.error = 'Game instance not available or game already started/finished.';\n      }\n    },\n    // Редьюсер для выполнения хода\n    makeMove: (state, action) => {\n      if (state.gameInstance && state.gameStatus === _kozyr_master_core__WEBPACK_IMPORTED_MODULE_1__.GameStatus.IN_PROGRESS) {\n        try {\n          const {\n            playerId,\n            action: playerAction,\n            cardIndex\n          } = action.payload;\n          const moveSuccessful = state.gameInstance.makeMove(playerId, playerAction, cardIndex);\n          if (moveSuccessful) {\n            const newState = state.gameInstance.getState();\n            Object.assign(state, newState);\n            state.error = null;\n          } else {\n            // Можно установить ошибку, если ход не удался, но DurakGame уже логирует ошибки\n            // state.error = 'Invalid move';\n            console.warn('Move was not successful according to game logic.');\n          }\n        } catch (e) {\n          state.error = e.message || 'Failed to make move';\n          console.error(\"Error making move:\", e);\n        }\n      } else {\n        state.error = 'Game instance not available or game not in progress.';\n      }\n    },\n    // Можно добавить другие редьюсеры по мере необходимости\n    setError: (state, action) => {\n      state.error = action.payload;\n    }\n  }\n});\nconst {\n  initializeGame,\n  startGame,\n  makeMove,\n  setError\n} = durakSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (durakSlice.reducer);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/entities/game/model/durakSlice.ts\n");

/***/ }),

/***/ "./src/hooks/useSocket.ts":
/*!********************************!*\
  !*** ./src/hooks/useSocket.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"socket.io-client\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([socket_io_client__WEBPACK_IMPORTED_MODULE_1__]);\nsocket_io_client__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst useSocket = (serverUrl = 'http://localhost:3002') => {\n  const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    connected: false,\n    player: null,\n    currentRoom: null,\n    rooms: [],\n    chatMessages: [],\n    gameState: null,\n    error: null,\n    playerRating: null,\n    ratingCategory: null,\n    playerAchievements: null,\n    leaderboard: [],\n    topPlayers: null,\n    tournaments: [],\n    currentTournament: null,\n    spectatorGames: [],\n    currentSpectatorGame: null,\n    spectatorChatMessages: [],\n    isSpectating: false,\n    friends: [],\n    friendRequests: [],\n    gameInvitations: [],\n    notifications: []\n  });\n\n  // Подключение к серверу\n  const connect = () => {\n    if (socketRef.current?.connected) return;\n    socketRef.current = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(serverUrl, {\n      transports: ['websocket', 'polling']\n    });\n    const socket = socketRef.current;\n\n    // Обработчики подключения\n    socket.on('connect', () => {\n      setState(prev => ({\n        ...prev,\n        connected: true,\n        error: null\n      }));\n    });\n    socket.on('disconnect', () => {\n      setState(prev => ({\n        ...prev,\n        connected: false\n      }));\n    });\n    socket.on('error', error => {\n      setState(prev => ({\n        ...prev,\n        error: error.message\n      }));\n    });\n\n    // Обработчики игрока\n    socket.on('player_registered', data => {\n      setState(prev => ({\n        ...prev,\n        player: {\n          id: data.playerId,\n          name: data.name,\n          isOnline: true,\n          joinedAt: new Date()\n        }\n      }));\n    });\n\n    // Обработчики комнат\n    socket.on('rooms_list', rooms => {\n      setState(prev => ({\n        ...prev,\n        rooms\n      }));\n    });\n    socket.on('room_created', data => {\n      setState(prev => ({\n        ...prev,\n        currentRoom: data.room,\n        rooms: [...prev.rooms, data.room]\n      }));\n    });\n    socket.on('room_joined', data => {\n      setState(prev => ({\n        ...prev,\n        currentRoom: data.room\n      }));\n    });\n    socket.on('room_left', () => {\n      setState(prev => ({\n        ...prev,\n        currentRoom: null,\n        chatMessages: []\n      }));\n    });\n    socket.on('room_added', room => {\n      setState(prev => ({\n        ...prev,\n        rooms: [...prev.rooms.filter(r => r.id !== room.id), room]\n      }));\n    });\n    socket.on('player_joined', data => {\n      setState(prev => ({\n        ...prev,\n        currentRoom: data.room\n      }));\n    });\n    socket.on('player_left', data => {\n      setState(prev => ({\n        ...prev,\n        currentRoom: data.room\n      }));\n    });\n\n    // Обработчики игры\n    socket.on('game_started', data => {\n      setState(prev => ({\n        ...prev,\n        gameState: data.gameState\n      }));\n    });\n    socket.on('game_updated', data => {\n      setState(prev => ({\n        ...prev,\n        gameState: data.gameState\n      }));\n    });\n\n    // Обработчики чата\n    socket.on('chat_message', message => {\n      setState(prev => ({\n        ...prev,\n        chatMessages: [...prev.chatMessages.slice(-49), message] // Ограничиваем 50 сообщениями\n      }));\n    });\n\n    // Обработчики рейтингов и достижений\n    socket.on('player_rating', data => {\n      setState(prev => ({\n        ...prev,\n        playerRating: data.rating,\n        ratingCategory: data.category\n      }));\n    });\n    socket.on('player_achievements', achievements => {\n      setState(prev => ({\n        ...prev,\n        playerAchievements: achievements\n      }));\n    });\n    socket.on('leaderboard', leaderboard => {\n      setState(prev => ({\n        ...prev,\n        leaderboard\n      }));\n    });\n    socket.on('top_players', topPlayers => {\n      setState(prev => ({\n        ...prev,\n        topPlayers\n      }));\n    });\n    socket.on('game_finished', data => {\n      // Обновляем рейтинг игрока если это его игра\n      if (data.winner.player.id === state.player?.id) {\n        setState(prev => ({\n          ...prev,\n          playerRating: data.winner.rating\n        }));\n      } else if (data.loser.player.id === state.player?.id) {\n        setState(prev => ({\n          ...prev,\n          playerRating: data.loser.rating\n        }));\n      }\n    });\n\n    // Обработчики турниров\n    socket.on('tournament_created', tournament => {\n      setState(prev => ({\n        ...prev,\n        tournaments: [...prev.tournaments, tournament]\n      }));\n    });\n    socket.on('tournament_added', tournament => {\n      setState(prev => ({\n        ...prev,\n        tournaments: [...prev.tournaments, tournament]\n      }));\n    });\n    socket.on('tournament_updated', tournament => {\n      setState(prev => ({\n        ...prev,\n        tournaments: prev.tournaments.map(t => t.id === tournament.id ? tournament : t),\n        currentTournament: prev.currentTournament?.id === tournament.id ? tournament : prev.currentTournament\n      }));\n    });\n    socket.on('tournament_registered', data => {\n      setState(prev => ({\n        ...prev,\n        tournaments: prev.tournaments.map(t => t.id === data.tournament.id ? data.tournament : t),\n        currentTournament: data.tournament\n      }));\n    });\n    socket.on('tournaments_list', tournaments => {\n      setState(prev => ({\n        ...prev,\n        tournaments\n      }));\n    });\n    socket.on('tournament_details', tournament => {\n      setState(prev => ({\n        ...prev,\n        currentTournament: tournament\n      }));\n    });\n\n    // Обработчики спектаторов\n    socket.on('spectator_games_list', games => {\n      setState(prev => ({\n        ...prev,\n        spectatorGames: games\n      }));\n    });\n    socket.on('spectator_game_added', gameInfo => {\n      setState(prev => ({\n        ...prev,\n        spectatorGames: [...prev.spectatorGames, gameInfo]\n      }));\n    });\n    socket.on('spectator_game_updated', data => {\n      setState(prev => ({\n        ...prev,\n        spectatorGames: prev.spectatorGames.map(game => game.gameId === data.gameId ? {\n          ...game,\n          gameState: data.gameState\n        } : game),\n        currentSpectatorGame: prev.currentSpectatorGame?.gameId === data.gameId ? {\n          ...prev.currentSpectatorGame,\n          gameState: data.gameState\n        } : prev.currentSpectatorGame\n      }));\n    });\n    socket.on('spectator_joined', data => {\n      setState(prev => ({\n        ...prev,\n        currentSpectatorGame: data.game,\n        isSpectating: true,\n        spectatorChatMessages: []\n      }));\n    });\n    socket.on('spectator_left', data => {\n      setState(prev => ({\n        ...prev,\n        currentSpectatorGame: null,\n        isSpectating: false,\n        spectatorChatMessages: []\n      }));\n    });\n    socket.on('spectator_added', data => {\n      setState(prev => ({\n        ...prev,\n        currentSpectatorGame: prev.currentSpectatorGame ? {\n          ...prev.currentSpectatorGame,\n          spectators: [...prev.currentSpectatorGame.spectators, data.spectator]\n        } : prev.currentSpectatorGame\n      }));\n    });\n    socket.on('spectator_removed', data => {\n      setState(prev => ({\n        ...prev,\n        currentSpectatorGame: prev.currentSpectatorGame ? {\n          ...prev.currentSpectatorGame,\n          spectators: prev.currentSpectatorGame.spectators.filter(s => s.playerId !== data.playerId)\n        } : prev.currentSpectatorGame\n      }));\n    });\n    socket.on('spectator_chat_message', message => {\n      setState(prev => ({\n        ...prev,\n        spectatorChatMessages: [...prev.spectatorChatMessages.slice(-49), message]\n      }));\n    });\n\n    // Обработчики друзей\n    socket.on('friends_list', friends => {\n      setState(prev => ({\n        ...prev,\n        friends\n      }));\n    });\n    socket.on('friend_requests_list', requests => {\n      setState(prev => ({\n        ...prev,\n        friendRequests: requests\n      }));\n    });\n    socket.on('game_invitations_list', invitations => {\n      setState(prev => ({\n        ...prev,\n        gameInvitations: invitations\n      }));\n    });\n    socket.on('notifications_list', notifications => {\n      setState(prev => ({\n        ...prev,\n        notifications\n      }));\n    });\n    socket.on('friend_request_received', request => {\n      setState(prev => ({\n        ...prev,\n        friendRequests: [...prev.friendRequests, request],\n        notifications: [...prev.notifications, {\n          id: `notif_${Date.now()}`,\n          playerId: prev.player?.id || '',\n          type: 'friend_request',\n          title: 'Новый запрос в друзья',\n          message: `${request.fromPlayerName} хочет добавить вас в друзья`,\n          data: {\n            requestId: request.id\n          },\n          read: false,\n          createdAt: new Date()\n        }]\n      }));\n    });\n    socket.on('friend_request_response', data => {\n      if (data.friendship) {\n        // Обновляем список друзей\n        setState(prev => ({\n          ...prev,\n          notifications: [...prev.notifications, {\n            id: `notif_${Date.now()}`,\n            playerId: prev.player?.id || '',\n            type: 'friend_accepted',\n            title: 'Запрос принят',\n            message: `${data.request.toPlayerName} принял ваш запрос в друзья`,\n            data: {\n              friendshipId: data.friendship?.id\n            },\n            read: false,\n            createdAt: new Date()\n          }]\n        }));\n      }\n    });\n    socket.on('game_invitation_received', invitation => {\n      setState(prev => ({\n        ...prev,\n        gameInvitations: [...prev.gameInvitations, invitation],\n        notifications: [...prev.notifications, {\n          id: `notif_${Date.now()}`,\n          playerId: prev.player?.id || '',\n          type: 'game_invitation',\n          title: 'Приглашение в игру',\n          message: `${invitation.fromPlayerName} приглашает вас в игру${invitation.roomName ? ` \"${invitation.roomName}\"` : ''}`,\n          data: {\n            invitationId: invitation.id\n          },\n          read: false,\n          createdAt: new Date()\n        }]\n      }));\n    });\n    socket.on('friend_removed', data => {\n      setState(prev => ({\n        ...prev,\n        friends: prev.friends.filter(friend => friend.playerId !== data.friendId)\n      }));\n    });\n  };\n\n  // Отключение от сервера\n  const disconnect = () => {\n    if (socketRef.current) {\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n    setState({\n      connected: false,\n      player: null,\n      currentRoom: null,\n      rooms: [],\n      chatMessages: [],\n      gameState: null,\n      error: null,\n      playerRating: null,\n      ratingCategory: null,\n      playerAchievements: null,\n      leaderboard: [],\n      topPlayers: null,\n      tournaments: [],\n      currentTournament: null,\n      spectatorGames: [],\n      currentSpectatorGame: null,\n      spectatorChatMessages: [],\n      isSpectating: false,\n      friends: [],\n      friendRequests: [],\n      gameInvitations: [],\n      notifications: []\n    });\n  };\n\n  // Регистрация игрока\n  const registerPlayer = name => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('register_player', {\n      name\n    });\n  };\n\n  // Создание комнаты\n  const createRoom = (name, maxPlayers = 2) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('create_room', {\n      name,\n      maxPlayers\n    });\n  };\n\n  // Присоединение к комнате\n  const joinRoom = roomId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('join_room', {\n      roomId\n    });\n  };\n\n  // Покидание комнаты\n  const leaveRoom = roomId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('leave_room', {\n      roomId\n    });\n  };\n\n  // Запуск игры\n  const startGame = roomId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('start_game', {\n      roomId\n    });\n  };\n\n  // Ход в игре\n  const makeMove = (roomId, action, cardIndex) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('game_move', {\n      roomId,\n      action,\n      cardIndex\n    });\n  };\n\n  // Отправка сообщения в чат\n  const sendChatMessage = (roomId, message) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('chat_message', {\n      roomId,\n      message\n    });\n  };\n\n  // Получение списка комнат\n  const getRooms = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_rooms');\n  };\n\n  // Получение рейтинга игрока\n  const getPlayerRating = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_player_rating');\n  };\n\n  // Получение достижений игрока\n  const getPlayerAchievements = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_player_achievements');\n  };\n\n  // Получение таблицы лидеров\n  const getLeaderboard = limit => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_leaderboard', {\n      limit\n    });\n  };\n\n  // Получение топ игроков\n  const getTopPlayers = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_top_players');\n  };\n\n  // Создание турнира\n  const createTournament = (name, description, type, maxParticipants, settings) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('create_tournament', {\n      name,\n      description,\n      type,\n      maxParticipants,\n      settings\n    });\n  };\n\n  // Регистрация на турнир\n  const registerForTournament = tournamentId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('register_for_tournament', {\n      tournamentId\n    });\n  };\n\n  // Получение списка турниров\n  const getTournaments = status => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_tournaments', {\n      status\n    });\n  };\n\n  // Получение деталей турнира\n  const getTournament = tournamentId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_tournament', {\n      tournamentId\n    });\n  };\n\n  // Получение списка игр для просмотра\n  const getSpectatorGames = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_spectator_games');\n  };\n\n  // Присоединение как зритель\n  const joinSpectator = gameId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('join_spectator', {\n      gameId\n    });\n  };\n\n  // Выход из режима зрителя\n  const leaveSpectator = gameId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('leave_spectator', {\n      gameId\n    });\n  };\n\n  // Отправка сообщения в чат зрителей\n  const sendSpectatorChatMessage = (gameId, message) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('spectator_chat_message', {\n      gameId,\n      message\n    });\n  };\n\n  // Отправка запроса в друзья\n  const sendFriendRequest = (toPlayerId, toPlayerName) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('send_friend_request', {\n      toPlayerId,\n      toPlayerName\n    });\n  };\n\n  // Ответ на запрос в друзья\n  const respondToFriendRequest = (requestId, accept) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('respond_to_friend_request', {\n      requestId,\n      accept\n    });\n  };\n\n  // Удаление друга\n  const removeFriend = friendId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('remove_friend', {\n      friendId\n    });\n  };\n\n  // Отправка приглашения в игру\n  const sendGameInvitation = (toPlayerId, toPlayerName, gameType, roomId, roomName, message) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('send_game_invitation', {\n      toPlayerId,\n      toPlayerName,\n      gameType,\n      roomId,\n      roomName,\n      message\n    });\n  };\n\n  // Ответ на приглашение в игру\n  const respondToGameInvitation = (invitationId, accept) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('respond_to_game_invitation', {\n      invitationId,\n      accept\n    });\n  };\n\n  // Получение списка друзей\n  const getFriends = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_friends');\n  };\n\n  // Получение запросов в друзья\n  const getFriendRequests = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_friend_requests');\n  };\n\n  // Получение приглашений в игры\n  const getGameInvitations = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_game_invitations');\n  };\n\n  // Получение уведомлений\n  const getNotifications = (unreadOnly = false) => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('get_notifications', {\n      unreadOnly\n    });\n  };\n\n  // Пометить уведомление как прочитанное\n  const markNotificationAsRead = notificationId => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('mark_notification_read', {\n      notificationId\n    });\n  };\n\n  // Пометить все уведомления как прочитанные\n  const markAllNotificationsAsRead = () => {\n    if (!socketRef.current) return;\n    socketRef.current.emit('mark_all_notifications_read');\n  };\n\n  // Очистка ошибки\n  const clearError = () => {\n    setState(prev => ({\n      ...prev,\n      error: null\n    }));\n  };\n\n  // Автоматическое подключение при монтировании\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    connect();\n    return () => disconnect();\n  }, [serverUrl]);\n  return {\n    // Состояние\n    ...state,\n    // Методы\n    connect,\n    disconnect,\n    registerPlayer,\n    createRoom,\n    joinRoom,\n    leaveRoom,\n    startGame,\n    makeMove,\n    sendChatMessage,\n    getRooms,\n    getPlayerRating,\n    getPlayerAchievements,\n    getLeaderboard,\n    getTopPlayers,\n    createTournament,\n    registerForTournament,\n    getTournaments,\n    getTournament,\n    getSpectatorGames,\n    joinSpectator,\n    leaveSpectator,\n    sendSpectatorChatMessage,\n    sendFriendRequest,\n    respondToFriendRequest,\n    removeFriend,\n    sendGameInvitation,\n    respondToGameInvitation,\n    getFriends,\n    getFriendRequests,\n    getGameInvitations,\n    getNotifications,\n    markNotificationAsRead,\n    markAllNotificationsAsRead,\n    clearError,\n    // Утилиты\n    isConnected: state.connected,\n    isInRoom: !!state.currentRoom,\n    isGameActive: state.gameState?.status === 'playing'\n  };\n};\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useSocket.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"react-redux\");\n/* harmony import */ var _app_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/store */ \"./src/app/store/index.ts\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/styles/globals.css */ \"./src/app/styles/globals.css\");\n/* harmony import */ var _app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_app_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_redux__WEBPACK_IMPORTED_MODULE_0__, _app_store__WEBPACK_IMPORTED_MODULE_1__]);\n([react_redux__WEBPACK_IMPORTED_MODULE_0__, _app_store__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nfunction MyApp({\n  Component,\n  pageProps\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(react_redux__WEBPACK_IMPORTED_MODULE_0__.Provider, {\n    store: _app_store__WEBPACK_IMPORTED_MODULE_1__.store,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Component, {\n      ...pageProps\n    })\n  });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUN1QztBQUNKO0FBQ0Y7QUFBQTtBQUVqQyxTQUFTSSxLQUFLQSxDQUFDO0VBQUVDLFNBQVM7RUFBRUM7QUFBb0IsQ0FBQyxFQUFFO0VBQ2pELG9CQUNFSCxzREFBQSxDQUFDSCxpREFBUTtJQUFDQyxLQUFLLEVBQUVBLDZDQUFNO0lBQUFNLFFBQUEsZUFDckJKLHNEQUFBLENBQUNFLFNBQVM7TUFBQSxHQUFLQztJQUFTLENBQUc7RUFBQyxDQUNwQixDQUFDO0FBRWY7QUFFQSxpRUFBZUYsS0FBSyxFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va296eXItbWFzdGVyLXdlYi8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFByb3BzIH0gZnJvbSBcIm5leHQvYXBwXCI7XG5pbXBvcnQgeyBQcm92aWRlciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xuaW1wb3J0IHsgc3RvcmUgfSBmcm9tIFwiQGFwcC9zdG9yZVwiO1xuaW1wb3J0IFwiQGFwcC9zdHlsZXMvZ2xvYmFscy5jc3NcIjtcblxuZnVuY3Rpb24gTXlBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxQcm92aWRlciBzdG9yZT17c3RvcmV9PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IE15QXBwO1xuIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJqc3giLCJfanN4IiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/multiplayer.tsx":
/*!***********************************!*\
  !*** ./src/pages/multiplayer.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"./src/hooks/useSocket.ts\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__]);\n_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst MultiplayerPage = () => {\n  const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n  const socket = (0,_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__.useSocket)();\n  const [playerName, setPlayerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n  const [roomName, setRoomName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n  const [chatMessage, setChatMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n  const [showCreateRoom, setShowCreateRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n  const handleBackToHome = () => {\n    socket.disconnect();\n    router.push(\"/\");\n  };\n  const handleRegisterPlayer = () => {\n    if (playerName.trim()) {\n      socket.registerPlayer(playerName.trim());\n    }\n  };\n  const handleCreateRoom = () => {\n    if (roomName.trim()) {\n      socket.createRoom(roomName.trim(), 2);\n      setShowCreateRoom(false);\n      setRoomName(\"\");\n    }\n  };\n  const handleJoinRoom = roomId => {\n    socket.joinRoom(roomId);\n  };\n  const handleLeaveRoom = () => {\n    if (socket.currentRoom) {\n      socket.leaveRoom(socket.currentRoom.id);\n    }\n  };\n  const handleStartGame = () => {\n    if (socket.currentRoom) {\n      socket.startGame(socket.currentRoom.id);\n    }\n  };\n  const handleSendMessage = () => {\n    if (chatMessage.trim() && socket.currentRoom) {\n      socket.sendChatMessage(socket.currentRoom.id, chatMessage.trim());\n      setChatMessage(\"\");\n    }\n  };\n  const handleRefreshRooms = () => {\n    socket.getRooms();\n  };\n\n  // Автоматически обновляем список комнат и загружаем рейтинг\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (socket.connected && socket.player) {\n      socket.getRooms();\n      socket.getPlayerRating();\n    }\n  }, [socket.connected, socket.player]);\n\n  // Перенаправляем на игровую страницу при запуске игры\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (socket.gameState && socket.gameState.status === 'playing') {\n      router.push('/multiplayer/game');\n    }\n  }, [socket.gameState, router]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Container, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)((next_head__WEBPACK_IMPORTED_MODULE_0___default()), {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"title\", {\n        children: \"\\u041C\\u043D\\u043E\\u0433\\u043E\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u0430\\u044F \\u0438\\u0433\\u0440\\u0430 - \\u041A\\u043E\\u0437\\u044B\\u0440\\u044C \\u041C\\u0430\\u0441\\u0442\\u0435\\u0440\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"meta\", {\n        name: \"description\",\n        content: \"\\u0418\\u0433\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0432 \\u0414\\u0443\\u0440\\u0430\\u043A\\u0430 \\u0441 \\u0434\\u0440\\u0443\\u0433\\u0438\\u043C\\u0438 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\\u043C\\u0438 \\u043E\\u043D\\u043B\\u0430\\u0439\\u043D\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Header, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(BackButton, {\n        onClick: handleBackToHome,\n        children: \"\\u2190 \\u041D\\u0430\\u0437\\u0430\\u0434\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Title, {\n        children: \"\\u041C\\u043D\\u043E\\u0433\\u043E\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u0430\\u044F \\u0438\\u0433\\u0440\\u0430\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ConnectionStatus, {\n        $connected: socket.connected,\n        children: socket.connected ? \"🟢 Подключен\" : \"🔴 Отключен\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Main, {\n      children: [socket.error && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ErrorMessage, {\n        children: [socket.error, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(CloseButton, {\n          onClick: socket.clearError,\n          children: \"\\xD7\"\n        })]\n      }), !socket.player ?\n      /*#__PURE__*/\n      // Регистрация игрока\n      (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Section, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SectionTitle, {\n          children: \"\\u0412\\u043E\\u0439\\u0442\\u0438 \\u0432 \\u0438\\u0433\\u0440\\u0443\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(InputGroup, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Input, {\n            type: \"text\",\n            placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0435 \\u0438\\u043C\\u044F\",\n            value: playerName,\n            onChange: e => setPlayerName(e.target.value),\n            onKeyPress: e => e.key === 'Enter' && handleRegisterPlayer()\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Button, {\n            onClick: handleRegisterPlayer,\n            disabled: !playerName.trim(),\n            children: \"\\u0412\\u043E\\u0439\\u0442\\u0438\"\n          })]\n        })]\n      }) : !socket.currentRoom ?\n      /*#__PURE__*/\n      // Список комнат\n      (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(TwoColumnLayout, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Section, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SectionHeader, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SectionTitle, {\n              children: [\"\\u041A\\u043E\\u043C\\u043D\\u0430\\u0442\\u044B (\", socket.rooms.length, \")\"]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ButtonGroup, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SmallButton, {\n                onClick: handleRefreshRooms,\n                children: \"\\uD83D\\uDD04\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Button, {\n                onClick: () => setShowCreateRoom(true),\n                children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u043A\\u043E\\u043C\\u043D\\u0430\\u0442\\u0443\"\n              })]\n            })]\n          }), showCreateRoom && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(CreateRoomForm, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Input, {\n              type: \"text\",\n              placeholder: \"\\u041D\\u0430\\u0437\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043A\\u043E\\u043C\\u043D\\u0430\\u0442\\u044B\",\n              value: roomName,\n              onChange: e => setRoomName(e.target.value),\n              onKeyPress: e => e.key === 'Enter' && handleCreateRoom()\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ButtonGroup, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Button, {\n                onClick: handleCreateRoom,\n                disabled: !roomName.trim(),\n                children: \"\\u0421\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SecondaryButton, {\n                onClick: () => setShowCreateRoom(false),\n                children: \"\\u041E\\u0442\\u043C\\u0435\\u043D\\u0430\"\n              })]\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(RoomsList, {\n            children: socket.rooms.length === 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(EmptyState, {\n              children: \"\\u041D\\u0435\\u0442 \\u0434\\u043E\\u0441\\u0442\\u0443\\u043F\\u043D\\u044B\\u0445 \\u043A\\u043E\\u043C\\u043D\\u0430\\u0442\"\n            }) : socket.rooms.map(room => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RoomCard, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RoomInfo, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(RoomName, {\n                  children: room.name\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RoomDetails, {\n                  children: [\"\\u0412\\u043B\\u0430\\u0434\\u0435\\u043B\\u0435\\u0446: \", room.ownerName, \" | \\u0418\\u0433\\u0440\\u043E\\u043A\\u0438: \", room.playerCount, \"/\", room.maxPlayers, \" | \\u0421\\u0442\\u0430\\u0442\\u0443\\u0441: \", room.status === 'waiting' ? 'Ожидание' : room.status === 'playing' ? 'Играют' : 'Завершена']\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(JoinButton, {\n                onClick: () => handleJoinRoom(room.id),\n                disabled: room.status !== 'waiting' || room.playerCount >= room.maxPlayers,\n                children: room.status !== 'waiting' ? 'Недоступна' : room.playerCount >= room.maxPlayers ? 'Полная' : 'Войти'\n              })]\n            }, room.id))\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Section, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SectionTitle, {\n            children: [\"\\u0414\\u043E\\u0431\\u0440\\u043E \\u043F\\u043E\\u0436\\u0430\\u043B\\u043E\\u0432\\u0430\\u0442\\u044C, \", socket.player.name, \"!\"]\n          }), socket.playerRating && socket.ratingCategory && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(PlayerRatingInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(RatingBadge, {\n              color: socket.ratingCategory.color,\n              children: socket.ratingCategory.name\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RatingValue, {\n              children: [\"\\u0420\\u0435\\u0439\\u0442\\u0438\\u043D\\u0433: \", socket.playerRating.rating]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RatingStats, {\n              children: [\"\\u0418\\u0433\\u0440: \", socket.playerRating.gamesPlayed, \" | \\u041F\\u043E\\u0431\\u0435\\u0434: \", socket.playerRating.wins, \" | \\u0412\\u0438\\u043D\\u0440\\u0435\\u0439\\u0442: \", socket.playerRating.winRate.toFixed(1), \"%\"]\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoText, {\n            children: \"\\u0412\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u043A\\u043E\\u043C\\u043D\\u0430\\u0442\\u0443 \\u0438\\u0437 \\u0441\\u043F\\u0438\\u0441\\u043A\\u0430 \\u0438\\u043B\\u0438 \\u0441\\u043E\\u0437\\u0434\\u0430\\u0439\\u0442\\u0435 \\u043D\\u043E\\u0432\\u0443\\u044E, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443.\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(InfoText, {\n            children: \"\\u0412 \\u043C\\u043D\\u043E\\u0433\\u043E\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u043E\\u043C \\u0440\\u0435\\u0436\\u0438\\u043C\\u0435 \\u0432\\u044B \\u043C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0438\\u0433\\u0440\\u0430\\u0442\\u044C \\u0441 \\u0434\\u0440\\u0443\\u0433\\u0438\\u043C\\u0438 \\u0438\\u0433\\u0440\\u043E\\u043A\\u0430\\u043C\\u0438 \\u0432 \\u0440\\u0435\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u0438.\"\n          })]\n        })]\n      }) :\n      /*#__PURE__*/\n      // В комнате\n      (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(TwoColumnLayout, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Section, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SectionHeader, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(SectionTitle, {\n              children: [\"\\u041A\\u043E\\u043C\\u043D\\u0430\\u0442\\u0430: \", socket.currentRoom.name]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ButtonGroup, {\n              children: [socket.currentRoom.ownerName === socket.player.name && socket.currentRoom.status === 'waiting' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Button, {\n                onClick: handleStartGame,\n                children: \"\\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SecondaryButton, {\n                onClick: handleLeaveRoom,\n                children: \"\\u041F\\u043E\\u043A\\u0438\\u043D\\u0443\\u0442\\u044C\"\n              })]\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(RoomStatus, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"strong\", {\n                children: \"\\u0421\\u0442\\u0430\\u0442\\u0443\\u0441:\"\n              }), \" \", socket.currentRoom.status === 'waiting' ? 'Ожидание игроков' : socket.currentRoom.status === 'playing' ? 'Игра идет' : 'Игра завершена']\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"strong\", {\n                children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\\u0438:\"\n              }), \" \", socket.currentRoom.playerCount, \"/\", socket.currentRoom.maxPlayers]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatusItem, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"strong\", {\n                children: \"\\u0412\\u043B\\u0430\\u0434\\u0435\\u043B\\u0435\\u0446:\"\n              }), \" \", socket.currentRoom.ownerName]\n            })]\n          }), socket.gameState && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameInfo, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"h4\", {\n              children: \"\\u0421\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u0438\\u0435 \\u0438\\u0433\\u0440\\u044B\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pre\", {\n              children: JSON.stringify(socket.gameState, null, 2)\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(Section, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SectionTitle, {\n            children: \"\\u0427\\u0430\\u0442\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ChatContainer, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ChatMessages, {\n              children: socket.chatMessages.map(msg => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ChatMessage, {\n                $type: msg.type,\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(MessageHeader, {\n                  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(MessageAuthor, {\n                    $type: msg.type,\n                    children: msg.playerName\n                  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(MessageTime, {\n                    children: new Date(msg.timestamp).toLocaleTimeString()\n                  })]\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(MessageText, {\n                  children: msg.message\n                })]\n              }, msg.id))\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ChatInput, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Input, {\n                type: \"text\",\n                placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435...\",\n                value: chatMessage,\n                onChange: e => setChatMessage(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && handleSendMessage()\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Button, {\n                onClick: handleSendMessage,\n                disabled: !chatMessage.trim(),\n                children: \"\\u041E\\u0442\\u043F\\u0440\\u0430\\u0432\\u0438\\u0442\\u044C\"\n              })]\n            })]\n          })]\n        })]\n      })]\n    })]\n  });\n};\n\n// Стилизованные компоненты\nconst Container = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n  color: white;\n  display: flex;\n  flex-direction: column;\n`;\nconst Header = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().header)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 2rem;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n`;\nconst BackButton = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().button)`\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n  }\n`;\nconst Title = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h1)`\n  font-size: 2rem;\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n`;\nconst ConnectionStatus = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  background: ${props => props.$connected ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)'};\n  border: 1px solid ${props => props.$connected ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)'};\n  font-weight: 600;\n`;\nconst Main = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().main)`\n  flex: 1;\n  padding: 2rem;\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n`;\nconst ErrorMessage = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(244, 67, 54, 0.2);\n  border: 1px solid rgba(244, 67, 54, 0.3);\n  padding: 1rem;\n  border-radius: 8px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\nconst CloseButton = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().button)`\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\nconst Section = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().section)`\n  background: rgba(255, 255, 255, 0.1);\n  padding: 2rem;\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h2)`\n  font-size: 1.5rem;\n  margin: 0 0 1rem 0;\n  color: #FFD700;\n`;\nconst SectionHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n  gap: 1rem;\n`;\nconst TwoColumnLayout = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst InputGroup = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  flex-wrap: wrap;\n`;\nconst Input = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().input)`\n  flex: 1;\n  min-width: 200px;\n  padding: 0.75rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  font-size: 1rem;\n\n  &::placeholder {\n    color: rgba(255, 255, 255, 0.6);\n  }\n\n  &:focus {\n    outline: none;\n    border-color: #4CAF50;\n    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);\n  }\n`;\nconst Button = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().button)`\n  background: ${props => props.disabled ? 'rgba(255, 255, 255, 0.1)' : 'linear-gradient(135deg, #4CAF50, #45a049)'};\n  color: ${props => props.disabled ? 'rgba(255, 255, 255, 0.5)' : 'white'};\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};\n  transition: all 0.3s ease;\n  white-space: nowrap;\n\n  &:hover {\n    ${props => !props.disabled && `\n      background: linear-gradient(135deg, #45a049, #4CAF50);\n      transform: translateY(-2px);\n    `}\n  }\n`;\nconst SecondaryButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(Button)`\n  background: linear-gradient(135deg, #2196F3, #1976D2);\n\n  &:hover {\n    background: linear-gradient(135deg, #1976D2, #2196F3);\n  }\n`;\nconst SmallButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(Button)`\n  padding: 0.5rem;\n  min-width: auto;\n`;\nconst ButtonGroup = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: wrap;\n`;\nconst CreateRoomForm = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(0, 0, 0, 0.2);\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\nconst RoomsList = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  max-height: 400px;\n  overflow-y: auto;\n`;\nconst EmptyState = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  text-align: center;\n  padding: 2rem;\n  color: rgba(255, 255, 255, 0.6);\n  font-style: italic;\n`;\nconst RoomCard = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  padding: 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    border-color: rgba(255, 255, 255, 0.2);\n  }\n`;\nconst RoomInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  flex: 1;\n`;\nconst RoomName = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().h3)`\n  margin: 0 0 0.5rem 0;\n  font-size: 1.1rem;\n`;\nconst RoomDetails = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  margin: 0;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\nconst JoinButton = styled_components__WEBPACK_IMPORTED_MODULE_2___default()(Button)`\n  margin-left: 1rem;\n  padding: 0.5rem 1rem;\n`;\nconst InfoText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().p)`\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  margin-bottom: 1rem;\n`;\nconst RoomStatus = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n`;\nconst StatusItem = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.9);\n`;\nconst GameInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(0, 0, 0, 0.2);\n  padding: 1rem;\n  border-radius: 8px;\n  margin-top: 1rem;\n\n  h4 {\n    margin: 0 0 1rem 0;\n    color: #FFD700;\n  }\n\n  pre {\n    background: rgba(0, 0, 0, 0.3);\n    padding: 1rem;\n    border-radius: 4px;\n    overflow-x: auto;\n    font-size: 0.8rem;\n    color: rgba(255, 255, 255, 0.8);\n  }\n`;\nconst ChatContainer = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  flex-direction: column;\n  height: 400px;\n`;\nconst ChatMessages = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\nconst ChatMessage = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  padding: 0.5rem;\n  border-radius: 4px;\n  background: ${props => {\n  switch (props.$type) {\n    case 'system':\n      return 'rgba(255, 193, 7, 0.1)';\n    case 'game':\n      return 'rgba(76, 175, 80, 0.1)';\n    default:\n      return 'rgba(255, 255, 255, 0.05)';\n  }\n}};\n  border-left: 3px solid ${props => {\n  switch (props.$type) {\n    case 'system':\n      return '#FFC107';\n    case 'game':\n      return '#4CAF50';\n    default:\n      return 'rgba(255, 255, 255, 0.2)';\n  }\n}};\n`;\nconst MessageHeader = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.25rem;\n`;\nconst MessageAuthor = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-weight: 600;\n  font-size: 0.9rem;\n  color: ${props => {\n  switch (props.$type) {\n    case 'system':\n      return '#FFC107';\n    case 'game':\n      return '#4CAF50';\n    default:\n      return '#2196F3';\n  }\n}};\n`;\nconst MessageTime = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().span)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.5);\n`;\nconst MessageText = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.9);\n  line-height: 1.4;\n`;\nconst ChatInput = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  display: flex;\n  gap: 0.5rem;\n`;\nconst PlayerRatingInfo = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\nconst RatingBadge = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  background: ${props => props.color}20;\n  border: 1px solid ${props => props.color}40;\n  color: ${props => props.color};\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-weight: 600;\n  display: inline-block;\n  width: fit-content;\n`;\nconst RatingValue = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #FFD700;\n`;\nconst RatingStats = (styled_components__WEBPACK_IMPORTED_MODULE_2___default().div)`\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultiplayerPage);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/multiplayer.tsx\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmultiplayer&preferredRegion=&absolutePagePath=.%2Fpages%2Fmultiplayer.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmultiplayer&preferredRegion=&absolutePagePath=.%2Fpages%2Fmultiplayer.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"../../node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/multiplayer.tsx */ \"./src/pages/multiplayer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/multiplayer\",\n        pathname: \"/multiplayer\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_multiplayer_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmultiplayer&preferredRegion=&absolutePagePath=.%2Fpages%2Fmultiplayer.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/core/dist/durak/bot.js":
/*!*********************************************!*\
  !*** ../../packages/core/dist/durak/bot.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Система ботов для игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotFactory = exports.DurakBot = exports.BotDifficulty = void 0;\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Уровни сложности бота\n */\nvar BotDifficulty;\n(function (BotDifficulty) {\n    BotDifficulty[\"EASY\"] = \"easy\";\n    BotDifficulty[\"MEDIUM\"] = \"medium\";\n    BotDifficulty[\"HARD\"] = \"hard\";\n})(BotDifficulty || (exports.BotDifficulty = BotDifficulty = {}));\n/**\n * Базовый класс бота для игры \"Дурак\"\n */\nclass DurakBot {\n    constructor(id, difficulty = BotDifficulty.MEDIUM) {\n        this.id = id;\n        this.difficulty = difficulty;\n        this.name = `Bot_${difficulty}_${id}`;\n    }\n    /**\n     * Создать игрока-бота\n     */\n    createPlayer() {\n        return {\n            id: this.id,\n            name: this.name,\n            hand: [],\n            isActive: false,\n        };\n    }\n    /**\n     * Принять решение о следующем ходе\n     */\n    makeDecision(gameState, playerId) {\n        const player = gameState.players.find(p => p.id === playerId);\n        if (!player) {\n            throw new Error(`Player ${playerId} not found`);\n        }\n        const isAttacker = gameState.attackerIndex === gameState.players.indexOf(player);\n        const isDefender = gameState.defenderIndex === gameState.players.indexOf(player);\n        // Определяем возможные действия\n        if (isAttacker && gameState.tableCards.length === 0) {\n            // Атакующий должен атаковать\n            return this.decideAttack(gameState, player);\n        }\n        else if (isDefender && this.hasUndefendedCards(gameState)) {\n            // Защитник должен защищаться или брать карты\n            return this.decideDefendOrTake(gameState, player);\n        }\n        else if (isAttacker && this.allCardsDefended(gameState)) {\n            // Атакующий может подкинуть карты или сказать \"бито\"\n            return this.decideThrowOrPass(gameState, player);\n        }\n        else if (!isDefender && this.allCardsDefended(gameState)) {\n            // Другие игроки могут подкинуть карты\n            return this.decideThrow(gameState, player);\n        }\n        // По умолчанию - пас\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Default pass action\",\n        };\n    }\n    /**\n     * Решение об атаке\n     */\n    decideAttack(gameState, player) {\n        const validCards = this.getValidAttackCards(gameState, player);\n        if (validCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid attack cards\",\n            };\n        }\n        // Выбираем карту в зависимости от сложности\n        let cardIndex;\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                // Легкий бот играет случайно\n                cardIndex = validCards[Math.floor(Math.random() * validCards.length)];\n                break;\n            case BotDifficulty.MEDIUM:\n                // Средний бот предпочитает младшие карты\n                cardIndex = this.selectLowestCard(player, validCards, gameState.trumpSuit);\n                break;\n            case BotDifficulty.HARD:\n                // Сложный бот использует стратегию\n                cardIndex = this.selectStrategicAttackCard(player, validCards, gameState);\n                break;\n            default:\n                cardIndex = validCards[0];\n        }\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Attack with card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о защите или взятии карт\n     */\n    decideDefendOrTake(gameState, player) {\n        const lastPair = gameState.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No card to defend against\",\n            };\n        }\n        const attackingCard = lastPair[0];\n        const validDefenseCards = this.getValidDefenseCards(player, attackingCard, gameState.trumpSuit);\n        if (validDefenseCards.length === 0) {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"No valid defense cards\",\n            };\n        }\n        // Решаем, защищаться или брать карты\n        const shouldDefend = this.shouldDefend(gameState, player, validDefenseCards);\n        if (shouldDefend) {\n            const cardIndex = this.selectDefenseCard(validDefenseCards, attackingCard, gameState.trumpSuit);\n            return {\n                action: types_1.PlayerAction.DEFEND,\n                cardIndex,\n                reasoning: `Defend with card at index ${cardIndex}`,\n            };\n        }\n        else {\n            return {\n                action: types_1.PlayerAction.TAKE,\n                reasoning: \"Decided to take cards instead of defending\",\n            };\n        }\n    }\n    /**\n     * Решение о подкидывании или пасе\n     */\n    decideThrowOrPass(gameState, player) {\n        const throwCards = this.getValidThrowCards(gameState, player);\n        if (throwCards.length === 0 || !this.shouldThrow(gameState, player)) {\n            return {\n                action: types_1.PlayerAction.PASS,\n                reasoning: \"No valid throw cards or decided not to throw\",\n            };\n        }\n        const cardIndex = throwCards[0]; // Простая стратегия - первая подходящая карта\n        return {\n            action: types_1.PlayerAction.ATTACK,\n            cardIndex,\n            reasoning: `Throw card at index ${cardIndex}`,\n        };\n    }\n    /**\n     * Решение о подкидывании (для не-атакующих игроков)\n     */\n    decideThrow(gameState, player) {\n        // Пока что не-атакующие игроки не подкидывают (упрощение)\n        return {\n            action: types_1.PlayerAction.PASS,\n            reasoning: \"Non-attacker decided not to throw\",\n        };\n    }\n    // Вспомогательные методы\n    hasUndefendedCards(gameState) {\n        return gameState.tableCards.some(pair => pair.length === 1);\n    }\n    allCardsDefended(gameState) {\n        return gameState.tableCards.length > 0 && gameState.tableCards.every(pair => pair.length === 2);\n    }\n    getValidAttackCards(gameState, player) {\n        if (gameState.tableCards.length === 0) {\n            // Первая атака - любая карта\n            return player.hand.map((_, index) => index);\n        }\n        // Подкидывание - карты с рангами, уже лежащими на столе\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    getValidDefenseCards(player, attackingCard, trumpSuit) {\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => this.canDefend(attackingCard, card, trumpSuit))\n            .map(({ index }) => index);\n    }\n    canDefend(attackCard, defendCard, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6,\n                [types_1.CardRank.SEVEN]: 7,\n                [types_1.CardRank.EIGHT]: 8,\n                [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10,\n                [types_1.CardRank.JACK]: 11,\n                [types_1.CardRank.QUEEN]: 12,\n                [types_1.CardRank.KING]: 13,\n                [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit && getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Козырь бьет не-козырь\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Козырь бьет козырь, если старше\n        if (attackCard.suit === trumpSuit && defendCard.suit === trumpSuit &&\n            getRankValue(defendCard.rank) > getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    getValidThrowCards(gameState, player) {\n        const ranksOnTable = new Set(gameState.tableCards.flat().map(card => card.rank));\n        return player.hand\n            .map((card, index) => ({ card, index }))\n            .filter(({ card }) => ranksOnTable.has(card.rank))\n            .map(({ index }) => index);\n    }\n    shouldDefend(gameState, player, validDefenseCards) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.3; // 70% шанс защищаться\n            case BotDifficulty.MEDIUM:\n                // Защищается, если у него мало карт или много карт на столе\n                return player.hand.length <= 3 || gameState.tableCards.length >= 3;\n            case BotDifficulty.HARD:\n                // Более сложная логика\n                return this.strategicDefendDecision(gameState, player, validDefenseCards);\n            default:\n                return true;\n        }\n    }\n    shouldThrow(gameState, player) {\n        switch (this.difficulty) {\n            case BotDifficulty.EASY:\n                return Math.random() > 0.7; // 30% шанс подкинуть\n            case BotDifficulty.MEDIUM:\n                return player.hand.length > 5; // Подкидывает, если много карт\n            case BotDifficulty.HARD:\n                return this.strategicThrowDecision(gameState, player);\n            default:\n                return false;\n        }\n    }\n    selectLowestCard(player, validIndices, trumpSuit) {\n        const getRankValue = (rank) => {\n            const values = {\n                [types_1.CardRank.SIX]: 6, [types_1.CardRank.SEVEN]: 7, [types_1.CardRank.EIGHT]: 8, [types_1.CardRank.NINE]: 9,\n                [types_1.CardRank.TEN]: 10, [types_1.CardRank.JACK]: 11, [types_1.CardRank.QUEEN]: 12, [types_1.CardRank.KING]: 13, [types_1.CardRank.ACE]: 14,\n            };\n            return values[rank];\n        };\n        return validIndices.reduce((lowestIndex, currentIndex) => {\n            const lowestCard = player.hand[lowestIndex];\n            const currentCard = player.hand[currentIndex];\n            // Предпочитаем не-козыри\n            if (lowestCard.suit === trumpSuit && currentCard.suit !== trumpSuit) {\n                return currentIndex;\n            }\n            if (lowestCard.suit !== trumpSuit && currentCard.suit === trumpSuit) {\n                return lowestIndex;\n            }\n            // Если обе карты одного типа (козыри или не-козыри), выбираем младшую\n            return getRankValue(currentCard.rank) < getRankValue(lowestCard.rank) ? currentIndex : lowestIndex;\n        });\n    }\n    selectStrategicAttackCard(player, validIndices, gameState) {\n        // Пока что используем простую стратегию - младшая карта\n        return this.selectLowestCard(player, validIndices, gameState.trumpSuit);\n    }\n    selectDefenseCard(validIndices, attackingCard, trumpSuit) {\n        // Выбираем первую подходящую карту (можно улучшить)\n        return validIndices[0];\n    }\n    strategicDefendDecision(gameState, player, validDefenseCards) {\n        // Упрощенная стратегическая логика\n        const cardsOnTable = gameState.tableCards.flat().length;\n        const playerCardCount = player.hand.length;\n        // Не защищается, если на столе много карт и у игрока мало карт\n        if (cardsOnTable >= 6 && playerCardCount <= 4) {\n            return false;\n        }\n        return true;\n    }\n    strategicThrowDecision(gameState, player) {\n        // Подкидывает, если у защитника много карт\n        const defender = gameState.players[gameState.defenderIndex];\n        return defender.hand.length > 6;\n    }\n}\nexports.DurakBot = DurakBot;\n/**\n * Фабрика для создания ботов\n */\nclass BotFactory {\n    /**\n     * Создать бота с указанной сложностью\n     */\n    static createBot(difficulty = BotDifficulty.MEDIUM) {\n        const id = `bot_${++this.botCounter}`;\n        return new DurakBot(id, difficulty);\n    }\n    /**\n     * Создать несколько ботов\n     */\n    static createBots(count, difficulty = BotDifficulty.MEDIUM) {\n        return Array.from({ length: count }, () => this.createBot(difficulty));\n    }\n}\nexports.BotFactory = BotFactory;\nBotFactory.botCounter = 0;\n//# sourceMappingURL=bot.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/bot.js\n");

/***/ }),

/***/ "../../packages/core/dist/durak/index.js":
/*!***********************************************!*\
  !*** ../../packages/core/dist/durak/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/**\n * Модуль игры \"Дурак\"\n *\n * Содержит основную логику и правила игры \"Дурак\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DurakGame = void 0;\n// Импортируем общие типы\nconst types_1 = __webpack_require__(/*! ../types */ \"../../packages/core/dist/types.js\");\n/**\n * Класс игры \"Дурак\"\n */\nclass DurakGame {\n    constructor(players, rules) {\n        this.eventHandlers = [];\n        this.rules = rules;\n        this.state = this.initializeGame(players);\n    }\n    /**\n     * Добавить обработчик событий\n     */\n    addEventListener(handler) {\n        this.eventHandlers.push(handler);\n    }\n    /**\n     * Удалить обработчик событий\n     */\n    removeEventListener(handler) {\n        const index = this.eventHandlers.indexOf(handler);\n        if (index > -1) {\n            this.eventHandlers.splice(index, 1);\n        }\n    }\n    /**\n     * Отправить событие всем обработчикам\n     */\n    emitEvent(eventData) {\n        this.eventHandlers.forEach(handler => {\n            try {\n                handler(eventData);\n            }\n            catch (error) {\n                console.error('Error in game event handler:', error);\n            }\n        });\n    }\n    /**\n     * Инициализация игры\n     */\n    initializeGame(players) {\n        // Создание и перемешивание колоды\n        const deck = this.createDeck();\n        this.shuffleDeck(deck);\n        // Определение козырной карты\n        const trumpCard = deck[deck.length - 1];\n        const trumpSuit = trumpCard.suit;\n        // Раздача карт игрокам\n        this.dealCards(players, deck);\n        // Определение первого игрока (у кого наименьший козырь)\n        const firstPlayerIndex = this.determineFirstPlayer(players, trumpSuit);\n        return {\n            players,\n            deck,\n            tableCards: [],\n            discardPile: [],\n            trumpCard,\n            trumpSuit,\n            currentPlayerIndex: firstPlayerIndex,\n            attackerIndex: firstPlayerIndex,\n            defenderIndex: (firstPlayerIndex + 1) % players.length,\n            gameStatus: types_1.GameStatus.NOT_STARTED,\n        };\n    }\n    /**\n     * Создание колоды карт\n     */\n    createDeck() {\n        const deck = [];\n        const suits = Object.values(types_1.CardSuit);\n        const ranks = Object.values(types_1.CardRank);\n        for (const suit of suits) {\n            for (const rank of ranks) {\n                deck.push({ suit, rank });\n            }\n        }\n        return deck;\n    }\n    /**\n     * Перемешивание колоды\n     */\n    shuffleDeck(deck) {\n        for (let i = deck.length - 1; i > 0; i--) {\n            const j = Math.floor(Math.random() * (i + 1));\n            [deck[i], deck[j]] = [deck[j], deck[i]];\n        }\n    }\n    /**\n     * Раздача карт игрокам\n     */\n    dealCards(players, deck) {\n        const cardsPerPlayer = this.rules.initialHandSize;\n        for (let i = 0; i < cardsPerPlayer; i++) {\n            for (const player of players) {\n                if (deck.length > 0) {\n                    const card = deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Определение первого игрока\n     */\n    determineFirstPlayer(players, trumpSuit) {\n        let minTrumpRankIndex = -1;\n        let minTrumpRankValue = Infinity;\n        // Поиск игрока с наименьшим козырем\n        for (let i = 0; i < players.length; i++) {\n            const player = players[i];\n            for (const card of player.hand) {\n                if (card.suit === trumpSuit) {\n                    const rankValue = this.getRankValue(card.rank);\n                    if (rankValue < minTrumpRankValue) {\n                        minTrumpRankValue = rankValue;\n                        minTrumpRankIndex = i;\n                    }\n                }\n            }\n        }\n        // Если ни у кого нет козырей, выбираем случайного игрока\n        if (minTrumpRankIndex === -1) {\n            minTrumpRankIndex = Math.floor(Math.random() * players.length);\n        }\n        return minTrumpRankIndex;\n    }\n    /**\n     * Получение числового значения ранга карты\n     */\n    getRankValue(rank) {\n        const rankValues = {\n            [types_1.CardRank.SIX]: 6,\n            [types_1.CardRank.SEVEN]: 7,\n            [types_1.CardRank.EIGHT]: 8,\n            [types_1.CardRank.NINE]: 9,\n            [types_1.CardRank.TEN]: 10,\n            [types_1.CardRank.JACK]: 11,\n            [types_1.CardRank.QUEEN]: 12,\n            [types_1.CardRank.KING]: 13,\n            [types_1.CardRank.ACE]: 14,\n        };\n        return rankValues[rank];\n    }\n    /**\n     * Получение текущего состояния игры\n     */\n    getState() {\n        return { ...this.state };\n    }\n    /**\n     * Обновление статуса активного игрока\n     */\n    updateActivePlayer() {\n        this.state.players.forEach((player, index) => {\n            player.isActive = index === this.state.currentPlayerIndex;\n        });\n    }\n    /**\n     * Начало игры\n     */\n    startGame() {\n        if (this.state.gameStatus === types_1.GameStatus.NOT_STARTED) {\n            this.state.gameStatus = types_1.GameStatus.IN_PROGRESS;\n            this.updateActivePlayer(); // Устанавливаем первого активного игрока\n            // Отправляем событие о начале игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_STARTED,\n                gameState: this.getState(),\n                message: `Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`,\n            });\n            console.log(`Game started. First turn: Player ${this.state.players[this.state.currentPlayerIndex].id}`);\n        }\n        else {\n            console.warn(\"Game already started or finished.\");\n        }\n    }\n    /**\n     * Проверка, разрешено ли действие игроку в текущем состоянии игры.\n     * @param playerIndex Индекс игрока, выполняющего действие.\n     * @param action Тип действия (атака, защита, пас, взять).\n     * @returns Объект с флагом `allowed` (true/false) и опциональным сообщением об ошибке `error`.\n     */\n    _isActionAllowed(playerIndex, action) {\n        const isCurrentPlayerTurn = playerIndex === this.state.currentPlayerIndex;\n        const isAttacker = playerIndex === this.state.attackerIndex;\n        const isDefender = playerIndex === this.state.defenderIndex;\n        const lastPair = this.state.tableCards.at(-1);\n        const lastPairDefended = !!lastPair && lastPair.length === 2;\n        const tableIsEmpty = this.state.tableCards.length === 0;\n        // 1. Ход текущего игрока (атакующий или защитник)\n        if (isCurrentPlayerTurn) {\n            // Атакующий может атаковать, если стол пуст или после взятия карт защитником\n            if (isAttacker &&\n                action === types_1.PlayerAction.ATTACK &&\n                (tableIsEmpty || this.state.defenderTookCards)) {\n                return { allowed: true };\n            }\n            // Атакующий может пасовать (бито), если защита была успешной\n            else if (isAttacker && action === types_1.PlayerAction.PASS && lastPairDefended) {\n                return { allowed: true };\n            }\n            // Защитник может защищаться или взять карты\n            else if (isDefender &&\n                (action === types_1.PlayerAction.DEFEND || action === types_1.PlayerAction.TAKE)) {\n                // Дополнительная проверка для TAKE: можно брать только если есть что брать\n                if (action === types_1.PlayerAction.TAKE && tableIsEmpty) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot TAKE, no cards on the table.`,\n                    };\n                }\n                // Дополнительная проверка для DEFEND: можно защищаться только если есть атакующая карта\n                if (action === types_1.PlayerAction.DEFEND &&\n                    (!lastPair || lastPair.length !== 1)) {\n                    return {\n                        allowed: false,\n                        error: `Error: Cannot DEFEND, no attacking card found.`,\n                    };\n                }\n                return { allowed: true };\n            }\n            else {\n                return {\n                    allowed: false,\n                    error: `Error: Player ${this.state.players[playerIndex].id} (current: ${this.state.currentPlayerIndex}) cannot perform action ${action} at this stage.`,\n                };\n            }\n        }\n        // 2. Подкидывание (не защитник, после успешной защиты, ход у защитника)\n        else if (action === types_1.PlayerAction.ATTACK &&\n            !isDefender &&\n            lastPairDefended &&\n            this.state.currentPlayerIndex === this.state.defenderIndex) {\n            // Дополнительная проверка: количество карт на столе не должно превышать лимит атаки\n            // И не больше, чем карт у защитника на руках в начале раунда атаки (если стол пуст)\n            const defender = this.state.players[this.state.defenderIndex];\n            const currentAttackLimit = this.state.tableCards.length === 0\n                ? Math.min(this.rules.attackLimit, defender.hand.length)\n                : this.rules.attackLimit;\n            if (this.state.tableCards.flat().length / 2 >= currentAttackLimit) { // Считаем пары карт (атака+защита) или одиночные карты атаки\n                return {\n                    allowed: false,\n                    error: `Error: Cannot podkidnut, attack limit (${currentAttackLimit}) reached.`,\n                };\n            }\n            return { allowed: true }; // Разрешаем подкидывание\n        }\n        // 3. Подкидывание карт (любым игроком, кроме защитника, после успешной защиты)\n        else if (action === types_1.PlayerAction.ATTACK && !isDefender && lastPairDefended) {\n            // Проверяем, есть ли у игрока карты, которые можно подкинуть\n            const player = this.state.players[playerIndex];\n            const validPodkidnoyCards = player.hand.filter(card => this.state.tableCards.flat().some(tableCard => tableCard.rank === card.rank));\n            if (validPodkidnoyCards.length > 0) {\n                // TODO: Логика выбора карты для подкидывания (если их несколько)\n                // Пока просто проверяем возможность\n                // const currentPodkidnoy = validPodkidnoyCards[0]; // Пример\n                return { allowed: true };\n            }\n            else {\n                return { allowed: false, error: `Error: Player ${player.id} has no valid cards to podkidnut.` };\n            }\n        }\n        // 4. Невалидное действие\n        else {\n            return { allowed: false, error: `Error: Action ${action} is not allowed for player ${playerIndex} in the current state.` };\n        }\n    }\n    /**\n     * Выполнение хода игрока\n     */\n    makeMove(playerId, action, cardIndex) {\n        // Проверка, что игра в процессе\n        if (this.state.gameStatus !== types_1.GameStatus.IN_PROGRESS) {\n            console.error(\"Error: Game is not in progress.\");\n            return false;\n        }\n        const playerIndex = this.state.players.findIndex(p => p.id === playerId);\n        if (playerIndex === -1) {\n            console.error(`Error: Player with ID ${playerId} not found.`);\n            return false;\n        }\n        // Проверка, разрешено ли действие\n        const { allowed, error } = this._isActionAllowed(playerIndex, action);\n        if (!allowed) {\n            console.error(error || `Error: Action ${action} is not allowed for player ${playerId} right now.`);\n            return false;\n        }\n        // Логика хода в зависимости от действия\n        let success = false;\n        switch (action) {\n            case types_1.PlayerAction.ATTACK:\n                success = this.handleAttack(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.DEFEND:\n                success = this.handleDefend(playerIndex, cardIndex);\n                break;\n            case types_1.PlayerAction.TAKE:\n                success = this.handleTake(playerIndex);\n                break;\n            case types_1.PlayerAction.PASS:\n                success = this.handlePass(playerIndex);\n                break;\n            default:\n                console.error(`Error: Unknown PlayerAction: ${action}`);\n                return false;\n        }\n        // Если ход был успешным, проверяем конец игры и обновляем активного игрока (если нужно)\n        if (success) {\n            // Отправляем событие о ходе игрока\n            this.emitEvent({\n                type: types_1.GameEvent.PLAYER_MOVED,\n                gameState: this.getState(),\n                playerId,\n                action,\n                cardIndex,\n            });\n            if (!this.checkGameEnd()) {\n                // Логика перехода хода теперь полностью внутри handleTake и handlePass\n                // (через _updateRolesAfterTake и _determineNextRoles соответственно)\n                // Поэтому вызов _moveToNextTurn здесь больше не нужен.\n                // if (action === PlayerAction.TAKE || action === PlayerAction.PASS) {\n                //     // this._moveToNextTurn(); // Удалено\n                // }\n                // this.updateActivePlayer(); // Обновление происходит внутри handle-методов или методов перехода хода\n            }\n        }\n        return success;\n    }\n    /**\n     * Проверка валидности атакующей карты\n     */\n    isValidAttack(card, tableCards, defenderHandSize) {\n        var _a;\n        // Если стол пуст (первый ход атаки), любая карта валидна.\n        if (tableCards.length === 0) {\n            return true;\n        }\n        // Если стол не пуст (подкидывание), ранг карты должен совпадать\n        // с рангом любой карты, уже лежащей на столе (атакующей или защитной).\n        const ranksOnTable = new Set(tableCards.flat().map((c) => c.rank));\n        if (!ranksOnTable.has(card.rank)) {\n            return false;\n        }\n        // Нельзя подкидывать больше карт, чем у защитника на руках (минус уже отбитые в этом раунде)\n        /* const cardsToDefendCount = tableCards.filter(\n          (pair) => pair.length === 1,\n        ).length; */ // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const maxPodkidnoy = defenderHandSize - cardsToDefendCount; // УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // const currentPodkidnoy = tableCards.length - cardsToDefendCount; // Сколько уже подкинули сверх первой атаки - УДАЛЕНО, ТАК КАК НЕ ИСПОЛЬЮТСЯ\n        // Проверяем общее количество карт на столе против лимита\n        const maxCardsOnTable = (_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6; // Используем правило или 6 по умолчанию\n        if (tableCards.length >= Math.min(maxCardsOnTable, defenderHandSize)) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * Обработка атаки\n     */\n    handleAttack(playerIndex, cardIndex) {\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for ATTACK action.`);\n            return false;\n        }\n        // Атаковать (или подкидывать) может любой игрок, кроме защищающегося\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex === this.state.defenderIndex) {\n        //   console.error(\"Error: The defender cannot attack.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const card = player.hand[cardIndex];\n        const defender = this.state.players[this.state.defenderIndex];\n        // Проверка валидности карты для атаки/подкидывания\n        if (!this.isValidAttack(card, this.state.tableCards, defender.hand.length)) {\n            console.error(`Error: Card ${card.rank} ${card.suit} is not a valid attack/podkidnoy card.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол\n        player.hand.splice(cardIndex, 1);\n        this.state.tableCards.push([card]);\n        // Сбрасываем флаг взятия карт, так как началась новая атака\n        this.state.defenderTookCards = false;\n        // Передаем ход защитнику\n        this.state.currentPlayerIndex = this.state.defenderIndex;\n        this.updateActivePlayer();\n        console.log(`Player ${player.id} attacks with ${card.rank} ${card.suit}. Turn passes to defender ${defender.id}.`);\n        return true;\n    }\n    /**\n     * Проверка валидности защищающейся карты\n     */\n    isValidDefense(attackCard, defendCard, trumpSuit) {\n        // Карта той же масти, но старше\n        if (attackCard.suit === defendCard.suit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        // Карта - козырь, а атакующая карта - нет\n        if (defendCard.suit === trumpSuit && attackCard.suit !== trumpSuit) {\n            return true;\n        }\n        // Обе карты козырные, защищающаяся карта старше\n        if (attackCard.suit === trumpSuit &&\n            defendCard.suit === trumpSuit &&\n            this.getRankValue(defendCard.rank) > this.getRankValue(attackCard.rank)) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Обработка защиты\n     */\n    handleDefend(playerIndex, cardIndex) {\n        var _a;\n        // Проверка наличия cardIndex\n        if (typeof cardIndex !== \"number\" || cardIndex < 0) {\n            console.error(`Error: Valid cardIndex is required for DEFEND action.`);\n            return false;\n        }\n        // Защищаться может только защищающийся игрок\n        // Эта проверка уже сделана в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can defend.\");\n        //   return false;\n        // }\n        const player = this.state.players[playerIndex];\n        if (cardIndex >= player.hand.length) {\n            console.error(`Error: Invalid card index ${cardIndex} for player ${player.id}.`);\n            return false;\n        }\n        const defendingCard = player.hand[cardIndex];\n        // Находим последнюю атакующую карту, которую нужно отбить\n        const lastPair = this.state.tableCards.at(-1);\n        if (!lastPair || lastPair.length !== 1) {\n            console.error(\"Error: No attacking card to defend against.\");\n            return false;\n        }\n        const attackingCard = lastPair[0];\n        // Проверяем, может ли выбранная карта отбить атакующую\n        if (!this.isValidDefense(attackingCard, defendingCard, this.state.trumpSuit)) {\n            console.error(`Error: Card ${defendingCard.rank} ${defendingCard.suit} cannot defend against ${attackingCard.rank} ${attackingCard.suit}.`);\n            return false;\n        }\n        // Перемещаем карту из руки на стол к атакующей карте\n        player.hand.splice(cardIndex, 1);\n        lastPair.push(defendingCard);\n        // Проверяем, все ли карты на столе отбиты\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        const defenderHasCards = player.hand.length > 0;\n        const canPodkidnut = this.state.tableCards.length < ((_a = this.rules.maxTableCards) !== null && _a !== void 0 ? _a : 6);\n        // Если все отбито и у защитника нет карт ИЛИ нельзя больше подкидывать, ход атакующего (сказать пас/бито)\n        if (allDefended && (!defenderHasCards || !canPodkidnut)) {\n            this.state.currentPlayerIndex = this.state.attackerIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. All cards defended. Turn passes to attacker ${this.state.players[this.state.attackerIndex].id} to pass.`);\n        }\n        // Если все отбито, но можно подкидывать и у защитника есть карты, ход остается у защитника (ожидание подкидывания или паса)\n        else if (allDefended) {\n            this.state.currentPlayerIndex = this.state.defenderIndex; // Остается у защитника, но он ждет\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Waiting for podkidnoy or pass from attacker(s).`);\n        }\n        // Если не все отбито (это не должно произойти здесь, т.к. мы только что добавили карту)\n        // Оставляем ход у защитника для следующей защиты\n        else {\n            this.state.currentPlayerIndex = this.state.defenderIndex;\n            console.log(`Defender ${player.id} defended with ${defendingCard.rank} ${defendingCard.suit}. Turn remains with defender.`);\n        }\n        this.updateActivePlayer();\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Защитник берет карты со стола\n     */\n    _defenderTakesCards(playerIndex) {\n        const player = this.state.players[playerIndex];\n        const cardsToTake = this.state.tableCards.flat();\n        player.hand.push(...cardsToTake);\n        this.state.tableCards = [];\n        this.state.defenderTookCards = true; // Устанавливаем флаг, что защитник взял карты\n        console.log(`Player ${player.id} takes ${cardsToTake.length} cards from the table.`);\n    }\n    /**\n     * Вспомогательный метод: Обновляет роли после того, как защитник взял карты.\n     * Ход переходит к следующему игроку после взявшего.\n     * @returns {boolean} Возвращает true, если игра окончена, иначе false.\n     */\n    _updateRolesAfterTake() {\n        const numPlayers = this.state.players.length;\n        const playerWhoTookIndex = this.state.defenderIndex; // Индекс игрока, который только что взял карты\n        // Определяем следующего атакующего, пропуская выбывших\n        let nextAttackerIndex = (playerWhoTookIndex + 1) % numPlayers;\n        let loopCheck = 0;\n        while (this.state.players[nextAttackerIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextAttackerIndex === playerWhoTookIndex) {\n                // Обошли круг и вернулись к тому, кто взял - он единственный оставшийся\n                console.log(\"Game ended: Only the player who took cards remains.\");\n                return this.checkGameEnd();\n            }\n            nextAttackerIndex = (nextAttackerIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextAttackerIndex совпадает с playerWhoTookIndex, игра окончена\n        if (nextAttackerIndex === playerWhoTookIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out after take.\");\n            return this.checkGameEnd();\n        }\n        // Определяем следующего защитника, пропуская выбывших\n        let nextDefenderIndex = (nextAttackerIndex + 1) % numPlayers;\n        loopCheck = 0;\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers) {\n            if (nextDefenderIndex === nextAttackerIndex) {\n                // Обошли круг и вернулись к атакующему - он единственный оставшийся\n                console.log(\"Game ended: Only the next attacker remains.\");\n                return this.checkGameEnd();\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с nextAttackerIndex, игра окончена\n        if (nextDefenderIndex === nextAttackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: Only attacker and defender remain, but defender cannot defend.\");\n            return this.checkGameEnd();\n        }\n        this.state.attackerIndex = nextAttackerIndex;\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        console.log(`Roles updated after take: Attacker=${this.state.players[this.state.attackerIndex].id}, Defender=${this.state.players[this.state.defenderIndex].id}`);\n        this.updateActivePlayer();\n        return false; // Игра не закончена этим действием\n    }\n    /**\n     * Обработка взятия карт (защитник берет)\n     */\n    handleTake(playerIndex) {\n        // Проверки перенесены в _isActionAllowed\n        // if (playerIndex !== this.state.defenderIndex) {\n        //   console.error(\"Error: Only the defender can take cards.\");\n        //   return false;\n        // }\n        // Проверяем, есть ли карты на столе для взятия\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: No cards on the table to take.\");\n            return false;\n        }\n        // Защитник берет карты\n        this._defenderTakesCards(playerIndex);\n        // Пополняем руки (начиная с атакующего, затем защитник, потом остальные)\n        // Порядок: атакующий -> ... -> защитник\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true;\n        }\n        // Обновляем роли атакующего и защитника\n        const gameEnded = this._updateRolesAfterTake();\n        if (gameEnded) {\n            return true;\n        }\n        // Сбрасываем флаг ПОСЛЕ обновления ролей и перехода хода\n        this.state.defenderTookCards = false;\n        return true;\n    }\n    /**\n     * Вспомогательный метод: Перемещает карты со стола в отбой\n     */\n    _clearTableToDiscardPile() {\n        this.state.discardPile.push(...this.state.tableCards.flat());\n        this.state.tableCards = [];\n    }\n    /**\n     * Вспомогательный метод: Определяет следующего атакующего и защитника\n     * @returns {boolean} Возвращает true, если удалось определить роли (игра продолжается), иначе false (игра окончена).\n     */\n    _determineNextRoles() {\n        const numPlayers = this.state.players.length;\n        const previousDefender = this.state.defenderIndex;\n        // Новый атакующий - это предыдущий защитник\n        this.state.attackerIndex = previousDefender;\n        // Определяем нового защитника, пропуская выбывших игроков\n        let nextDefenderIndex = (this.state.attackerIndex + 1) % numPlayers;\n        let loopCheck = 0; // Предотвращение бесконечного цикла, если что-то пойдет не так\n        while (this.state.players[nextDefenderIndex].hand.length === 0 &&\n            this.state.deck.length === 0 &&\n            loopCheck < numPlayers // Проверяем не больше, чем количество игроков\n        ) {\n            if (nextDefenderIndex === this.state.attackerIndex) {\n                // Если обошли круг и вернулись к атакующему, значит, все остальные выбыли\n                console.log(\"Game potentially ended: Only attacker remains with cards or deck is empty.\");\n                return false; // Сигнализируем, что роли определить не удалось (игра окончена)\n            }\n            nextDefenderIndex = (nextDefenderIndex + 1) % numPlayers;\n            loopCheck++;\n        }\n        // Если после цикла nextDefenderIndex совпадает с attackerIndex, игра окончена\n        if (nextDefenderIndex === this.state.attackerIndex &&\n            loopCheck >= numPlayers - 1) {\n            console.log(\"Game ended: All other players are out.\");\n            return false;\n        }\n        this.state.defenderIndex = nextDefenderIndex;\n        this.state.currentPlayerIndex = this.state.attackerIndex; // Ход переходит к новому атакующему\n        return true; // Роли успешно определены\n    }\n    /**\n     * Обработка паса (бито) - атакующий завершает раунд после успешной защиты\n     */\n    handlePass(playerIndex) {\n        // Проверяем, что это атакующий игрок\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can pass (finish the round).\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе и все ли они отбиты\n        if (this.state.tableCards.length === 0) {\n            console.error(\"Error: Cannot pass, no cards on the table.\");\n            return false;\n        }\n        const allDefended = this.state.tableCards.every((pair) => pair.length === 2);\n        if (!allDefended) {\n            console.error(\"Error: Cannot pass, not all cards are defended.\");\n            return false;\n        }\n        // Перемещаем карты со стола в отбой\n        this._clearTableToDiscardPile();\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Сбрасываем флаг перед проверкой конца игры и определением ролей\n        this.state.defenderTookCards = false;\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Обработка завершения хода (атакующий говорит \"бито\" или \"пас\")\n     */\n    handleDone(playerIndex) {\n        // Проверяем, что это действительно атакующий игрок завершает ход\n        if (playerIndex !== this.state.attackerIndex) {\n            console.error(\"Error: Only the attacker can finish the turn with 'Done'.\");\n            return false;\n        }\n        // Проверяем, есть ли карты на столе (был ли хотя бы один ход атаки)\n        if (this.state.tableCards.length > 0) {\n            // Перемещаем карты со стола в отбой\n            this._clearTableToDiscardPile();\n        }\n        else {\n            // Если стол пуст, значит атакующий спасовал сразу\n            console.log(`Player ${this.state.players[playerIndex].id} passed the turn immediately.`);\n            // Ничего не делаем с картами, просто передаем ход\n        }\n        // Пополняем руки игроков\n        this.replenishHands();\n        // Проверяем окончание игры после пополнения рук\n        if (this.checkGameEnd()) {\n            return true; // Игра завершена\n        }\n        // Определяем следующие роли и передаем ход\n        // Логика такая же, как при 'Pass', т.к. защитник успешно отбился (или атаки не было)\n        if (!this._determineNextRoles()) {\n            // Если определить роли не удалось (например, остался 1 игрок), проверяем конец игры еще раз\n            return this.checkGameEnd();\n        }\n        // Обновляем статус активного игрока\n        this.updateActivePlayer();\n        console.log(`Round finished (Done/Pass). New attacker: ${this.state.players[this.state.attackerIndex].id}, New defender: ${this.state.players[this.state.defenderIndex].id}`);\n        return true;\n    }\n    /**\n     * Пополнение рук игроков из колоды до нужного количества\n     */\n    replenishHands() {\n        const cardsNeeded = this.rules.initialHandSize;\n        const numPlayers = this.state.players.length;\n        let currentPlayerToCheck = this.state.attackerIndex; // Начинаем с атакующего\n        for (let i = 0; i < numPlayers; i++) {\n            const player = this.state.players[currentPlayerToCheck];\n            // Пополняем руку, только если игрок еще в игре (есть карты или есть колода)\n            if (player.hand.length > 0 || this.state.deck.length > 0) {\n                while (player.hand.length < cardsNeeded && this.state.deck.length > 0) {\n                    const card = this.state.deck.shift();\n                    if (card) {\n                        player.hand.push(card);\n                    }\n                }\n            }\n            // Переходим к следующему игроку по кругу\n            currentPlayerToCheck = (currentPlayerToCheck + 1) % numPlayers;\n        }\n        // Если колода закончилась и козырь был под ней, добавляем его в state\n        if (this.state.deck.length === 0 &&\n            this.state.trumpCard &&\n            !this.state.players.some((p) => p.hand.includes(this.state.trumpCard))) {\n            // Козырь забирает игрок, который последним пополнил руку (если ему нужно)\n            // В нашей логике пополнения это будет игрок перед атакующим, если круг полный\n            // Но проще отдать его текущему атакующему, если у него меньше 6 карт.\n            // Или просто оставить его видимым, но не в игре? Правила разнятся.\n            // Пока оставим его видимым в state.trumpCard, но не в руках.\n            // Убираем TODO, т.к. конкретная реализация зависит от выбранных правил.\n            // this.state.trumpCard = undefined;\n        }\n    }\n    /**\n     * Проверка окончания игры\n     */\n    checkGameEnd() {\n        // Проверка условий окончания игры\n        const playersWithCards = this.state.players.filter((p) => p.hand.length > 0);\n        const playersWithoutCards = this.state.players.filter((p) => p.hand.length === 0);\n        // Игра заканчивается, если колода пуста и не более одного игрока с картами\n        if (this.state.deck.length === 0 && playersWithCards.length <= 1) {\n            this.state.gameStatus = types_1.GameStatus.FINISHED;\n            let message = \"\";\n            if (playersWithCards.length === 1) {\n                // Проигравший - тот, у кого остались карты\n                this.state.loser = playersWithCards[0];\n                console.log(`Game finished. Loser: ${this.state.loser.id}`);\n                // Победитель - первый игрок, который избавился от всех карт\n                // В классическом дураке первый вышедший считается победителем\n                this.state.winner = playersWithoutCards.length > 0 ? playersWithoutCards[0] : undefined;\n                if (this.state.winner) {\n                    console.log(`Winner: ${this.state.winner.id}`);\n                    message = `Game finished. Winner: ${this.state.winner.id}, Loser: ${this.state.loser.id}`;\n                }\n                else {\n                    message = `Game finished. Loser: ${this.state.loser.id}`;\n                }\n            }\n            else {\n                // Ничья (все сбросили карты одновременно) - очень редкий случай\n                console.log(\"Game finished. Draw!\");\n                this.state.winner = undefined;\n                this.state.loser = undefined;\n                message = \"Game finished. Draw!\";\n            }\n            // Отправляем событие окончания игры\n            this.emitEvent({\n                type: types_1.GameEvent.GAME_ENDED,\n                gameState: this.getState(),\n                message,\n            });\n            // Обнуляем активного игрока, т.к. игра завершена\n            this.state.currentPlayerIndex = -1;\n            this.updateActivePlayer();\n            return true;\n        }\n        return false;\n    }\n}\nexports.DurakGame = DurakGame;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/durak/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/index.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n/**\n * Игровое ядро \"Козырь Мастер\"\n *\n * Этот файл экспортирует основные классы и интерфейсы игрового ядра\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BotDifficulty = exports.BotFactory = exports.DurakBot = void 0;\n// Экспорт общих типов\n__exportStar(__webpack_require__(/*! ./types */ \"../../packages/core/dist/types.js\"), exports);\n// Экспорт основной логики игры Дурак\n__exportStar(__webpack_require__(/*! ./durak */ \"../../packages/core/dist/durak/index.js\"), exports);\n// Экспорт системы ботов\nvar bot_1 = __webpack_require__(/*! ./durak/bot */ \"../../packages/core/dist/durak/bot.js\");\nObject.defineProperty(exports, \"DurakBot\", ({ enumerable: true, get: function () { return bot_1.DurakBot; } }));\nObject.defineProperty(exports, \"BotFactory\", ({ enumerable: true, get: function () { return bot_1.BotFactory; } }));\nObject.defineProperty(exports, \"BotDifficulty\", ({ enumerable: true, get: function () { return bot_1.BotDifficulty; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29yZS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQ7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUIsR0FBRyxrQkFBa0IsR0FBRyxnQkFBZ0I7QUFDN0Q7QUFDQSxhQUFhLG1CQUFPLENBQUMsa0RBQVM7QUFDOUI7QUFDQSxhQUFhLG1CQUFPLENBQUMsd0RBQVM7QUFDOUI7QUFDQSxZQUFZLG1CQUFPLENBQUMsMERBQWE7QUFDakMsNENBQTJDLEVBQUUscUNBQXFDLDBCQUEwQixFQUFDO0FBQzdHLDhDQUE2QyxFQUFFLHFDQUFxQyw0QkFBNEIsRUFBQztBQUNqSCxpREFBZ0QsRUFBRSxxQ0FBcUMsK0JBQStCLEVBQUM7QUFDdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb3p5ci1tYXN0ZXItd2ViLy4uLy4uL3BhY2thZ2VzL2NvcmUvZGlzdC9pbmRleC5qcz9kNjViIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLyoqXG4gKiDQmNCz0YDQvtCy0L7QtSDRj9C00YDQviBcItCa0L7Qt9GL0YDRjCDQnNCw0YHRgtC10YBcIlxuICpcbiAqINCt0YLQvtGCINGE0LDQudC7INGN0LrRgdC/0L7RgNGC0LjRgNGD0LXRgiDQvtGB0L3QvtCy0L3Ri9C1INC60LvQsNGB0YHRiyDQuCDQuNC90YLQtdGA0YTQtdC50YHRiyDQuNCz0YDQvtCy0L7Qs9C+INGP0LTRgNCwXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Cb3REaWZmaWN1bHR5ID0gZXhwb3J0cy5Cb3RGYWN0b3J5ID0gZXhwb3J0cy5EdXJha0JvdCA9IHZvaWQgMDtcbi8vINCt0LrRgdC/0L7RgNGCINC+0LHRidC40YUg0YLQuNC/0L7QslxuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3R5cGVzXCIpLCBleHBvcnRzKTtcbi8vINCt0LrRgdC/0L7RgNGCINC+0YHQvdC+0LLQvdC+0Lkg0LvQvtCz0LjQutC4INC40LPRgNGLINCU0YPRgNCw0Lpcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9kdXJha1wiKSwgZXhwb3J0cyk7XG4vLyDQrdC60YHQv9C+0YDRgiDRgdC40YHRgtC10LzRiyDQsdC+0YLQvtCyXG52YXIgYm90XzEgPSByZXF1aXJlKFwiLi9kdXJhay9ib3RcIik7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJEdXJha0JvdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gYm90XzEuRHVyYWtCb3Q7IH0gfSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJCb3RGYWN0b3J5XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBib3RfMS5Cb3RGYWN0b3J5OyB9IH0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQm90RGlmZmljdWx0eVwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gYm90XzEuQm90RGlmZmljdWx0eTsgfSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/core/dist/index.js\n");

/***/ }),

/***/ "../../packages/core/dist/types.js":
/*!*****************************************!*\
  !*** ../../packages/core/dist/types.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n/**\n * Общие типы и интерфейсы для игрового ядра \"Козырь Мастер\"\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GameEvent = exports.PlayerAction = exports.GameStatus = exports.DurakVariant = exports.CardRank = exports.CardSuit = void 0;\n// Типы карт\nvar CardSuit;\n(function (CardSuit) {\n    CardSuit[\"HEARTS\"] = \"hearts\";\n    CardSuit[\"DIAMONDS\"] = \"diamonds\";\n    CardSuit[\"CLUBS\"] = \"clubs\";\n    CardSuit[\"SPADES\"] = \"spades\";\n})(CardSuit || (exports.CardSuit = CardSuit = {}));\nvar CardRank;\n(function (CardRank) {\n    CardRank[\"SIX\"] = \"6\";\n    CardRank[\"SEVEN\"] = \"7\";\n    CardRank[\"EIGHT\"] = \"8\";\n    CardRank[\"NINE\"] = \"9\";\n    CardRank[\"TEN\"] = \"10\";\n    CardRank[\"JACK\"] = \"jack\";\n    CardRank[\"QUEEN\"] = \"queen\";\n    CardRank[\"KING\"] = \"king\";\n    CardRank[\"ACE\"] = \"ace\";\n})(CardRank || (exports.CardRank = CardRank = {}));\n// Варианты игры Дурак\nvar DurakVariant;\n(function (DurakVariant) {\n    DurakVariant[\"CLASSIC\"] = \"classic\";\n    DurakVariant[\"THROWING\"] = \"throwing\";\n    DurakVariant[\"TRANSFERABLE\"] = \"transferable\";\n    DurakVariant[\"TEAM\"] = \"team\";\n})(DurakVariant || (exports.DurakVariant = DurakVariant = {}));\n// Статус игры\nvar GameStatus;\n(function (GameStatus) {\n    GameStatus[\"NOT_STARTED\"] = \"not_started\";\n    GameStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    GameStatus[\"FINISHED\"] = \"finished\";\n})(GameStatus || (exports.GameStatus = GameStatus = {}));\n// Действия игрока\nvar PlayerAction;\n(function (PlayerAction) {\n    PlayerAction[\"ATTACK\"] = \"attack\";\n    PlayerAction[\"DEFEND\"] = \"defend\";\n    PlayerAction[\"TAKE\"] = \"take\";\n    PlayerAction[\"PASS\"] = \"pass\";\n})(PlayerAction || (exports.PlayerAction = PlayerAction = {}));\n// События игры\nvar GameEvent;\n(function (GameEvent) {\n    GameEvent[\"GAME_STARTED\"] = \"game_started\";\n    GameEvent[\"GAME_ENDED\"] = \"game_ended\";\n    GameEvent[\"PLAYER_MOVED\"] = \"player_moved\";\n    GameEvent[\"TURN_CHANGED\"] = \"turn_changed\";\n    GameEvent[\"CARDS_DEALT\"] = \"cards_dealt\";\n    GameEvent[\"ROUND_ENDED\"] = \"round_ended\";\n})(GameEvent || (exports.GameEvent = GameEvent = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/dist/types.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-components":
/*!************************************!*\
  !*** external "styled-components" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-components");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@reduxjs/toolkit":
/*!***********************************!*\
  !*** external "@reduxjs/toolkit" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@reduxjs/toolkit");;

/***/ }),

/***/ "react-redux":
/*!******************************!*\
  !*** external "react-redux" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-redux");;

/***/ }),

/***/ "socket.io-client":
/*!***********************************!*\
  !*** external "socket.io-client" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("socket.io-client");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmultiplayer&preferredRegion=&absolutePagePath=.%2Fpages%2Fmultiplayer.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();