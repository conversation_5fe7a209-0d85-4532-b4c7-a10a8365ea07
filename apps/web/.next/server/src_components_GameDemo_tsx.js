"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_components_GameDemo_tsx";
exports.ids = ["src_components_GameDemo_tsx"];
exports.modules = {

/***/ "./src/components/GameDemo.tsx":
/*!*************************************!*\
  !*** ./src/components/GameDemo.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"@react-three/fiber\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"@react-three/drei\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! styled-components */ \"styled-components\");\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(styled_components__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// 3D карта компонент\nconst Card3D = ({\n  card,\n  onClick,\n  isHovered\n}) => {\n  const meshRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.useFrame)(state => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = isHovered ? Math.sin(state.clock.elapsedTime * 2) * 0.1 : 0;\n      meshRef.current.position.y = isHovered ? card.position.y + Math.sin(state.clock.elapsedTime * 4) * 0.05 : card.position.y;\n    }\n  });\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Float, {\n    speed: 2,\n    rotationIntensity: 0.5,\n    floatIntensity: 0.3,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"mesh\", {\n      ref: meshRef,\n      position: [card.position.x, card.position.y, card.position.z],\n      onClick: onClick,\n      onPointerOver: e => e.stopPropagation(),\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"boxGeometry\", {\n        args: [0.8, 1.2, 0.05]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"meshStandardMaterial\", {\n        color: card.isRevealed ? 'white' : '#1a4480',\n        metalness: 0.3,\n        roughness: 0.4\n      }), card.isRevealed && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Text3D, {\n        font: \"/fonts/helvetiker_regular.typeface.json\",\n        size: 0.2,\n        height: 0.01,\n        position: [-0.2, 0, 0.026],\n        children: [card.value, \" \", card.suit, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"meshStandardMaterial\", {\n          color: card.color === 'red' ? '#dc2626' : '#1f2937'\n        })]\n      })]\n    })\n  });\n};\n\n// Игровой стол 3D\nconst GameTable3D = () => {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"group\", {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"mesh\", {\n      position: [0, -0.5, 0],\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"cylinderGeometry\", {\n        args: [3, 3, 0.1, 32]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"meshStandardMaterial\", {\n        color: \"#0f4c3a\"\n      })]\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(\"mesh\", {\n      position: [0, -0.44, 0],\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"cylinderGeometry\", {\n        args: [2.9, 2.9, 0.02, 32]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"meshStandardMaterial\", {\n        color: \"#1a5d4a\"\n      })]\n    })]\n  });\n};\nconst GameDemo = ({\n  onStartGame\n}) => {\n  const [cards, setCards] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('waiting');\n  const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    player: 0,\n    ai: 0\n  });\n  const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [aiThinking, setAiThinking] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [gameStats, setGameStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    cardsPlayed: 0,\n    winStreak: 0,\n    totalGames: 0,\n    aiPredictionAccuracy: 0.85\n  });\n\n  // Создание колоды карт\n  const createDeck = () => {\n    const suits = ['♠️', '♥️', '♦️', '♣️'];\n    const values = ['6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];\n    const deck = [];\n    suits.forEach(suit => {\n      values.forEach(value => {\n        deck.push({\n          id: `${suit}-${value}`,\n          suit,\n          value,\n          color: suit === '♥️' || suit === '♦️' ? 'red' : 'black',\n          isRevealed: false,\n          position: {\n            x: 0,\n            y: 0,\n            z: 0\n          }\n        });\n      });\n    });\n    return deck.sort(() => Math.random() - 0.5);\n  };\n\n  // Раздача карт\n  const dealCards = async () => {\n    setGameState('dealing');\n    const deck = createDeck();\n    const playerCards = deck.slice(0, 6);\n    const aiCards = deck.slice(6, 12);\n\n    // Позиционирование карт игрока\n    playerCards.forEach((card, index) => {\n      card.position = {\n        x: (index - 2.5) * 0.9,\n        y: -1.5,\n        z: 0\n      };\n      card.isRevealed = true;\n    });\n\n    // Позиционирование карт ИИ\n    aiCards.forEach((card, index) => {\n      card.position = {\n        x: (index - 2.5) * 0.9,\n        y: 1.5,\n        z: 0\n      };\n    });\n    setCards([...playerCards, ...aiCards]);\n\n    // Анимация раздачи\n    for (let i = 0; i < 12; i++) {\n      await new Promise(resolve => setTimeout(resolve, 200));\n      setCards(prev => {\n        const newCards = [...prev];\n        if (newCards[i]) {\n          newCards[i].isRevealed = i < 6; // Только карты игрока открыты\n        }\n\n        return newCards;\n      });\n    }\n    setGameState('playing');\n  };\n\n  // Ход игрока\n  const playCard = async cardId => {\n    if (gameState !== 'playing' || aiThinking) return;\n    setCards(prev => prev.map(card => card.id === cardId ? {\n      ...card,\n      position: {\n        ...card.position,\n        y: 0,\n        z: 0.5\n      }\n    } : card));\n    setGameStats(prev => ({\n      ...prev,\n      cardsPlayed: prev.cardsPlayed + 1\n    }));\n\n    // ИИ думает\n    setAiThinking(true);\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // ИИ играет карту\n    const aiCards = cards.filter(card => card.position.y > 0 && !card.isRevealed);\n    if (aiCards.length > 0) {\n      const aiCard = aiCards[Math.floor(Math.random() * aiCards.length)];\n      setCards(prev => prev.map(card => card.id === aiCard.id ? {\n        ...card,\n        position: {\n          ...card.position,\n          y: 0,\n          z: -0.5\n        },\n        isRevealed: true\n      } : card));\n    }\n    setAiThinking(false);\n\n    // Определение победителя хода (упрощенная логика)\n    const winner = Math.random() > 0.5 ? 'player' : 'ai';\n    setScore(prev => ({\n      ...prev,\n      [winner]: prev[winner] + 1\n    }));\n\n    // Проверка окончания игры\n    if (score.player + score.ai >= 5) {\n      setGameState('finished');\n      setGameStats(prev => ({\n        ...prev,\n        totalGames: prev.totalGames + 1,\n        winStreak: winner === 'player' ? prev.winStreak + 1 : 0\n      }));\n    }\n  };\n\n  // Новая игра\n  const startNewGame = () => {\n    setCards([]);\n    setGameState('waiting');\n    setScore({\n      player: 0,\n      ai: 0\n    });\n    setHoveredCard(null);\n    setAiThinking(false);\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameDemoContainer, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ContentWrapper, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        initial: {\n          opacity: 0,\n          y: 50\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SectionTitle, {\n          children: \"\\u0418\\u043D\\u0442\\u0435\\u0440\\u0430\\u043A\\u0442\\u0438\\u0432\\u043D\\u0430\\u044F \\u0438\\u0433\\u0440\\u043E\\u0432\\u0430\\u044F \\u0434\\u0435\\u043C\\u043E\\u043D\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F\"\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(SectionSubtitle, {\n          children: \"\\u041F\\u043E\\u043F\\u0440\\u043E\\u0431\\u0443\\u0439\\u0442\\u0435 \\u0440\\u0435\\u0432\\u043E\\u043B\\u044E\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0439 \\u0438\\u0433\\u0440\\u043E\\u0432\\u043E\\u0439 \\u0434\\u0432\\u0438\\u0436\\u043E\\u043A \\u0432 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u0438\"\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameContainer, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameCanvas, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.Canvas, {\n            camera: {\n              position: [0, 2, 8],\n              fov: 60\n            },\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"ambientLight\", {\n              intensity: 0.4\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pointLight\", {\n              position: [5, 5, 5],\n              intensity: 1\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(\"pointLight\", {\n              position: [-5, -5, -5],\n              intensity: 0.5,\n              color: \"#4a90e2\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(GameTable3D, {}), cards.map(card => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(Card3D, {\n              card: card,\n              isHovered: hoveredCard === card.id,\n              onClick: () => {\n                if (card.position.y < 0 && card.isRevealed) {\n                  playCard(card.id);\n                }\n              }\n            }, card.id)), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.OrbitControls, {\n              enableZoom: false,\n              enablePan: false\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameOverlay, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ScoreBoard, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ScoreItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ScoreLabel, {\n                  children: \"\\u0418\\u0433\\u0440\\u043E\\u043A\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ScoreValue, {\n                  children: score.player\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ScoreItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ScoreLabel, {\n                  children: \"\\u0418\\u0418\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ScoreValue, {\n                  children: score.ai\n                })]\n              })]\n            }), aiThinking && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(AIStatus, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              exit: {\n                opacity: 0,\n                scale: 0.8\n              },\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(AIIcon, {\n                children: \"\\uD83E\\uDDE0\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(AIText, {\n                children: \"\\u0418\\u0418 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437\\u0438\\u0440\\u0443\\u0435\\u0442 \\u0445\\u043E\\u0434...\"\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(AIProgress, {})]\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameStatus, {\n              children: [gameState === 'waiting' && 'Готов к игре', gameState === 'dealing' && 'Раздача карт...', gameState === 'playing' && 'Ваш ход', gameState === 'finished' && `Игра окончена! ${score.player > score.ai ? 'Вы победили!' : 'ИИ победил!'}`]\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(ControlPanel, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(GameControls, {\n            children: [gameState === 'waiting' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ControlButton, {\n              primary: true,\n              onClick: dealCards,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\uD83C\\uDFB4 \\u041D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u0433\\u0440\\u0443\"\n            }), gameState === 'finished' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ControlButton, {\n              primary: true,\n              onClick: startNewGame,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\uD83D\\uDD04 \\u041D\\u043E\\u0432\\u0430\\u044F \\u0438\\u0433\\u0440\\u0430\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(ControlButton, {\n              onClick: onStartGame,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\uD83D\\uDE80 \\u041F\\u043E\\u043B\\u043D\\u0430\\u044F \\u0432\\u0435\\u0440\\u0441\\u0438\\u044F\"\n            })]\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatsPanel, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatsTitle, {\n              children: \"\\u0421\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u043A\\u0430 \\u0441\\u0435\\u0441\\u0441\\u0438\\u0438\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatsList, {\n              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatLabel, {\n                  children: \"\\u041A\\u0430\\u0440\\u0442 \\u0441\\u044B\\u0433\\u0440\\u0430\\u043D\\u043E:\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatValue, {\n                  children: gameStats.cardsPlayed\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatLabel, {\n                  children: \"\\u0421\\u0435\\u0440\\u0438\\u044F \\u043F\\u043E\\u0431\\u0435\\u0434:\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatValue, {\n                  children: gameStats.winStreak\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatLabel, {\n                  children: \"\\u0412\\u0441\\u0435\\u0433\\u043E \\u0438\\u0433\\u0440:\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatValue, {\n                  children: gameStats.totalGames\n                })]\n              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatItem, {\n                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(StatLabel, {\n                  children: \"\\u0422\\u043E\\u0447\\u043D\\u043E\\u0441\\u0442\\u044C \\u0418\\u0418:\"\n                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(StatValue, {\n                  children: [(gameStats.aiPredictionAccuracy * 100).toFixed(1), \"%\"]\n                })]\n              })]\n            })]\n          })]\n        })]\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(TechFeatures, {\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureIcon, {\n            children: \"\\u269B\\uFE0F\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureTitle, {\n              children: \"\\u041A\\u0432\\u0430\\u043D\\u0442\\u043E\\u0432\\u0430\\u044F \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u043E\\u0441\\u0442\\u044C\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureDescription, {\n              children: \"\\u0418\\u0441\\u0442\\u0438\\u043D\\u043D\\u043E \\u0441\\u043B\\u0443\\u0447\\u0430\\u0439\\u043D\\u0430\\u044F \\u0440\\u0430\\u0437\\u0434\\u0430\\u0447\\u0430 \\u043A\\u0430\\u0440\\u0442\"\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureIcon, {\n            children: \"\\uD83E\\uDDE0\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureTitle, {\n              children: \"\\u0418\\u0418 \\u043F\\u0440\\u043E\\u0442\\u0438\\u0432\\u043D\\u0438\\u043A\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureDescription, {\n              children: \"\\u0410\\u0434\\u0430\\u043F\\u0442\\u0438\\u0432\\u043D\\u044B\\u0439 \\u0438\\u0441\\u043A\\u0443\\u0441\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u044B\\u0439 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\"\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureIcon, {\n            children: \"\\uD83C\\uDFAE\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureTitle, {\n              children: \"3D \\u0433\\u0440\\u0430\\u0444\\u0438\\u043A\\u0430\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureDescription, {\n              children: \"\\u0418\\u043C\\u043C\\u0435\\u0440\\u0441\\u0438\\u0432\\u043D\\u044B\\u0439 \\u0438\\u0433\\u0440\\u043E\\u0432\\u043E\\u0439 \\u043E\\u043F\\u044B\\u0442\"\n            })]\n          })]\n        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureItem, {\n          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureIcon, {\n            children: \"\\uD83D\\uDCCA\"\n          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)(FeatureText, {\n            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureTitle, {\n              children: \"\\u0420\\u0435\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0430\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430\"\n            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(FeatureDescription, {\n              children: \"\\u0414\\u0435\\u0442\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0441\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u043A\\u0430 \\u0438\\u0433\\u0440\\u044B\"\n            })]\n          })]\n        })]\n      })]\n    })\n  });\n};\n\n// Стилизованные компоненты\nconst GameDemoContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);\n  padding: 4rem 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\nconst ContentWrapper = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  max-width: 1400px;\n  width: 100%;\n`;\nconst SectionTitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().h2)`\n  font-size: 3.5rem;\n  font-weight: 900;\n  text-align: center;\n  margin-bottom: 1rem;\n  background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\nconst SectionSubtitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().p)`\n  font-size: 1.3rem;\n  color: rgba(255, 255, 255, 0.7);\n  text-align: center;\n  margin-bottom: 4rem;\n  max-width: 800px;\n  margin-left: auto;\n  margin-right: auto;\n`;\nconst GameContainer = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 2rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst GameCanvas = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: relative;\n  height: 600px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 20px;\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst GameOverlay = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 10;\n`;\nconst ScoreBoard = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  top: 1rem;\n  left: 1rem;\n  display: flex;\n  gap: 1rem;\n`;\nconst ScoreItem = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem 1rem;\n  text-align: center;\n`;\nconst ScoreLabel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  font-size: 0.8rem;\n  color: rgba(255, 255, 255, 0.7);\n  margin-bottom: 0.25rem;\n`;\nconst ScoreValue = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #4a90e2;\n`;\nconst AIStatus = styled_components__WEBPACK_IMPORTED_MODULE_4___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div)`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(20px);\n  border-radius: 15px;\n  padding: 1.5rem;\n  text-align: center;\n  border: 1px solid rgba(74, 144, 226, 0.3);\n`;\nconst AIIcon = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\nconst AIText = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  color: white;\n  font-weight: 600;\n  margin-bottom: 1rem;\n`;\nconst AIProgress = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  width: 100px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n  overflow: hidden;\n  \n  &::after {\n    content: '';\n    display: block;\n    width: 30%;\n    height: 100%;\n    background: linear-gradient(90deg, #4a90e2, #7b68ee);\n    animation: progress 1.5s infinite;\n  }\n  \n  @keyframes progress {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(300%); }\n  }\n`;\nconst GameStatus = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  position: absolute;\n  bottom: 1rem;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(10px);\n  border-radius: 10px;\n  padding: 0.75rem 1.5rem;\n  color: white;\n  font-weight: 600;\n`;\nconst ControlPanel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n`;\nconst GameControls = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\nconst ControlButton = styled_components__WEBPACK_IMPORTED_MODULE_4___default()(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button)`\n  background: ${props => props.primary ? 'linear-gradient(135deg, #4a90e2, #7b68ee)' : 'rgba(255, 255, 255, 0.1)'};\n  color: white;\n  border: ${props => props.primary ? 'none' : '1px solid rgba(255, 255, 255, 0.2)'};\n  border-radius: 10px;\n  padding: 1rem 1.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.primary ? 'linear-gradient(135deg, #5ba0f2, #8b78fe)' : 'rgba(255, 255, 255, 0.2)'};\n  }\n`;\nconst StatsPanel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst StatsTitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().h4)`\n  color: white;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n`;\nconst StatsList = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n`;\nconst StatItem = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\nconst StatLabel = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().span)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.9rem;\n`;\nconst StatValue = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().span)`\n  color: #4a90e2;\n  font-weight: 600;\n`;\nconst TechFeatures = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 1.5rem;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  @media (max-width: 480px) {\n    grid-template-columns: 1fr;\n  }\n`;\nconst FeatureItem = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n  padding: 1.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\nconst FeatureIcon = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  font-size: 2rem;\n`;\nconst FeatureText = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)``;\nconst FeatureTitle = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  color: white;\n  font-weight: 600;\n  margin-bottom: 0.25rem;\n`;\nconst FeatureDescription = (styled_components__WEBPACK_IMPORTED_MODULE_4___default().div)`\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 0.8rem;\n`;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GameDemo);\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/GameDemo.tsx\n");

/***/ })

};
;