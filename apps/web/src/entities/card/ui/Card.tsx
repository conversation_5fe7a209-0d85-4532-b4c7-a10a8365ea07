import { FC, useEffect, useRef, useState } from "react";
import styled, { keyframes, css } from "styled-components";
import {
  CardSuit,
  CardAnimationType,
  CardAnimationSpeed,
  CardTheme,
  CardEffect,
} from "../model/types";
import { applyCardTheme } from "../themes";

export interface CardProps {
  suit: CardSuit;
  rank: string | number;
  isSelected?: boolean;
  isPlayable?: boolean;
  onClick?: () => void;
  highContrast?: boolean;
  animationSpeed?: CardAnimationSpeed;
  isFaceDown?: boolean;
  animationType?: CardAnimationType;
  enableSound?: boolean;
  theme?: CardTheme;
  effect?: CardEffect;
  colorTheme?: "light" | "dark" | "colorful" | "monochrome";
  "data-testid"?: string;
}

export const Card: FC<CardProps> = ({
  suit,
  rank,
  isSelected = false,
  isPlayable = true,
  onClick,
  highContrast = false,
  animationSpeed = "normal",
  isFaceDown = false,
  animationType = "simple",
  enableSound = false,
  theme = "classic",
  effect = "none",
  colorTheme = "light",
  "data-testid": dataTestId,
}) => {
  // Определение цвета масти
  const isRed = suit === "hearts" || suit === "diamonds";

  // Состояние для эффектов частиц
  const [showParticles, setShowParticles] = useState(false);
  const [particles, setParticles] = useState<
    Array<{ id: number; x: number; y: number }>
  >([]);

  // Преобразование ранга для отображения
  const displayRank = (): string => {
    if (rank === 1) return "A";
    if (rank === 11) return "J";
    if (rank === 12) return "Q";
    if (rank === 13) return "K";
    return String(rank);
  };

  // Получение символа масти
  const getSuitSymbol = (): string => {
    switch (suit) {
      case "hearts":
        return "♥";
      case "diamonds":
        return "♦";
      case "clubs":
        return "♣";
      case "spades":
        return "♠";
      default:
        return "";
    }
  };

  // Ссылка на аудио элемент для звуковых эффектов
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Эффект для создания аудио элемента
  useEffect(() => {
    if (enableSound) {
      audioRef.current = new Audio();
      audioRef.current.volume = 0.5;

      return () => {
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current = null;
        }
      };
    }
  }, [enableSound]);

  // Функция для воспроизведения звука
  const playSound = (type: "select" | "click") => {
    if (enableSound && audioRef.current) {
      // Здесь можно добавить разные звуки для разных действий
      audioRef.current.src =
        type === "select"
          ? "/sounds/card-select.mp3"
          : "/sounds/card-click.mp3";
      audioRef.current
        .play()
        .catch((e) => console.error("Ошибка воспроизведения звука:", e));
    }
  };

  // Обработчик клика с добавлением звука
  const handleClick = () => {
    if (isPlayable && onClick) {
      playSound("click");
      onClick();

      // Активация эффекта частиц при клике
      if (animationType === "particle") {
        createParticles();
      }
    }
  };

  // Обработчик нажатия клавиши с добавлением звука
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isPlayable && (e.key === "Enter" || e.key === " ")) {
      e.preventDefault();
      playSound("click");
      onClick && onClick();

      // Активация эффекта частиц при нажатии клавиши
      if (animationType === "particle") {
        createParticles();
      }
    }
  };

  // Создание эффекта частиц
  const createParticles = () => {
    const newParticles = [];
    for (let i = 0; i < 20; i++) {
      newParticles.push({
        id: Math.random(),
        x: (Math.random() - 0.5) * 100,
        y: (Math.random() - 0.5) * 100,
      });
    }
    setParticles(newParticles);
    setShowParticles(true);

    // Удаление частиц через 1 секунду
    setTimeout(() => {
      setShowParticles(false);
      setParticles([]);
    }, 1000);
  };

  // Применение темы к карте с использованием новой системы тем
  const getThemeStyles = () => {
    return applyCardTheme(theme, colorTheme as any, effect, isRed);
  };

  // Эффект для воспроизведения звука при выборе карты
  useEffect(() => {
    if (isSelected && enableSound) {
      playSound("select");
    }
  }, [isSelected]);

  return (
    <CardWrapper>
      <CardContainer
        isSelected={isSelected}
        isPlayable={isPlayable}
        isRed={isRed}
        animationSpeed={animationSpeed}
        animationType={animationType}
        isFaceDown={isFaceDown}
        theme={theme}
        effect={effect}
        colorTheme={colorTheme}
        highContrast={highContrast}
        onClick={isPlayable ? handleClick : undefined}
        role="button"
        aria-disabled={!isPlayable}
        aria-pressed={isSelected}
        aria-label={
          isFaceDown ? "Карта рубашкой вверх" : `${displayRank()} ${suit}`
        }
        tabIndex={isPlayable ? 0 : -1}
        onKeyDown={handleKeyDown}
        data-testid={dataTestId}
        className={suit}
      >
        {isFaceDown ? (
          <CardBack theme={theme}>
            <CardBackPattern theme={theme} />
          </CardBack>
        ) : (
          <>
            <CardCorner position="top">
              <Rank highContrast={highContrast} colorTheme={colorTheme}>
                {displayRank()}
              </Rank>
              <Suit
                isRed={isRed}
                highContrast={highContrast}
                colorTheme={colorTheme}
              >
                {getSuitSymbol()}
              </Suit>
            </CardCorner>

            <CardCenter>
              <CenterSuit
                isRed={isRed}
                highContrast={highContrast}
                colorTheme={colorTheme}
                theme={theme}
              >
                {getSuitSymbol()}
              </CenterSuit>
            </CardCenter>

            <CardCorner position="bottom">
              <Rank highContrast={highContrast} colorTheme={colorTheme}>
                {displayRank()}
              </Rank>
              <Suit
                isRed={isRed}
                highContrast={highContrast}
                colorTheme={colorTheme}
              >
                {getSuitSymbol()}
              </Suit>
            </CardCorner>
          </>
        )}
      </CardContainer>

      {/* Эффект частиц */}
      {showParticles && (
        <ParticlesContainer>
          {particles.map((particle) => (
            <Particle
              key={particle.id}
              style={
                {
                  "--x": `${particle.x}px`,
                  "--y": `${particle.y}px`,
                  backgroundColor: isRed ? "#ff6b6b" : "#4d94ff",
                } as React.CSSProperties
              }
            />
          ))}
        </ParticlesContainer>
      )}
    </CardWrapper>
  );
};

// Анимации
const pulseAnimation = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const flipAnimation = keyframes`
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(180deg); }
`;

const rotateAnimation = keyframes`
  0% { transform: rotate3d(1, 1, 0, 0deg); }
  50% { transform: rotate3d(1, 1, 0, 180deg); }
  100% { transform: rotate3d(1, 1, 0, 360deg); }
`;

const glowAnimation = keyframes`
  0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5), 0 0 10px rgba(255, 255, 255, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.5); }
  100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5), 0 0 10px rgba(255, 255, 255, 0.3); }
`;

const cascadeAnimation = keyframes`
  0% { opacity: 0; transform: translateY(-20px); }
  100% { opacity: 1; transform: translateY(0); }
`;

const particleAnimation = keyframes`
  0% { transform: translate(0, 0); opacity: 1; }
  100% { transform: translate(var(--x), var(--y)); opacity: 0; }
`;

const holographicEffect = keyframes`
  0% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
  100% { background-position: 0% 0%; }
`;

// Функция для определения анимации
const getAnimation = (type: CardAnimationType, speed: CardAnimationSpeed) => {
  let duration = "0.3s";

  if (speed === "slow") duration = "0.6s";
  if (speed === "fast") duration = "0.15s";
  if (speed === "instant") duration = "0.05s";

  switch (type) {
    case "pulse":
      return `${pulseAnimation} ${duration} ease-in-out infinite`;
    case "flip":
      return `${flipAnimation} ${duration} ease-in-out`;
    case "3d-rotate":
      return `${rotateAnimation} ${duration} ease-in-out`;
    case "glow":
      return `${glowAnimation} ${duration} ease-in-out infinite`;
    case "cascade":
      return `${cascadeAnimation} ${duration} ease-out`;
    case "particle":
      return "none"; // Частицы обрабатываются отдельно
    default:
      return "none";
  }
};

// Функция для определения эффектов
const getEffectStyles = (effect: CardEffect, isRed: boolean) => {
  switch (effect) {
    case "holographic":
      return css`
        background-image: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.2) 0%,
          rgba(255, 255, 255, 0.7) 50%,
          rgba(255, 255, 255, 0.2) 100%
        );
        background-size: 200% 200%;
        animation: ${holographicEffect} 3s ease infinite;
      `;
    case "metallic":
      return css`
        background-image: linear-gradient(to bottom, #d3d3d3, #a9a9a9, #d3d3d3);
        border: 1px solid #999;
      `;
    case "neon":
      return css`
        box-shadow:
          0 0 10px ${isRed ? "#ff0066" : "#00ccff"},
          0 0 20px ${isRed ? "#ff0066" : "#00ccff"},
          0 0 30px ${isRed ? "#ff0066" : "#00ccff"};
        text-shadow: 0 0 5px ${isRed ? "#ff0066" : "#00ccff"};
      `;
    case "shadow":
      return css`
        box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.4);
        transform: translateY(-2px);
      `;
    case "none":
    default:
      return "";
  }
};

// Стилизованные компоненты
const CardWrapper = styled.div`
  position: relative;
  width: 100px;
  height: 150px;
  margin: 10px;
  perspective: 1000px;
`;

const CardContainer = styled.div<{
  isSelected?: boolean;
  isPlayable?: boolean;
  isRed?: boolean;
  animationSpeed?: CardAnimationSpeed;
  animationType?: CardAnimationType;
  isFaceDown?: boolean;
  theme?: CardTheme;
  effect?: CardEffect;
  colorTheme?: string;
  highContrast?: boolean;
}>`
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: ${(props) => {
    switch (props.theme) {
      case "modern":
        return "12px";
      case "minimal":
        return "4px";
      case "fantasy":
        return "16px";
      default:
        return "8px";
    }
  }};
  background-color: ${(props) => {
    if (props.isFaceDown) return "#2c3e50";
    switch (props.colorTheme) {
      case "dark":
        return "#222";
      case "colorful":
        return props.isRed ? "#ffebee" : "#e8f5e9";
      case "monochrome":
        return "#f5f5f5";
      default:
        return "white";
    }
  }};
  box-shadow: ${(props) => {
    switch (props.theme) {
      case "modern":
        return "0 8px 16px rgba(0, 0, 0, 0.15)";
      case "minimal":
        return "none";
      case "fantasy":
        return "0 10px 20px rgba(0, 0, 0, 0.2)";
      default:
        return "0 4px 8px rgba(0, 0, 0, 0.1)";
    }
  }};
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  user-select: none;
  transform-style: preserve-3d;
  transform: ${({ isSelected, animationType }) => {
    if (!isSelected) return "none";
    return animationType === "flip" ? "rotateY(0deg)" : "translateY(-20px)";
  }};
  animation: ${({ isSelected, animationType, animationSpeed }) => {
    if (!isSelected) return "none";
    return getAnimation(
      animationType as CardAnimationType,
      animationSpeed as CardAnimationSpeed,
    );
  }};
  transition: ${({ animationSpeed }) => {
    switch (animationSpeed) {
      case "slow":
        return "transform 0.6s ease, box-shadow 0.6s ease, filter 0.6s ease";
      case "fast":
        return "transform 0.15s ease, box-shadow 0.15s ease, filter 0.15s ease";
      case "instant":
        return "transform 0.05s ease, box-shadow 0.05s ease, filter 0.05s ease";
      default:
        return "transform 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease";
    }
  }};
  cursor: ${({ isPlayable }) => (isPlayable ? "pointer" : "default")};
  opacity: ${({ isPlayable }) => (isPlayable ? 1 : 0.7)};
  filter: ${({ isSelected, colorTheme }) => {
    if (colorTheme === "monochrome") return "grayscale(100%)";
    return isSelected ? "brightness(1.1)" : "none";
  }};
  outline: ${({ isSelected, isPlayable }) =>
    isSelected && isPlayable ? "2px solid #4a90e2" : "none"};
  font-family: ${(props) => {
    switch (props.theme) {
      case "modern":
        return '"Roboto", sans-serif';
      case "minimal":
        return '"Helvetica Neue", sans-serif';
      case "fantasy":
        return '"Fantasy", cursive';
      default:
        return '"Times New Roman", serif';
    }
  }};
  ${(props) =>
    getEffectStyles(props.effect as CardEffect, props.isRed as boolean)};

  /* Адаптивность для различных размеров экрана */
  @media (max-width: 768px) {
    width: 80px;
    height: 120px;
    padding: 6px;
  }

  @media (max-width: 480px) {
    width: 60px;
    height: 90px;
    padding: 4px;
  }

  &:hover {
    box-shadow: ${({ isPlayable, theme }) => {
      if (!isPlayable) return "0 2px 5px rgba(0, 0, 0, 0.2)";
      switch (theme) {
        case "modern":
          return "0 12px 24px rgba(0, 0, 0, 0.2)";
        case "minimal":
          return "0 2px 5px rgba(0, 0, 0, 0.1)";
        case "fantasy":
          return "0 15px 30px rgba(0, 0, 0, 0.25)";
        default:
          return "0 8px 16px rgba(0, 0, 0, 0.15)";
      }
    }};
    transform: ${({ isPlayable, isSelected }) =>
      isPlayable && !isSelected
        ? "translateY(-5px)"
        : isSelected
          ? "translateY(-20px)"
          : "none"};
  }

  /* Добавляем тактильную обратную связь при нажатии */
  &:active {
    transform: ${({ isPlayable, isSelected }) =>
      isPlayable && !isSelected
        ? "translateY(0)"
        : isSelected
          ? "translateY(-15px)"
          : "none"};
  }

  /* Улучшение доступности - фокус клавиатуры */
  &:focus-visible {
    outline: 3px solid #4a90e2;
    outline-offset: 2px;
  }
`;

const CardCorner = styled.div<{ position: "top" | "bottom" }>`
  display: flex;
  flex-direction: column;
  align-items: ${({ position }) =>
    position === "top" ? "flex-start" : "flex-end"};
  transform: ${({ position }) =>
    position === "bottom" ? "rotate(180deg)" : "none"};
`;

const Rank = styled.span<{ highContrast?: boolean; colorTheme?: string }>`
  font-size: 1.2rem;
  font-weight: bold;
  line-height: 1;
  text-shadow: ${(props) =>
    props.highContrast ? "0 0 2px rgba(0, 0, 0, 0.5)" : "none"};
  font-weight: ${(props) => (props.highContrast ? "800" : "bold")};
  color: ${(props) => {
    if (props.colorTheme === "dark") return "#fff";
    if (props.colorTheme === "monochrome") return "#333";
    return "inherit";
  }};
`;

const Suit = styled.span<{
  isRed?: boolean;
  highContrast?: boolean;
  colorTheme?: string;
}>`
  font-size: 1.2rem;
  line-height: 1;
  color: ${({ isRed, highContrast, colorTheme }) => {
    if (colorTheme === "dark") return isRed ? "#ff6b6b" : "#a5d8ff";
    if (colorTheme === "monochrome") return "#333";
    if (colorTheme === "colorful") return isRed ? "#d32f2f" : "#2e7d32";
    if (highContrast) {
      return isRed ? "#FF0000" : "#000000";
    }
    return isRed ? "red" : "black";
  }};
  text-shadow: ${(props) =>
    props.highContrast ? "0 0 2px rgba(0, 0, 0, 0.3)" : "none"};
`;

const CardCenter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
`;

const CenterSuit = styled.span<{
  isRed?: boolean;
  highContrast?: boolean;
  colorTheme?: string;
  theme?: CardTheme;
}>`
  font-size: ${(props) => (props.theme === "fantasy" ? "3.5rem" : "3rem")};
  color: ${({ isRed, highContrast, colorTheme }) => {
    if (colorTheme === "dark") return isRed ? "#ff6b6b" : "#a5d8ff";
    if (colorTheme === "monochrome") return "#333";
    if (colorTheme === "colorful") return isRed ? "#d32f2f" : "#2e7d32";
    if (highContrast) {
      return isRed ? "#FF0000" : "#000000";
    }
    return isRed ? "red" : "black";
  }};
  text-shadow: ${(props) =>
    props.highContrast ? "0 0 3px rgba(0, 0, 0, 0.3)" : "none"};
  transform: ${(props) => (props.theme === "fantasy" ? "scale(1.1)" : "none")};
`;

// Компоненты для рубашки карты
const CardBack = styled.div<{ theme?: CardTheme }>`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: ${(props) => {
    switch (props.theme) {
      case "modern":
        return "12px";
      case "minimal":
        return "4px";
      case "fantasy":
        return "16px";
      default:
        return "8px";
    }
  }};
  background-color: ${(props) => {
    switch (props.theme) {
      case "modern":
        return "#1a237e";
      case "minimal":
        return "#455a64";
      case "fantasy":
        return "#4a148c";
      default:
        return "#2c3e50";
    }
  }};
  display: flex;
  justify-content: center;
  align-items: center;
  backface-visibility: hidden;
`;

const CardBackPattern = styled.div<{ theme?: CardTheme }>`
  width: 80%;
  height: 80%;
  border-radius: 5px;
  background-image: ${(props) => {
    switch (props.theme) {
      case "modern":
        return "repeating-linear-gradient(45deg, #3949ab, #3949ab 5px, #303f9f 5px, #303f9f 10px)";
      case "minimal":
        return "linear-gradient(to right, #37474f, #263238)";
      case "fantasy":
        return "radial-gradient(circle, #7b1fa2, #4a148c)";
      default:
        return "repeating-linear-gradient(45deg, #34495e, #34495e 5px, #2c3e50 5px, #2c3e50 10px)";
    }
  }};
  border: ${(props) =>
    props.theme === "minimal" ? "none" : "2px solid rgba(255, 255, 255, 0.2)"};
`;

// Компоненты для эффекта частиц
const ParticlesContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
`;

const Particle = styled.div`
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  animation: ${particleAnimation} 1s ease-out forwards;
`;
