import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import styled from 'styled-components';
import { io, Socket } from 'socket.io-client';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Section = styled.div`
  background: #f8f9fa;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  border: 1px solid #dee2e6;
`;

const Button = styled.button`
  padding: 10px 20px;
  margin: 5px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  
  &:hover {
    background: #0056b3;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const Input = styled.input`
  padding: 8px 12px;
  margin: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
`;

const Log = styled.div`
  max-height: 300px;
  overflow-y: auto;
  background: #f1f3f4;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
`;

const Status = styled.div<{ connected: boolean }>`
  padding: 10px;
  border-radius: 4px;
  color: white;
  background: ${props => props.connected ? '#28a745' : '#dc3545'};
  margin-bottom: 10px;
`;

const TestMultiplayerPage: React.FC = () => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [playerName, setPlayerName] = useState('Тестер');
  const [roomName, setRoomName] = useState('Тестовая комната');
  const [currentRoom, setCurrentRoom] = useState<any>(null);
  const [rooms, setRooms] = useState<any[]>([]);
  const [logs, setLogs] = useState<string[]>([]);
  const [gameState, setGameState] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const connectToServer = () => {
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io('http://localhost:3002', {
      transports: ['websocket']
    });

    newSocket.on('connect', () => {
      setConnected(true);
      addLog('✅ Подключен к серверу');
      
      // Регистрируем игрока
      newSocket.emit('register_player', { name: playerName });
    });

    newSocket.on('disconnect', () => {
      setConnected(false);
      addLog('❌ Отключен от сервера');
    });

    newSocket.on('player_registered', (data) => {
      addLog(`👤 Игрок зарегистрирован: ${data.name} (${data.playerId})`);
    });

    newSocket.on('rooms_list', (roomsList) => {
      setRooms(roomsList);
      addLog(`🏠 Получен список комнат: ${roomsList.length} комнат`);
    });

    newSocket.on('room_created', (room) => {
      addLog(`🏠 Комната создана: ${room.name} (${room.id})`);
      setCurrentRoom(room);
    });

    newSocket.on('room_joined', (room) => {
      addLog(`🚪 Присоединился к комнате: ${room.name}`);
      setCurrentRoom(room);
    });

    newSocket.on('room_updated', (room) => {
      addLog(`🔄 Комната обновлена: ${room.players.length} игроков`);
      setCurrentRoom(room);
    });

    newSocket.on('game_started', (data) => {
      addLog(`🎮 Игра началась! ID: ${data.gameId}`);
      setGameState(data.gameState);
    });

    newSocket.on('game_state_updated', (state) => {
      addLog(`🎯 Состояние игры обновлено`);
      setGameState(state);
    });

    newSocket.on('move_result', (result) => {
      addLog(`🎲 Результат хода: ${result.success ? 'успех' : 'неудача'}`);
    });

    newSocket.on('error', (error) => {
      addLog(`❌ Ошибка: ${error.message}`);
    });

    setSocket(newSocket);
  };

  const createRoom = () => {
    if (!socket || !connected) return;
    
    socket.emit('create_room', {
      name: roomName,
      maxPlayers: 2,
      gameType: 'durak'
    });
  };

  const joinRoom = (roomId: string) => {
    if (!socket || !connected) return;
    
    socket.emit('join_room', { roomId });
  };

  const startGame = () => {
    if (!socket || !connected || !currentRoom) return;
    
    socket.emit('start_game', { roomId: currentRoom.id });
  };

  const makeMove = (action: string) => {
    if (!socket || !connected || !gameState) return;
    
    socket.emit('make_move', {
      gameId: gameState.id,
      action,
      cardIndex: 0 // Для теста используем первую карту
    });
  };

  const disconnect = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setConnected(false);
      setCurrentRoom(null);
      setGameState(null);
    }
  };

  useEffect(() => {
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [socket]);

  return (
    <Container>
      <Head>
        <title>Тест Мультиплеер - Козырь Мастер</title>
      </Head>

      <h1>🌐 Тест Мультиплеера</h1>

      <Status connected={connected}>
        {connected ? '🟢 Подключен к серверу' : '🔴 Не подключен'}
      </Status>

      <Section>
        <h3>🔌 Подключение</h3>
        <Input
          type="text"
          value={playerName}
          onChange={(e) => setPlayerName(e.target.value)}
          placeholder="Имя игрока"
          disabled={connected}
        />
        <Button onClick={connectToServer} disabled={connected}>
          Подключиться
        </Button>
        <Button onClick={disconnect} disabled={!connected}>
          Отключиться
        </Button>
      </Section>

      <Section>
        <h3>🏠 Комнаты</h3>
        <Input
          type="text"
          value={roomName}
          onChange={(e) => setRoomName(e.target.value)}
          placeholder="Название комнаты"
          disabled={!connected}
        />
        <Button onClick={createRoom} disabled={!connected}>
          Создать комнату
        </Button>
        
        {rooms.length > 0 && (
          <div>
            <h4>Доступные комнаты:</h4>
            {rooms.map((room) => (
              <div key={room.id} style={{ margin: '10px 0', padding: '10px', background: '#e9ecef', borderRadius: '4px' }}>
                <strong>{room.name}</strong> - {room.players.length}/{room.maxPlayers} игроков
                <Button onClick={() => joinRoom(room.id)} disabled={!connected}>
                  Присоединиться
                </Button>
              </div>
            ))}
          </div>
        )}
      </Section>

      {currentRoom && (
        <Section>
          <h3>🎮 Текущая комната: {currentRoom.name}</h3>
          <p>Игроки: {currentRoom.players.map((p: any) => p.name).join(', ')}</p>
          <p>Статус: {currentRoom.status}</p>
          <Button onClick={startGame} disabled={currentRoom.players.length < 2}>
            Начать игру
          </Button>
        </Section>
      )}

      {gameState && (
        <Section>
          <h3>🎯 Игровое состояние</h3>
          <p>Статус: {gameState.gameStatus}</p>
          <p>Игроков: {gameState.players?.length || 0}</p>
          <p>Текущий игрок: {gameState.currentPlayerIndex}</p>
          
          <div>
            <Button onClick={() => makeMove('attack')}>Атака</Button>
            <Button onClick={() => makeMove('defend')}>Защита</Button>
            <Button onClick={() => makeMove('take')}>Взять</Button>
            <Button onClick={() => makeMove('pass')}>Пас</Button>
          </div>
        </Section>
      )}

      <Section>
        <h3>📝 Лог событий</h3>
        <Log>
          {logs.join('\n')}
        </Log>
        <Button onClick={() => setLogs([])}>Очистить лог</Button>
      </Section>
    </Container>
  );
};

export default TestMultiplayerPage;
