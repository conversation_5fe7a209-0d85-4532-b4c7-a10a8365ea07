import { useState, useCallback, useEffect } from 'react';
import {
  DurakGame,
  GameState,
  Player,
  Card,
  GameRules,
  PlayerAction,
  GameEvent,
  GameEventData,
  DurakVariant,
  GameStatus
} from '@kozyr-master/core';

interface GameEventLog {
  type: string;
  message: string;
  timestamp: number;
  playerId?: string;
  data?: any;
}

interface UseDurakGameReturn {
  game: DurakGame | null;
  gameState: GameState | null;
  currentPlayer: Player | null;
  isMyTurn: boolean;
  gameEvents: GameEventLog[];

  // Действия
  createNewGame: (rules: GameRules) => void;
  addPlayer: (name: string, isBot?: boolean) => void;
  startGame: () => void;
  makeMove: (action: PlayerAction, cardIndex?: number) => void;

  // Утилиты
  canPlayCard: (card: Card) => boolean;
  resetGame: () => void;
}

export const useDurakGame = (playerId?: string): UseDurakGameReturn => {
  const [game, setGame] = useState<DurakGame | null>(null);
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [gameEvents, setGameEvents] = useState<GameEventLog[]>([]);
  const [currentPlayerId] = useState<string>(
    playerId || `player-${Date.now()}`
  );

  // Обновление состояния игры
  const updateGameState = useCallback(() => {
    if (game) {
      setGameState(game.getState());
    }
  }, [game]);

  // Добавление события
  const addEvent = useCallback((event: Omit<GameEventLog, 'timestamp'>) => {
    const newEvent: GameEventLog = {
      ...event,
      timestamp: Date.now()
    };
    setGameEvents(prev => [...prev, newEvent]);
  }, []);

  // Создание новой игры
  const createNewGame = useCallback((rules: GameRules) => {
    try {
      const players: Player[] = [];
      const newGame = new DurakGame(players, rules);

      // Подписываемся на события игры
      newGame.addEventListener((event: GameEventData) => {
        addEvent({
          type: event.type,
          message: event.message || `Game event: ${event.type}`,
          playerId: event.playerId,
          data: event
        });
      });

      setGame(newGame);
      setGameState(newGame.getState());
      setGameEvents([]);

      addEvent({
        type: 'game_created',
        message: 'Новая игра создана'
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка создания игры'
      });
    }
  }, [addEvent]);

  // Добавление игрока (пересоздание игры с новыми игроками)
  const addPlayer = useCallback((name: string, isBot: boolean = false) => {
    if (!game) {
      addEvent({
        type: 'error',
        message: 'Игра не создана'
      });
      return;
    }

    try {
      const currentState = game.getState();
      const newPlayer: Player = {
        id: isBot ? `bot-${Date.now()}` : currentPlayerId,
        name,
        hand: [],
        isActive: false
      };

      // Создаем новую игру с обновленным списком игроков
      const allPlayers = [...currentState.players, newPlayer];
      const rules: GameRules = {
        variant: DurakVariant.CLASSIC,
        numberOfPlayers: allPlayers.length,
        initialHandSize: 6,
        attackLimit: 6
      };

      const newGame = new DurakGame(allPlayers, rules);

      // Подписываемся на события новой игры
      newGame.addEventListener((event: GameEventData) => {
        addEvent({
          type: event.type,
          message: event.message || `Game event: ${event.type}`,
          playerId: event.playerId,
          data: event
        });
      });

      setGame(newGame);
      setGameState(newGame.getState());

      addEvent({
        type: 'player_joined',
        playerId: newPlayer.id,
        message: `${name} ${isBot ? '(бот)' : ''} присоединился к игре`
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка добавления игрока'
      });
    }
  }, [game, currentPlayerId, addEvent]);

  // Начало игры
  const startGame = useCallback(() => {
    if (!game) {
      addEvent({
        type: 'error',
        message: 'Игра не создана'
      });
      return;
    }

    try {
      // Добавляем бота если недостаточно игроков
      const currentState = game.getState();
      if (currentState.players.length < 2) {
        addPlayer('ИИ Противник', true);
        return; // addPlayer пересоздаст игру, поэтому выходим
      }

      game.startGame();
      updateGameState();

      addEvent({
        type: 'game_started',
        message: 'Карты розданы, игра началась!'
      });
    } catch (error) {
      addEvent({
        type: 'error',
        message: error instanceof Error ? error.message : 'Ошибка начала игры'
      });
    }
  }, [game, addPlayer, updateGameState, addEvent]);

  // Выполнение хода
  const makeMove = useCallback((action: PlayerAction, cardIndex?: number) => {
    if (!game) {
      addEvent({
        type: 'error',
        message: 'Игра не создана'
      });
      return;
    }

    try {
      const success = game.makeMove(currentPlayerId, action, cardIndex);
      updateGameState();

      if (success) {
        addEvent({
          type: 'player_moved',
          playerId: currentPlayerId,
          message: `Игрок выполнил действие: ${action}`,
          data: { action, cardIndex }
        });

        // Проверяем окончание игры
        const newState = game.getState();
        if (newState.gameStatus === GameStatus.FINISHED) {
          addEvent({
            type: 'game_finished',
            message: newState.winner
              ? `Игра окончена! Победитель: ${newState.winner.name || newState.winner.id}`
              : 'Игра окончена'
          });
        }
      } else {
        addEvent({
          type: 'error',
          playerId: currentPlayerId,
          message: 'Недопустимый ход'
        });
      }
    } catch (error) {
      addEvent({
        type: 'error',
        playerId: currentPlayerId,
        message: error instanceof Error ? error.message : 'Ошибка выполнения хода'
      });
    }
  }, [game, currentPlayerId, updateGameState, addEvent]);

  // Проверка, можно ли сыграть карту
  const canPlayCard = useCallback((card: Card): boolean => {
    if (!game || !gameState) return false;

    const currentPlayer = gameState.players.find(p => p.id === currentPlayerId);
    if (!currentPlayer) return false;

    // Проверяем, есть ли карта у игрока
    const hasCard = currentPlayer.hand.some(c => c.suit === card.suit && c.rank === card.rank);
    if (!hasCard) return false;

    // Проверяем, наш ли ход или можем ли мы играть
    const isMyTurn = gameState.currentPlayerIndex === gameState.players.findIndex(p => p.id === currentPlayerId);
    const isActive = currentPlayer.isActive;

    return isMyTurn || isActive;
  }, [game, gameState, currentPlayerId]);

  // Сброс игры
  const resetGame = useCallback(() => {
    setGame(null);
    setGameState(null);
    setGameEvents([]);
  }, []);

  // Вычисляемые значения
  const currentPlayer = gameState?.players.find(p => p.id === currentPlayerId) || null;
  const isMyTurn = gameState ? 
    gameState.players[gameState.currentPlayerIndex]?.id === currentPlayerId : false;

  // Автоматические действия ботов
  useEffect(() => {
    if (!game || !gameState || gameState.gameStatus === GameStatus.FINISHED) return;

    const currentGamePlayer = gameState.players[gameState.currentPlayerIndex];
    if (!currentGamePlayer || !currentGamePlayer.name?.includes('бот') && !currentGamePlayer.name?.includes('ИИ')) return;

    // Простая логика бота - случайное действие через небольшую задержку
    const timer = setTimeout(() => {
      try {
        // Простая логика: пробуем разные действия
        const actions = [PlayerAction.ATTACK, PlayerAction.DEFEND, PlayerAction.TAKE, PlayerAction.PASS];

        for (const action of actions) {
          // Для атаки пробуем случайную карту
          if (action === PlayerAction.ATTACK && currentGamePlayer.hand.length > 0) {
            const randomCardIndex = Math.floor(Math.random() * currentGamePlayer.hand.length);
            const success = game.makeMove(currentGamePlayer.id, action, randomCardIndex);
            if (success) {
              updateGameState();
              break;
            }
          } else {
            // Для других действий не нужен индекс карты
            const success = game.makeMove(currentGamePlayer.id, action);
            if (success) {
              updateGameState();
              break;
            }
          }
        }
      } catch (error) {
        console.error('Ошибка хода бота:', error);
      }
    }, 1000 + Math.random() * 2000); // 1-3 секунды задержка

    return () => clearTimeout(timer);
  }, [game, gameState, updateGameState]);

  return {
    game,
    gameState,
    currentPlayer,
    isMyTurn,
    gameEvents,

    createNewGame,
    addPlayer,
    startGame,
    makeMove,

    canPlayCard,
    resetGame
  };
};
