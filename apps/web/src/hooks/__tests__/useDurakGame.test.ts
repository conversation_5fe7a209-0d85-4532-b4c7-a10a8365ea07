import { renderHook, act } from '@testing-library/react';
import { useDurakGame } from '../useDurakGame';
import { GameRules, DurakVariant, GameStatus } from '@kozyr-master/core';

describe('useDurakGame', () => {
  const defaultRules: GameRules = {
    variant: DurakVariant.CLASSIC,
    numberOfPlayers: 2,
    initialHandSize: 6,
    attackLimit: 6
  };

  it('должен инициализироваться с пустым состоянием', () => {
    const { result } = renderHook(() => useDurakGame());

    expect(result.current.game).toBeNull();
    expect(result.current.gameState).toBeNull();
    expect(result.current.currentPlayer).toBeNull();
    expect(result.current.isMyTurn).toBe(false);
    expect(result.current.gameEvents).toEqual([]);
  });

  it('должен создавать новую игру', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    expect(result.current.game).not.toBeNull();
    expect(result.current.gameState).not.toBeNull();
    expect(result.current.gameState?.gameStatus).toBe(GameStatus.NOT_STARTED);
    expect(result.current.gameEvents.length).toBeGreaterThan(0);
    expect(result.current.gameEvents[0].message).toBe('Новая игра создана');
  });

  it('должен добавлять игрока в игру', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.addPlayer('Тестовый игрок');
    });

    expect(result.current.gameState?.players.length).toBe(1);
    expect(result.current.gameState?.players[0].name).toBe('Тестовый игрок');
    // В новом API нет поля isBot, проверяем по имени
    expect(result.current.gameState?.players[0].name).toBe('Тестовый игрок');
  });

  it('должен добавлять бота в игру', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.addPlayer('Бот', true);
    });

    expect(result.current.gameState?.players.length).toBe(1);
    expect(result.current.gameState?.players[0].name).toBe('Бот');
    // В новом API нет поля isBot, проверяем по имени
    expect(result.current.gameState?.players[0].name).toBe('Бот');
  });

  it('должен начинать игру с достаточным количеством игроков', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.addPlayer('Игрок 1');
    });

    act(() => {
      result.current.addPlayer('Игрок 2');
    });

    act(() => {
      result.current.startGame();
    });

    expect(result.current.gameState?.phase).not.toBe('waiting');
    expect(result.current.gameState?.players.every(p => p.hand.length > 0)).toBe(true);
  });

  it('должен автоматически добавлять бота если недостаточно игроков', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.addPlayer('Игрок 1');
    });

    act(() => {
      result.current.startGame();
    });

    // Должен добавиться бот автоматически
    expect(result.current.gameState?.players.length).toBe(2);
    // В новом API нет поля isBot, проверяем по имени
    expect(result.current.gameState?.players.some(p => p.name?.includes('ИИ'))).toBe(true);
  });

  it('должен сбрасывать игру', () => {
    const { result } = renderHook(() => useDurakGame());

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.resetGame();
    });

    expect(result.current.game).toBeNull();
    expect(result.current.gameState).toBeNull();
    expect(result.current.gameEvents).toEqual([]);
  });

  it('должен определять текущего игрока', () => {
    const { result } = renderHook(() => useDurakGame('test-player'));

    act(() => {
      result.current.createNewGame(defaultRules);
    });

    act(() => {
      result.current.addPlayer('Тестовый игрок');
    });

    expect(result.current.currentPlayer?.name).toBe('Тестовый игрок');
  });

  it('должен обрабатывать ошибки при создании игры', () => {
    const { result } = renderHook(() => useDurakGame());

    // Попытка добавить игрока без создания игры
    act(() => {
      try {
        result.current.addPlayer('Игрок');
      } catch (error) {
        // Ошибка должна быть обработана внутри хука
      }
    });

    // Проверяем, что ошибка записана в события
    const errorEvents = result.current.gameEvents.filter(e => e.type === 'error');
    expect(errorEvents.length).toBeGreaterThan(0);
  });
});
