/**
 * Интеграционный тест мультиплеера
 */

import { io, Socket } from 'socket.io-client';

describe('Multiplayer Integration', () => {
  let socket: Socket;
  const SERVER_URL = 'http://localhost:3002';

  beforeAll((done) => {
    // Подключаемся к серверу перед тестами
    socket = io(SERVER_URL, {
      transports: ['websocket']
    });

    socket.on('connect', () => {
      done();
    });

    socket.on('connect_error', (error) => {
      done(error);
    });
  });

  afterAll(() => {
    if (socket) {
      socket.disconnect();
    }
  });

  it('должен подключиться к игровому серверу', (done) => {
    expect(socket.connected).toBe(true);
    done();
  });

  it('должен зарегистрировать игрока', (done) => {
    socket.emit('register_player', { name: 'Тестовый игрок' });

    socket.on('player_registered', (data) => {
      expect(data.name).toBe('Тестовый игрок');
      expect(data.playerId).toBeDefined();
      done();
    });

    // Таймаут на случай если событие не придет
    setTimeout(() => {
      done(new Error('Timeout: player_registered event not received'));
    }, 5000);
  });

  it('должен получить список комнат', (done) => {
    socket.on('rooms_list', (rooms) => {
      expect(Array.isArray(rooms)).toBe(true);
      done();
    });

    // Таймаут на случай если событие не придет
    setTimeout(() => {
      done(new Error('Timeout: rooms_list event not received'));
    }, 5000);
  });

  it('должен создать комнату', (done) => {
    const roomData = {
      name: 'Тестовая комната',
      maxPlayers: 2,
      gameType: 'durak'
    };

    socket.emit('create_room', roomData);

    socket.on('room_created', (room) => {
      expect(room.name).toBe(roomData.name);
      expect(room.maxPlayers).toBe(roomData.maxPlayers);
      expect(room.players.length).toBe(1);
      done();
    });

    // Таймаут на случай если событие не придет
    setTimeout(() => {
      done(new Error('Timeout: room_created event not received'));
    }, 5000);
  });
});
