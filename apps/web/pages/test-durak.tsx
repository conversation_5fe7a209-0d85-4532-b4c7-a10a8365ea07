import React from 'react';
import Head from 'next/head';
import styled from 'styled-components';
import { useDurakGame } from '../hooks/useDurakGame';
import { GameRules, DurakVariant, PlayerAction, GameStatus } from '@kozyr-master/core';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const GameArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const Controls = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const Button = styled.button`
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  
  &:hover {
    background: #0056b3;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
`;

const GameInfo = styled.div`
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #dee2e6;
`;

const PlayerHand = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 10px;
`;

const Card = styled.div<{ isPlayable?: boolean }>`
  width: 60px;
  height: 80px;
  border: 2px solid ${props => props.isPlayable ? '#28a745' : '#dee2e6'};
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  cursor: ${props => props.isPlayable ? 'pointer' : 'default'};
  font-size: 12px;
  text-align: center;
  
  &:hover {
    ${props => props.isPlayable && `
      border-color: #1e7e34;
      transform: translateY(-2px);
    `}
  }
`;

const EventLog = styled.div`
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #dee2e6;
`;

const Event = styled.div`
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  
  &:last-child {
    border-bottom: none;
  }
`;

const TestDurakPage: React.FC = () => {
  const {
    game,
    gameState,
    currentPlayer,
    isMyTurn,
    gameEvents,
    createNewGame,
    addPlayer,
    startGame,
    makeMove,
    canPlayCard,
    resetGame
  } = useDurakGame();

  const handleCreateGame = () => {
    const rules: GameRules = {
      variant: DurakVariant.CLASSIC,
      numberOfPlayers: 2,
      initialHandSize: 6,
      attackLimit: 6
    };
    createNewGame(rules);
  };

  const handleAddPlayer = () => {
    addPlayer('Игрок 1');
  };

  const handleAddBot = () => {
    addPlayer('Бот', true);
  };

  const handleCardClick = (card: any, cardIndex: number) => {
    if (!canPlayCard(card) || !isMyTurn) return;

    // Пробуем атаку с этой картой
    makeMove(PlayerAction.ATTACK, cardIndex);
  };

  const handleAction = (actionType: string) => {
    let action: PlayerAction;

    switch (actionType) {
      case 'pass':
        action = PlayerAction.PASS;
        break;
      case 'take':
        action = PlayerAction.TAKE;
        break;
      case 'defend':
        action = PlayerAction.DEFEND;
        break;
      default:
        return;
    }

    makeMove(action);
  };

  const getSuitSymbol = (suit: string) => {
    const symbols = {
      hearts: '♥️',
      diamonds: '♦️',
      clubs: '♣️',
      spades: '♠️'
    };
    return symbols[suit as keyof typeof symbols] || suit;
  };

  return (
    <Container>
      <Head>
        <title>Тест Дурак - Козырь Мастер</title>
      </Head>

      <h1>Тест игры Дурак</h1>
      
      <GameArea>
        <Controls>
          <Button onClick={handleCreateGame} disabled={!!game}>
            Создать игру
          </Button>
          <Button onClick={handleAddPlayer} disabled={!game || !!gameState?.players.find(p => !p.isBot)}>
            Добавить игрока
          </Button>
          <Button onClick={handleAddBot} disabled={!game}>
            Добавить бота
          </Button>
          <Button onClick={startGame} disabled={!game || gameState?.gameStatus !== GameStatus.NOT_STARTED}>
            Начать игру
          </Button>
          <Button onClick={resetGame}>
            Сбросить игру
          </Button>
        </Controls>

        {gameState && (
          <GameInfo>
            <h3>Информация об игре</h3>
            <p><strong>Статус:</strong> {gameState.gameStatus}</p>
            <p><strong>Игроки:</strong> {gameState.players.length}</p>
            {gameState.trumpCard && (
              <p><strong>Козырь:</strong> {gameState.trumpCard.rank} {getSuitSymbol(gameState.trumpCard.suit)}</p>
            )}
            <p><strong>Карт в колоде:</strong> {gameState.deck.length}</p>
            {gameState.currentPlayerIndex >= 0 && (
              <p><strong>Ход игрока:</strong> {gameState.players[gameState.currentPlayerIndex]?.name || gameState.players[gameState.currentPlayerIndex]?.id}</p>
            )}
            {isMyTurn && <p style={{color: 'green'}}><strong>Ваш ход!</strong></p>}
          </GameInfo>
        )}

        {currentPlayer && (
          <div>
            <h3>Ваши карты ({currentPlayer.name || currentPlayer.id})</h3>
            <PlayerHand>
              {currentPlayer.hand.map((card, index) => (
                <Card
                  key={`${card.suit}-${card.rank}-${index}`}
                  isPlayable={canPlayCard(card) && isMyTurn}
                  onClick={() => handleCardClick(card, index)}
                >
                  <div>{card.rank}</div>
                  <div>{getSuitSymbol(card.suit)}</div>
                </Card>
              ))}
            </PlayerHand>
          </div>
        )}

        {isMyTurn && (
          <Controls>
            <Button onClick={() => handleAction('pass')}>
              Пас
            </Button>
            <Button onClick={() => handleAction('take')}>
              Взять карты
            </Button>
          </Controls>
        )}

        {gameState?.tableCards && gameState.tableCards.length > 0 && (
          <div>
            <h3>Стол</h3>
            <PlayerHand>
              {gameState.tableCards.map((cardPair, index) => (
                <div key={index} style={{display: 'flex', flexDirection: 'column', gap: '5px'}}>
                  {cardPair.map((card, cardIndex) => (
                    <Card key={cardIndex}>
                      <div>{card.rank}</div>
                      <div>{getSuitSymbol(card.suit)}</div>
                    </Card>
                  ))}
                </div>
              ))}
            </PlayerHand>
          </div>
        )}

        <div>
          <h3>События игры</h3>
          <EventLog>
            {gameEvents.map((event, index) => (
              <Event key={index}>
                <strong>{new Date(event.timestamp).toLocaleTimeString()}:</strong> {event.message}
              </Event>
            ))}
          </EventLog>
        </div>
      </GameArea>
    </Container>
  );
};

export default TestDurakPage;
