import React, { useEffect, useState, Suspense } from 'react';
import Head from "next/head";
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import { Canvas } from '@react-three/fiber';
import styled from "styled-components";
import { useRouter } from "next/router";

// Динамическая загрузка тяжелых компонентов
const LoadingScreen = dynamic(() => import('../components/LoadingScreen'), { 
  ssr: false 
});

const Hero3D = dynamic(() => import('../components/Hero3D'), { 
  ssr: false,
  loading: () => <div>Загрузка квантового движка...</div>
});

const RevolutionaryFeatures = dynamic(() => import('../components/RevolutionaryFeatures'), {
  ssr: false
});

const MetaversePreview = dynamic(() => import('../components/MetaversePreview-simple'), {
  ssr: false
});

const AIShowcase = dynamic(() => import('../components/AIShowcase'), {
  ssr: false
});

const Web3Dashboard = dynamic(() => import('../components/Web3Dashboard'), {
  ssr: false
});

const StreamingPlatform = dynamic(() => import('../components/StreamingPlatform'), {
  ssr: false
});

const GameDemo = dynamic(() => import('../components/GameDemo'), {
  ssr: false
});

const Footer = dynamic(() => import('../components/Footer'), {
  ssr: false
});

const HomePage = () => {
  const router = useRouter();
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentSection, setCurrentSection] = useState(0);
  const [quantumStatus, setQuantumStatus] = useState({ 
    isQuantumAvailable: false, 
    metrics: { entropy: 0 } 
  });
  const [emotionalState, setEmotionalState] = useState({ happiness: 0.5 });
  const [web3Status, setWeb3Status] = useState({ connected: false });

  useEffect(() => {
    // Инициализация всех систем
    const initializeSystems = async () => {
      try {
        // Симуляция загрузки квантовых систем
        await new Promise(resolve => setTimeout(resolve, 3000));
        setQuantumStatus({ 
          isQuantumAvailable: true, 
          metrics: { entropy: 0.999 } 
        });
        setEmotionalState({ happiness: 0.8 });
        setIsLoaded(true);
      } catch (error) {
        console.error('Error initializing systems:', error);
        setIsLoaded(true);
      }
    };

    initializeSystems();
  }, []);

  useEffect(() => {
    // Обработка скролла для анимаций
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      const section = Math.floor(scrollY / windowHeight);
      setCurrentSection(section);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const connectWallet = () => {
    setWeb3Status({ connected: true });
  };

  if (!isLoaded) {
    return <LoadingScreen message="Инициализация квантовых систем..." />;
  }

  return (
    <Container>
      <Head>
        <title>Козырь Мастер 4.0 - Революция карточных игр</title>
        <meta name="description" content="Первая в мире платформа карточных игр с квантовой случайностью, эмоциональным ИИ и 3D метавселенной" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Основной контент */}
      <MainContent>
        <AnimatePresence mode="wait">
          {/* Секция 1: Революционный Hero */}
          <Section
            key="hero"
            initial={{ opacity: 0 }}
            animate={{ opacity: currentSection === 0 ? 1 : 0.3 }}
            transition={{ duration: 0.8 }}
          >
            <Hero3D 
              quantumStatus={quantumStatus}
              emotionalState={emotionalState}
              onStartJourney={() => setCurrentSection(1)}
            />
          </Section>

          {/* Секция 2: Революционные технологии */}
          <Section
            key="features"
            initial={{ opacity: 0, y: 100 }}
            animate={{ 
              opacity: currentSection >= 1 ? 1 : 0,
              y: currentSection >= 1 ? 0 : 100
            }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <RevolutionaryFeatures currentSection={currentSection} />
          </Section>

          {/* Секция 3: Метавселенная */}
          <Section
            key="metaverse"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ 
              opacity: currentSection >= 2 ? 1 : 0,
              scale: currentSection >= 2 ? 1 : 0.8
            }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <MetaversePreview />
          </Section>

          {/* Секция 4: ИИ демонстрация */}
          <Section
            key="ai"
            initial={{ opacity: 0, rotateY: 90 }}
            animate={{
              opacity: currentSection >= 3 ? 1 : 0,
              rotateY: currentSection >= 3 ? 0 : 90
            }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <AIShowcase emotionalState={emotionalState} />
          </Section>

          {/* Секция 5: Web3 экосистема */}
          <Section
            key="web3"
            initial={{ opacity: 0, x: -100 }}
            animate={{
              opacity: currentSection >= 4 ? 1 : 0,
              x: currentSection >= 4 ? 0 : -100
            }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <Web3Dashboard
              web3Status={web3Status}
              onConnectWallet={connectWallet}
            />
          </Section>

          {/* Секция 6: Стриминг платформа */}
          <Section
            key="streaming"
            initial={{ opacity: 0, x: 100 }}
            animate={{
              opacity: currentSection >= 5 ? 1 : 0,
              x: currentSection >= 5 ? 0 : 100
            }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <StreamingPlatform />
          </Section>

          {/* Секция 7: Игровая демонстрация */}
          <Section
            key="game-demo"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: currentSection >= 6 ? 1 : 0,
              scale: currentSection >= 6 ? 1 : 0.8
            }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <GameDemo onStartGame={() => router.push('/games')} />
          </Section>
        </AnimatePresence>
      </MainContent>

      {/* Footer */}
      <Footer onSubscribe={(email) => console.log('Подписка:', email)} />

      {/* Навигационные индикаторы */}
      <NavigationDots>
        {[0, 1, 2, 3, 4, 5, 6].map((section) => (
          <NavDot
            key={section}
            active={currentSection === section}
            onClick={() => {
              window.scrollTo({
                top: section * window.innerHeight,
                behavior: 'smooth'
              });
            }}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </NavigationDots>

      {/* Квантовый статус индикатор */}
      <QuantumStatus
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 1 }}
      >
        <StatusIndicator active={quantumStatus?.isQuantumAvailable} />
        <StatusText>
          {quantumStatus?.isQuantumAvailable ? 'Квантовая связь активна' : 'Криптографический режим'}
        </StatusText>
        <StatusDetail>
          Энтропия: {quantumStatus?.metrics?.entropy?.toFixed(3) || 'N/A'}
        </StatusDetail>
      </QuantumStatus>

      {/* Эмоциональный ИИ индикатор */}
      <AIStatus
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5 }}
      >
        <StatusIndicator active={true} color="#4a90e2" />
        <StatusText>ИИ анализ активен</StatusText>
        <StatusDetail>
          Настроение: {emotionalState?.happiness > 0.7 ? '😊' :
                      emotionalState?.happiness > 0.4 ? '😐' : '😔'}
        </StatusDetail>
        <TestButton onClick={() => router.push('/test-durak')}>
          🎮 Тест Дурак
        </TestButton>
      </AIStatus>
    </Container>
  );
};

// Стилизованные компоненты
const Container = styled.div`
  min-height: 100vh;
  background: #000;
  overflow-x: hidden;
`;

const MainContent = styled.div`
  position: relative;
  z-index: 10;
`;

const Section = styled(motion.section)`
  min-height: 100vh;
  width: 100%;
`;

const PlaceholderSection = styled.div<{ background: string }>`
  min-height: 100vh;
  background: ${props => props.background};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  
  h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #4a90e2, #7b68ee, #9370db);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  p {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
  }
`;

const NavigationDots = styled.div`
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const NavDot = styled(motion.button)<{ active: boolean }>`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid ${props => props.active ? 'white' : 'rgba(255, 255, 255, 0.4)'};
  background: ${props => props.active 
    ? 'linear-gradient(45deg, #4a90e2, #7b68ee)' 
    : 'transparent'};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: white;
  }
`;

const QuantumStatus = styled(motion.div)`
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 50;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
`;

const AIStatus = styled(motion.div)`
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  z-index: 50;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
`;

const StatusIndicator = styled.div<{ active: boolean; color?: string }>`
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: ${props => props.active ? (props.color || '#4ade80') : '#fbbf24'};
  display: inline-block;
  margin-right: 0.5rem;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

const StatusText = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatusDetail = styled.div`
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
`;

const TestButton = styled.button`
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(45deg, #4a90e2, #7b68ee);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
  }
`;

export default HomePage;
