<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749626618814" clover="3.2.0">
  <project timestamp="1749626618814" name="All files">
    <metrics statements="480" coveredstatements="338" conditionals="242" coveredconditionals="147" methods="90" coveredmethods="68" elements="812" coveredelements="553" complexity="0" loc="480" ncloc="480" packages="2" files="3" classes="3"/>
    <package name="src">
      <metrics statements="36" coveredstatements="36" conditionals="12" coveredconditionals="12" methods="6" coveredmethods="6"/>
      <file name="types.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/types.ts">
        <metrics statements="36" coveredstatements="36" conditionals="12" coveredconditionals="12" methods="6" coveredmethods="6"/>
        <line num="6" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="13" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="17" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="39" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="2" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="75" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="82" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="90" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="91" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="94" count="2" type="stmt"/>
        <line num="95" count="2" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.durak">
      <metrics statements="444" coveredstatements="302" conditionals="230" coveredconditionals="135" methods="84" coveredmethods="62"/>
      <file name="bot.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/durak/bot.ts">
        <metrics statements="117" coveredstatements="75" conditionals="69" coveredconditionals="42" methods="42" coveredmethods="25"/>
        <line num="5" count="1" type="stmt"/>
        <line num="17" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="41" count="22" type="stmt"/>
        <line num="42" count="22" type="stmt"/>
        <line num="43" count="22" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="62" count="95" type="stmt"/>
        <line num="63" count="64" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="64" type="stmt"/>
        <line num="68" count="64" type="stmt"/>
        <line num="71" count="64" type="cond" truecount="4" falsecount="0"/>
        <line num="73" count="33" type="stmt"/>
        <line num="74" count="31" type="cond" truecount="4" falsecount="0"/>
        <line num="76" count="30" type="stmt"/>
        <line num="77" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="1" type="cond" truecount="1" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="96" count="33" type="stmt"/>
        <line num="98" count="33" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="0" type="stmt"/>
        <line num="108" count="33" type="cond" truecount="3" falsecount="1"/>
        <line num="111" count="30" type="stmt"/>
        <line num="112" count="30" type="stmt"/>
        <line num="116" count="2" type="stmt"/>
        <line num="117" count="2" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="33" type="stmt"/>
        <line num="139" count="30" type="stmt"/>
        <line num="140" count="30" type="cond" truecount="2" falsecount="1"/>
        <line num="141" count="0" type="stmt"/>
        <line num="147" count="30" type="stmt"/>
        <line num="148" count="30" type="stmt"/>
        <line num="150" count="30" type="cond" truecount="1" falsecount="0"/>
        <line num="151" count="3" type="stmt"/>
        <line num="158" count="27" type="stmt"/>
        <line num="160" count="27" type="cond" truecount="1" falsecount="1"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="27" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="210" count="31" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="33" type="cond" truecount="1" falsecount="0"/>
        <line num="220" count="183" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="30" type="stmt"/>
        <line num="233" count="586" type="stmt"/>
        <line num="234" count="586" type="stmt"/>
        <line num="235" count="152" type="stmt"/>
        <line num="239" count="586" type="stmt"/>
        <line num="240" count="306" type="stmt"/>
        <line num="251" count="306" type="stmt"/>
        <line num="255" count="586" type="cond" truecount="3" falsecount="0"/>
        <line num="256" count="65" type="stmt"/>
        <line num="260" count="521" type="cond" truecount="3" falsecount="0"/>
        <line num="261" count="87" type="stmt"/>
        <line num="265" count="434" type="cond" truecount="3" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="434" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="282" count="27" type="cond" truecount="1" falsecount="3"/>
        <line num="284" count="0" type="stmt"/>
        <line num="288" count="27" type="cond" truecount="2" falsecount="0"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="316" count="3" type="stmt"/>
        <line num="317" count="18" type="stmt"/>
        <line num="321" count="18" type="stmt"/>
        <line num="324" count="3" type="stmt"/>
        <line num="325" count="15" type="stmt"/>
        <line num="326" count="15" type="stmt"/>
        <line num="329" count="15" type="cond" truecount="3" falsecount="0"/>
        <line num="330" count="1" type="stmt"/>
        <line num="332" count="14" type="cond" truecount="3" falsecount="0"/>
        <line num="333" count="5" type="stmt"/>
        <line num="337" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="343" count="1" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="358" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="374" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="381" count="15" type="stmt"/>
        <line num="382" count="15" type="stmt"/>
        <line num="389" count="3" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/S/a/A1-K/packages/core/src/durak/index.ts">
        <metrics statements="327" coveredstatements="227" conditionals="161" coveredconditionals="93" methods="42" coveredmethods="37"/>
        <line num="8" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="28" count="15" type="stmt"/>
        <line num="31" count="15" type="stmt"/>
        <line num="32" count="15" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="56" count="89" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="70" count="15" type="stmt"/>
        <line num="71" count="15" type="stmt"/>
        <line num="74" count="15" type="stmt"/>
        <line num="75" count="15" type="stmt"/>
        <line num="78" count="15" type="stmt"/>
        <line num="81" count="15" type="stmt"/>
        <line num="83" count="15" type="stmt"/>
        <line num="101" count="15" type="stmt"/>
        <line num="102" count="15" type="stmt"/>
        <line num="103" count="15" type="stmt"/>
        <line num="105" count="15" type="stmt"/>
        <line num="106" count="60" type="stmt"/>
        <line num="107" count="540" type="stmt"/>
        <line num="111" count="15" type="stmt"/>
        <line num="118" count="15" type="stmt"/>
        <line num="119" count="525" type="stmt"/>
        <line num="120" count="525" type="stmt"/>
        <line num="128" count="15" type="stmt"/>
        <line num="130" count="15" type="stmt"/>
        <line num="131" count="90" type="stmt"/>
        <line num="132" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="133" count="192" type="stmt"/>
        <line num="134" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="135" count="192" type="stmt"/>
        <line num="146" count="15" type="stmt"/>
        <line num="147" count="15" type="stmt"/>
        <line num="150" count="15" type="stmt"/>
        <line num="151" count="32" type="stmt"/>
        <line num="152" count="32" type="stmt"/>
        <line num="153" count="192" type="cond" truecount="1" falsecount="0"/>
        <line num="154" count="48" type="stmt"/>
        <line num="155" count="48" type="cond" truecount="1" falsecount="0"/>
        <line num="156" count="32" type="stmt"/>
        <line num="157" count="32" type="stmt"/>
        <line num="164" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="15" type="stmt"/>
        <line num="175" count="56" type="stmt"/>
        <line num="187" count="56" type="stmt"/>
        <line num="194" count="241" type="stmt"/>
        <line num="201" count="89" type="stmt"/>
        <line num="202" count="185" type="stmt"/>
        <line num="210" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="211" count="11" type="stmt"/>
        <line num="212" count="11" type="stmt"/>
        <line num="215" count="11" type="stmt"/>
        <line num="221" count="11" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="239" count="76" type="stmt"/>
        <line num="240" count="76" type="stmt"/>
        <line num="241" count="76" type="stmt"/>
        <line num="242" count="76" type="stmt"/>
        <line num="243" count="76" type="cond" truecount="2" falsecount="0"/>
        <line num="244" count="76" type="stmt"/>
        <line num="247" count="76" type="cond" truecount="2" falsecount="0"/>
        <line num="250" count="75" type="cond" truecount="2" falsecount="0"/>
        <line num="254" count="1" type="stmt"/>
        <line num="256" count="38" type="stmt"/>
        <line num="259" count="37" type="cond" truecount="1" falsecount="1"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="268" count="37" type="cond" truecount="6" falsecount="0"/>
        <line num="270" count="2" type="stmt"/>
        <line num="271" count="2" type="stmt"/>
        <line num="274" count="35" type="cond" truecount="1" falsecount="1"/>
        <line num="279" count="35" type="cond" truecount="2" falsecount="1"/>
        <line num="280" count="0" type="stmt"/>
        <line num="286" count="35" type="cond" truecount="0" falsecount="1"/>
        <line num="290" count="0" type="stmt"/>
        <line num="295" count="35" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="304" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="318" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="326" count="1" type="cond" truecount="4" falsecount="1"/>
        <line num="328" count="1" type="stmt"/>
        <line num="329" count="1" type="stmt"/>
        <line num="330" count="19" type="stmt"/>
        <line num="332" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="336" count="1" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="353" count="76" type="cond" truecount="0" falsecount="1"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="358" count="115" type="stmt"/>
        <line num="359" count="76" type="cond" truecount="0" falsecount="1"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="365" count="76" type="stmt"/>
        <line num="366" count="76" type="cond" truecount="0" falsecount="1"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="372" count="76" type="stmt"/>
        <line num="373" count="76" type="cond" truecount="4" falsecount="1"/>
        <line num="375" count="39" type="stmt"/>
        <line num="376" count="39" type="stmt"/>
        <line num="378" count="4" type="stmt"/>
        <line num="379" count="4" type="stmt"/>
        <line num="381" count="31" type="stmt"/>
        <line num="382" count="31" type="stmt"/>
        <line num="384" count="2" type="stmt"/>
        <line num="385" count="2" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="392" count="76" type="cond" truecount="1" falsecount="0"/>
        <line num="394" count="76" type="stmt"/>
        <line num="402" count="76" type="cond" truecount="1" falsecount="0"/>
        <line num="413" count="76" type="stmt"/>
        <line num="425" count="39" type="cond" truecount="1" falsecount="0"/>
        <line num="426" count="37" type="stmt"/>
        <line num="430" count="6" type="stmt"/>
        <line num="431" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="432" count="0" type="stmt"/>
        <line num="437" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="438" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="439" count="0" type="stmt"/>
        <line num="442" count="2" type="stmt"/>
        <line num="450" count="39" type="cond" truecount="2" falsecount="1"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="462" count="39" type="stmt"/>
        <line num="463" count="39" type="cond" truecount="0" falsecount="1"/>
        <line num="464" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="469" count="39" type="stmt"/>
        <line num="470" count="39" type="stmt"/>
        <line num="473" count="39" type="cond" truecount="0" falsecount="1"/>
        <line num="476" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="483" count="39" type="stmt"/>
        <line num="484" count="39" type="stmt"/>
        <line num="487" count="39" type="stmt"/>
        <line num="490" count="39" type="stmt"/>
        <line num="491" count="39" type="stmt"/>
        <line num="493" count="39" type="stmt"/>
        <line num="496" count="39" type="stmt"/>
        <line num="508" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="512" count="4" type="stmt"/>
        <line num="515" count="11" type="cond" truecount="3" falsecount="0"/>
        <line num="516" count="2" type="stmt"/>
        <line num="519" count="9" type="cond" truecount="0" falsecount="1"/>
        <line num="524" count="0" type="stmt"/>
        <line num="526" count="9" type="stmt"/>
        <line num="534" count="4" type="cond" truecount="2" falsecount="1"/>
        <line num="535" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="546" count="4" type="stmt"/>
        <line num="547" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="548" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="553" count="4" type="stmt"/>
        <line num="556" count="4" type="stmt"/>
        <line num="557" count="4" type="cond" truecount="2" falsecount="1"/>
        <line num="558" count="0" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="561" count="4" type="stmt"/>
        <line num="564" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="574" count="4" type="stmt"/>
        <line num="575" count="4" type="stmt"/>
        <line num="578" count="4" type="stmt"/>
        <line num="579" count="5" type="stmt"/>
        <line num="581" count="4" type="stmt"/>
        <line num="583" count="4" type="cond" truecount="3" falsecount="1"/>
        <line num="587" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="588" count="4" type="stmt"/>
        <line num="589" count="4" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="601" count="4" type="stmt"/>
        <line num="602" count="4" type="stmt"/>
        <line num="609" count="31" type="stmt"/>
        <line num="610" count="31" type="stmt"/>
        <line num="611" count="31" type="stmt"/>
        <line num="612" count="31" type="stmt"/>
        <line num="613" count="31" type="stmt"/>
        <line num="614" count="31" type="stmt"/>
        <line num="625" count="31" type="stmt"/>
        <line num="626" count="31" type="stmt"/>
        <line num="629" count="31" type="stmt"/>
        <line num="630" count="31" type="stmt"/>
        <line num="631" count="31" type="stmt"/>
        <line num="636" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="646" count="31" type="cond" truecount="0" falsecount="1"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="655" count="31" type="stmt"/>
        <line num="656" count="31" type="stmt"/>
        <line num="657" count="31" type="stmt"/>
        <line num="662" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="672" count="31" type="cond" truecount="0" falsecount="1"/>
        <line num="676" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="682" count="31" type="stmt"/>
        <line num="683" count="31" type="stmt"/>
        <line num="684" count="31" type="stmt"/>
        <line num="686" count="31" type="stmt"/>
        <line num="689" count="31" type="stmt"/>
        <line num="690" count="31" type="stmt"/>
        <line num="704" count="31" type="cond" truecount="0" falsecount="1"/>
        <line num="705" count="0" type="stmt"/>
        <line num="706" count="0" type="stmt"/>
        <line num="710" count="31" type="stmt"/>
        <line num="714" count="31" type="stmt"/>
        <line num="717" count="31" type="cond" truecount="0" falsecount="1"/>
        <line num="718" count="0" type="stmt"/>
        <line num="722" count="31" type="stmt"/>
        <line num="723" count="31" type="cond" truecount="0" falsecount="1"/>
        <line num="724" count="0" type="stmt"/>
        <line num="730" count="31" type="stmt"/>
        <line num="737" count="2" type="stmt"/>
        <line num="738" count="2" type="stmt"/>
        <line num="746" count="2" type="stmt"/>
        <line num="747" count="2" type="stmt"/>
        <line num="750" count="2" type="stmt"/>
        <line num="753" count="2" type="stmt"/>
        <line num="754" count="2" type="stmt"/>
        <line num="755" count="2" type="stmt"/>
        <line num="760" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="762" count="0" type="stmt"/>
        <line num="765" count="0" type="stmt"/>
        <line num="767" count="0" type="stmt"/>
        <line num="768" count="0" type="stmt"/>
        <line num="772" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="776" count="0" type="stmt"/>
        <line num="777" count="0" type="stmt"/>
        <line num="780" count="2" type="stmt"/>
        <line num="781" count="2" type="stmt"/>
        <line num="783" count="2" type="stmt"/>
        <line num="791" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="792" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
        <line num="797" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="798" count="0" type="stmt"/>
        <line num="799" count="0" type="stmt"/>
        <line num="801" count="2" type="stmt"/>
        <line num="802" count="2" type="stmt"/>
        <line num="804" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="805" count="0" type="stmt"/>
        <line num="806" count="0" type="stmt"/>
        <line num="810" count="2" type="stmt"/>
        <line num="813" count="2" type="stmt"/>
        <line num="816" count="2" type="stmt"/>
        <line num="819" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="820" count="0" type="stmt"/>
        <line num="824" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="826" count="0" type="stmt"/>
        <line num="830" count="2" type="stmt"/>
        <line num="832" count="2" type="stmt"/>
        <line num="835" count="2" type="stmt"/>
        <line num="843" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="844" count="0" type="stmt"/>
        <line num="847" count="0" type="stmt"/>
        <line num="851" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="853" count="0" type="stmt"/>
        <line num="856" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="866" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="867" count="0" type="stmt"/>
        <line num="872" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="874" count="0" type="stmt"/>
        <line num="878" count="0" type="stmt"/>
        <line num="880" count="0" type="stmt"/>
        <line num="883" count="0" type="stmt"/>
        <line num="890" count="33" type="stmt"/>
        <line num="891" count="33" type="stmt"/>
        <line num="892" count="33" type="stmt"/>
        <line num="894" count="33" type="stmt"/>
        <line num="895" count="67" type="stmt"/>
        <line num="897" count="67" type="cond" truecount="2" falsecount="1"/>
        <line num="898" count="67" type="cond" truecount="2" falsecount="0"/>
        <line num="899" count="32" type="stmt"/>
        <line num="900" count="32" type="cond" truecount="1" falsecount="0"/>
        <line num="901" count="32" type="stmt"/>
        <line num="906" count="67" type="stmt"/>
        <line num="910" count="33" type="cond" truecount="0" falsecount="1"/>
        <line num="913" count="8" type="stmt"/>
        <line num="930" count="110" type="stmt"/>
        <line num="931" count="227" type="stmt"/>
        <line num="933" count="110" type="stmt"/>
        <line num="934" count="227" type="stmt"/>
        <line num="938" count="110" type="cond" truecount="3" falsecount="0"/>
        <line num="939" count="2" type="stmt"/>
        <line num="941" count="2" type="stmt"/>
        <line num="942" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="944" count="2" type="stmt"/>
        <line num="945" count="2" type="stmt"/>
        <line num="949" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="951" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="952" count="2" type="stmt"/>
        <line num="953" count="2" type="stmt"/>
        <line num="955" count="0" type="stmt"/>
        <line num="959" count="0" type="stmt"/>
        <line num="960" count="0" type="stmt"/>
        <line num="961" count="0" type="stmt"/>
        <line num="962" count="0" type="stmt"/>
        <line num="966" count="2" type="stmt"/>
        <line num="972" count="2" type="stmt"/>
        <line num="973" count="2" type="stmt"/>
        <line num="974" count="2" type="stmt"/>
        <line num="976" count="108" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
